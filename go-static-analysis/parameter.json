{"rules": [{"package": "icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient", "func": [{"name": "SendRequest", "index": 3, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "SendRequest", "index": 4, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "Post", "index": 2, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "Post", "index": 3, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "Put", "index": 2, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "Put", "index": 3, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "Get", "index": 2, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "Get", "index": 3, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "Head", "index": 2, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "Head", "index": 3, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "Delete", "index": 2, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "Delete", "index": 3, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "MultiGet", "index": 2, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "MultiGet", "index": 3, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "MultiPost", "index": 2, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "MultiPost", "index": 3, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "NewHTTPRequest", "index": 0, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "NewHTTPRequest", "index": 1, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}]}, {"package": "icode.baidu.com/baidu/netdisk/pcs-go-lib/ufc", "func": [{"name": "NewUfcRequest", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "NewUfcRequest", "index": 8, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "NewUFCClient", "index": 0, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "NewUFCClient", "index": 1, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}]}, {"package": "icode.baidu.com/baidu/netdisk/nd-golib/src/pcs/ufc", "func": [{"name": "NewUfcRequest", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "NewUfcRequest", "index": 8, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}, {"name": "NewUFCClient", "index": 0, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "NewUFCClient", "index": 1, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}]}, {"package": "icode.baidu.com/baidu/netdisk/clouddisk-golib/ufc", "func": [{"name": "NewUfcRequest", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "NewUfcRequest", "index": 8, "greater": 0, "less": 1800000, "param": "ReadWriteTimeoutMs"}]}, {"package": "(*icode.baidu.com/baidu/netdisk/clouddisk-golib/utils.UfcHelper)", "func": [{"name": "CurlProxyByUfc", "index": 8, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfc", "index": 9, "greater": 0, "less": 1800000, "param": "requestTimeout"}, {"name": "CurlProxyByUfcAll", "index": 8, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfcAll", "index": 9, "greater": 0, "less": 1800000, "param": "requestTimeout"}]}, {"package": "(*icode.baidu.com/baidu/netdisk/clouddisk-golib/pcs.PcsService)", "func": [{"name": "CurlProxyByUfc", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfc", "index": 8, "greater": 0, "less": 1800000, "param": "requestTimeout"}, {"name": "CurlProxyByUfcWithoutUI", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfcWithoutUI", "index": 8, "greater": 0, "less": 1800000, "param": "requestTimeout"}, {"name": "CurlProxyByUfcWithRetryForLite", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfcWithRetryForLite", "index": 8, "greater": 0, "less": 1800000, "param": "requestTimeout"}]}, {"package": "icode.baidu.com/baidu/netdisk/clouddisk-golib/pcs.PcsService", "func": [{"name": "CurlProxyByUfc", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfc", "index": 8, "greater": 0, "less": 1800000, "param": "requestTimeout"}, {"name": "CurlProxyByUfcWithoutUI", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfcWithoutUI", "index": 8, "greater": 0, "less": 1800000, "param": "requestTimeout"}, {"name": "CurlProxyByUfcWithRetryForLite", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfcWithRetryForLite", "index": 8, "greater": 0, "less": 1800000, "param": "requestTimeout"}]}, {"package": "(*icode.baidu.com/baidu/netdisk/nd-point/src/points/models.SigninEventModel)", "func": [{"name": "CurlProxyByUfc", "index": 8, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfc", "index": 9, "greater": 0, "less": 1800000, "param": "requestTimeout"}, {"name": "CurlProxyByUfcWithoutUI", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfcWithoutUI", "index": 8, "greater": 0, "less": 1800000, "param": "requestTimeout"}, {"name": "CurlProxyByUfcWithRetryForLite", "index": 7, "greater": 0, "less": 600000, "param": "ConnectTimeoutMs"}, {"name": "CurlProxyByUfcWithRetryForLite", "index": 8, "greater": 0, "less": 1800000, "param": "requestTimeout"}]}]}