.PHONY:: all build pack upload clean test

all: prepare build package-bin

HOMEDIR ?= $(shell pwd)
RELEASE := release
NAME := go-static-analysis
FILECENTER_TOKEN = f600f45a30a611ebb58398039b75f290
GO      := $(GOROOT)/bin/go
GOPATH  := $(shell $(GO) env GOPATH)
GOMOD   := $(GO) mod
OUTDIR  := $(HOMEDIR)/output

#GOOS=linux GOARCH=amd64
define build-it =
GOOS=linux GOARCH=amd64 go build -o $(name) \
	-trimpath \
	cmd/$(subst -,/,$(name))/main.go
endef

define build-it-cgoclose =
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o $(name) \
	-trimpath \
	cmd/$(subst -,/,$(name))/main.go
endef

define build-it-easy =
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o $(name) \
	-trimpath \
	cmd/easy/$(subst -,/,$(name))/main.go
endef

define clean-it =
rm -rf $(name)
endef

define test-it =
(cd passes/$(subst -,/,$(name)) && go test -v)
endef

prepare: gomod
gomod: set-env
	$(GOMOD) download


# set proxy env
set-env:
	$(GO) env -w GO111MODULE=on
	$(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com
	$(GO) env -w GONOSUMDB=\*


# {{{ printast

.PHONY build:: build-printast
build-printast: name=printast
build-printast:
	$(build-it-cgoclose)

.PHONY clean:: clean-printast
clean-printast: name=printast
clean-printast:
	$(clean-it)

# }}} printast

# {{{ printssa

.PHONY build:: build-printssa
build-printssa: name=printssa
build-printssa:
	$(build-it-cgoclose)

.PHONY clean:: clean-printssa
clean-printssa: name=printssa
clean-printssa:
	$(clean-it)

# }}} printssa

# {{{ followedby

.PHONY build:: build-followedby
build-followedby: name=followedby
build-followedby:
	$(build-it-cgoclose)

.PHONY clean:: clean-followedby
clean-followedby: name=followedby
clean-followedby:
	$(clean-it)

.PHONY test:: test-followedby
test-followedby: name=followedby
test-followedby:
	$(test-it)

# }}} followedby

# {{{ unfreed

.PHONY build:: build-unfreed
build-unfreed: name=unfreed
build-unfreed:
	$(build-it-cgoclose)

.PHONY clean:: clean-unfreed
clean-unfreed: name=unfreed
clean-unfreed:
	$(clean-it)

.PHONY test:: test-unfreed
test-unfreed: name=unfreed
test-unfreed:
	$(test-it)

# }}} unfreed

# {{{ nocomments

.PHONY build:: build-nocomments
build-nocomments: name=nocomments
build-nocomments:
	$(build-it-cgoclose)

.PHONY clean:: clean-nocomments
clean-nocomments: name=nocomments
clean-nocomments:
	$(clean-it)

.PHONY test:: test-nocomments
test-nocomments: name=nocomments
test-nocomments:
	$(test-it)

# }}} nocomments

# {{{ reverse

.PHONY build:: build-reverse
build-reverse: name=reverse
build-reverse:
	$(build-it-cgoclose)

.PHONY clean:: clean-reverse
clean-reverse: name=reverse
clean-reverse:
	$(clean-it)

.PHONY test:: test-reverse
test-reverse: name=reverse
test-reverse:
	$(test-it)

# }}} reverse

# {{{ assignstmt

.PHONY build:: build-assignstmt
build-assignstmt: name=assignstmt
build-assignstmt:
	$(build-it-cgoclose)

.PHONY clean:: clean-assignstmt
clean-assignstmt: name=assignstmt
clean-assignstmt:
	$(clean-it)

# }}} assignstmt

# {{{ ifnil

.PHONY build:: build-ifnil
build-ifnil: name=ifnil
build-ifnil:
	$(build-it-cgoclose)

.PHONY test:: test-ifnil
test-ifnil: name=ifnil
test-ifnil:
	$(test-it)

.PHONY clean:: clean-ifnil
clean-ifnil: name=ifnil
clean-ifnil:
	$(clean-it)

# }}} ifnil

# {{{ pre

.PHONY build:: build-pre
build-pre: name=pre
build-pre:
	$(build-it-cgoclose)

.PHONY clean:: clean-pre
clean-pre: name=pre
clean-pre:
	$(clean-it)

# }}} pre

# {{{ parameter

.PHONY build:: build-parameter
build-parameter: name=parameter
build-parameter:
	$(build-it-cgoclose)

.PHONY clean:: clean-parameter
clean-parameter: name=parameter
clean-parameter:
	$(clean-it)

# }}} parameter

# {{{ nilerr

.PHONY build:: build-nilerr
build-nilerr: name=nilerr
build-nilerr:
	$(build-it-cgoclose)

.PHONY clean:: clean-nilerr
clean-nilerr: name=nilerr
clean-nilerr:
	$(clean-it)

# }}} nilerr

# {{{ report

.PHONY build:: build-report
build-report: name=report
build-report:
	$(build-it-cgoclose)

.PHONY clean:: clean-report
clean-report: name=report
clean-report:
	$(clean-it)

# {{{ checkdiff

.PHONY build:: build-checkdiff
build-checkdiff: name=checkdiff
build-checkdiff:
	$(build-it-easy)

.PHONY clean:: clean-checkdiff
clean-checkdiff: name=checkdiff
clean-checkdiff:
	$(clean-it)

.PHONY test:: test-checkdiff
test-checkdiff: name=checkdiff
test-checkdiff:
	$(test-it)

# }}} checkdiff	

# {{{ checkerr

.PHONY build:: build-checkerr
build-checkerr: name=checkerr
build-checkerr:
	$(build-it-easy)

.PHONY clean:: clean-checkerr
clean-checkerr: name=checkerr
clean-checkerr:
	$(clean-it)

.PHONY test:: test-checkerr
test-checkerr: name=checkerr
test-checkerr:
	$(test-it)

# }}} checkerr



# {{{ checkmain

.PHONY build:: build-checkmain
build-checkmain: name=checkmain
build-checkmain:
	$(build-it-easy)

.PHONY clean:: clean-checkmain
clean-checkmain: name=checkmain
clean-checkmain:
	$(clean-it)

.PHONY test:: test-checkmain
test-checkmain: name=checkmain
test-checkmain:
	$(test-it)

# }}} checkmain

# {{{ checkparameters

.PHONY build:: build-checkparameters
build-checkparameters: name=checkparameters
build-checkparameters:
	$(build-it-easy)

.PHONY clean:: clean-checkparameters
clean-checkparameters: name=checkparameters
clean-checkparameters:
	$(clean-it)

.PHONY test:: test-checkparameters
test-checkparameters: name=checkparameters
test-checkparameters:
	$(test-it)

# }}} checkparameters

# {{{ checknilaway

.PHONY build:: build-nilaway
build-nilaway: name=nilaway
build-nilaway:
	$(build-it-cgoclose)

.PHONY clean:: clean-nilaway
clean-nilaway: name=nilaway
clean-nilaway:
	$(clean-it)

.PHONY test:: test-nilaway
test-nilaway: name=nilaway
test-nilaway:
	$(test-it)

# }}} checknilaway



# {{{ checksqlscan

.PHONY build:: build-sqlscan
build-sqlscan: name=sqlscan
build-sqlscan:
	$(build-it-cgoclose)

.PHONY clean:: clean-sqlscan
clean-sqlscan: name=sqlscan
clean-sqlscan:
	$(clean-it)

.PHONY test:: test-checksqlscan
test-checksqlscan: name=checksqlscan
test-checksqlscan:
	$(test-it)

# }}} checksqlscan


# {{{ checkcomment

.PHONY build:: build-checkcomment
build-checkcomment: name=checkcomment
build-checkcomment:
	$(build-it-easy)

.PHONY clean:: clean-checkcomment
clean-checkcomment: name=checkcomment
clean-checkcomment:
	$(clean-it)

.PHONY test:: test-checkcomment
test-checkcomment: name=checkcomment
test-checkcomment:
	$(test-it)

# }}} checkcomment

# {{{ recover

.PHONY build:: build-recover
build-recover: name=recover
build-recover:
	$(build-it-cgoclose)

.PHONY clean:: clean-recover
clean-recover: name=recover
clean-recover:
	$(clean-it)

.PHONY test:: test-recover
test-recover: name=recover
test-recover:
	$(test-it)

# }}} recover

# {{{ golibcheck

.PHONY build:: build-golibcheck
build-golibcheck: name=golibcheck
build-golibcheck:
	$(build-it-easy)

.PHONY clean:: clean-golibcheck
clean-golibcheck: name=golibcheck
clean-golibcheck:
	$(clean-it)

.PHONY test:: test-golibcheck
test-golibcheck: name=golibcheck
test-golibcheck:
	$(test-it)

# }}} golibcheck

# {{{ naccheck

.PHONY build:: build-naccheck
build-naccheck: name=naccheck
build-naccheck:
	$(build-it-easy)

.PHONY clean:: clean-naccheck
clean-naccheck: name=naccheck
clean-naccheck:
	$(clean-it)

.PHONY test:: test-naccheck
test-naccheck: name=naccheck
test-naccheck:
	$(test-it)

# }}} naccheck

# {{{ portcheck

.PHONY build:: build-portcheck
build-portcheck: name=portcheck
build-portcheck:
	$(build-it-easy)

.PHONY clean:: clean-portcheck
clean-portcheck: name=portcheck
clean-portcheck:
	$(clean-it)

.PHONY test:: test-portcheck
test-portcheck: name=portcheck
test-portcheck:
	$(test-it)

# }}} portcheck

# {{{ retry

.PHONY build:: build-retry
build-retry: name=retry
build-retry:
	$(build-it-easy)

.PHONY clean:: clean-retry
clean-retry: name=retry
clean-retry:
	$(clean-it)

.PHONY test:: test-retry
test-retry: name=retry
test-retry:
	$(test-it)

# }}} retry

# {{{ codemetric

.PHONY build:: build-codemetric
build-codemetric: name=codemetric
build-codemetric:
	$(build-it-easy)

.PHONY clean:: clean-codemetric
clean-codemetric: name=codemetric
clean-codemetric:
	$(clean-it)

.PHONY test:: test-codemetric
test-codemetric: name=codemetric
test-codemetric:
	$(test-it)

# }}} codemetric

# {{{ slogcheck

.PHONY build:: build-slogcheck
build-slogcheck: name=slogcheck
build-slogcheck:
	$(build-it-easy)

.PHONY clean:: clean-slogcheck
clean-slogcheck: name=slogcheck
clean-slogcheck:
	$(clean-it)

.PHONY test:: test-slogcheck
test-slogcheck: name=slogcheck
test-slogcheck:
	$(test-it)

# }}} slogcheck

# {{{ easylogger

.PHONY build:: build-easylogger
build-easylogger: name=easylogger
build-easylogger:
	$(build-it-easy)

.PHONY clean:: clean-easylogger
clean-easylogger: name=easylogger
clean-easylogger:
	$(clean-it)

.PHONY test:: test-easylogger
test-easylogger: name=easylogger
test-easylogger:
	$(test-it)

# }}} easylogger

# {{{ apicollection
.PHONY build:: build-apicollection
build-apicollection: name=apicollection
build-apicollection:
	$(build-it-easy)

.PHONY clean:: clean-apicollection
clean-apicollection: name=apicollection
clean-apicollection:
	$(clean-it)

.PHONY test:: test-apicollection
test-apicollection: name=apicollection
test-apicollection:
	$(test-it)
# }}} apicollection

# {{{ apicollection
.PHONY build:: build-callgraph
build-callgraph: name=callgraph
build-callgraph:
	$(build-it-easy)

.PHONY clean:: clean-callgraph
clean-callgraph: name=callgraph
clean-callgraph:
	$(clean-it)

.PHONY test:: test-callgraph
test-callgraph: name=callgraph
test-callgraph:
	$(test-it)
# }}} apicollection

# {{{ easyfn
.PHONY build:: build-easyfn
build-easyfn: name=easyfn
build-easyfn:
	$(build-it-easy)

.PHONY clean:: clean-easyfn
clean-easyfn: name=easyfn
clean-easyfn:
	$(clean-it)

.PHONY test:: test-easyfn
test-easyfn: name=easyfn
test-easyfn:
	$(test-it)
# }}} easyfn


.PHONY:: upload-report
upload-report:
	curl -H "accessToken:$(FILECENTER_TOKEN)" \
		-F file=@report \
		-F filename=report \
		-F group=$(NAME) \
		http://filecenter.matrix.baidu.com/api/v1/files

# }}} report

.PHONY:: upload-nilerr
upload-nilerr:
	curl -H "accessToken:$(FILECENTER_TOKEN)" \
		-F file=@nilerr \
		-F filename=nilerr \
		-F group=$(NAME) \
		http://filecenter.matrix.baidu.com/api/v1/files

pack:
	rm -rf $(RELEASE)/$(NAME)
	mkdir -p $(RELEASE)/$(NAME)
	cp pre followedby followedby.json unfreed unfreed.json parameter parameter.json $(RELEASE)/$(NAME)
	cp nocomments nocomments.json reverse ifnil nilaway sqlscan $(RELEASE)/$(NAME)
	cp retry portcheck naccheck golibcheck checkmain checkerr checkdiff codemetric slogcheck easylogger apicollection $(RELEASE)/$(NAME)
	cp callgraph easyfn $(RELEASE)/$(NAME)
	cp checkparameters checkcomment recover $(RELEASE)/$(NAME)
	cd $(RELEASE)/$(NAME) && tar czf ../$(NAME).tar.gz *

package-bin:
	mkdir -p $(OUTDIR)
	mkdir -p $(RELEASE)/$(NAME)
	cp pre followedby followedby.json unfreed unfreed.json parameter parameter.json $(RELEASE)/$(NAME)
	cp nocomments nocomments.json reverse ifnil nilaway sqlscan $(RELEASE)/$(NAME)
	cp retry portcheck naccheck golibcheck checkmain checkerr checkdiff codemetric slogcheck easylogger apicollection $(RELEASE)/$(NAME)
	cp callgraph easyfn $(RELEASE)/$(NAME)
	cp checkparameters checkcomment recover $(RELEASE)/$(NAME)
	cp -rf $(RELEASE)/$(NAME) $(OUTDIR)/


upload:
	curl -H "accessToken:$(FILECENTER_TOKEN)" \
		-F file=@$(RELEASE)/$(NAME).tar.gz \
		-F filename=$(NAME).tar.gz \
		-F group=$(NAME) \
		http://filecenter.matrix.baidu.com/api/v1/files
