package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"runtime/debug"
	"strings"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/report"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

const (
	// dirName下除了要放自研的tar.gz, 还要放errcheck规则供调用
	dirName = "/home/<USER>/gocheck/analysis/baidu/netdisk/go-static-analysis" // 正式路径, 用于存放线上所需的代码库
	// dirName               = "/home/<USER>/gocheck/test" // 测试路径
	tarAddr               = dirName + "/release" + "/go-static-analysis.tar.gz"
	dirNameForRegularMode = "/home/<USER>/gocheck/test" // 用于存放regular mode所需的errcheck规则
)

var (
	easyEnabledDiff  = flag.Bool("easy-enable-diff", true, "是否开启easy增量检测，如果开启，内容将嵌入到html给流水线，如果全量则只把结果落库")
	regularMode      = flag.Bool("regular", false, "此处运行是否开启定期跑模式, 该模式目前增加了errcheck的规则")
	singleModeFlag   = flag.Bool("single", false, "是否开启单规则模式，如果开启，则只检测当前目录下的文件")
	singleModeGlobal bool

	ruleNameFlag   = flag.String("rulename", "", "单规则模式下，指定的规则名称")
	ruleNameGlobal string
)

func main() {
	flag.Parse()
	checkAndSetFlag()

	status := 0
	defer func() {
		if x := recover(); x != nil {
			log.Printf("%v\n%s\n", x, string(debug.Stack()))
			status = -1
		}
		os.Exit(status)
	}()

	ok := preDo()
	if !ok {
		status = -1
		return
	}

	// 非go module格式的代码库，不进行检测
	output, err := utils.ExecScript(report.ScriptCheckGoMod)
	if err != nil || strings.TrimSpace(output) == "N" {
		r := &report.Recorder{}
		r.Summary = "解析代码库失败"
		r.Output = "不支持GOPATH格式代码库，请尽快调整到Module格式"
		r.GenHTML()
		return
	}

	// 代码检测 & 代码度量
	runConfig := report.NewRunConfig(*easyEnabledDiff, *regularMode, tarAddr, dirNameForRegularMode, singleModeGlobal, ruleNameGlobal)
	r := report.Run(runConfig)
	// 如果为定期跑模式且不为单文件模式，不用走后续逻辑
	if runConfig.RegularMode || singleModeGlobal {
		return
	}

	// 用于展示easy规则的增量拦截，已有拦截过滤掉, 如果 r.Err 或者 r.EasyErr 存在一个不为空则不展示
	if r.EasyErr != nil {
		r.Err = r.EasyErr
	}
	r.SAOutput = r.EasyOutput

	if r.Err != nil {
		status = -1
		sendHiMsg := fmt.Sprintf("reoprt run failed. \nr.err is: %v\nr.easyErr%v", r.Err, r.EasyErr)
		utils.SendWarnMessageToHiForDailyMessage(sendHiMsg)
	} else if len(r.SAOutput) > 0 {
		status = 1
	}

	r.GenHTML()
}

// 该函数执行需要的前置工作
// 1: 把代码下载到dirName目录
// 2: 执行gmake 和 gmake pack
// 该函数用于检测目标目录和目标压缩包是否存在
func preDo() bool {
	ok := utils.IsDir(dirName)
	if !ok {
		utils.SendWarnMessageToHiForDailyMessage("[静态分析规则] : " + dirName + ", 该目录不存在")
		return false
	}

	file, err := os.Open(tarAddr)
	if err != nil {
		utils.SendWarnMessageToHiForDailyMessage("[静态分析规则] : tar.gz压缩包不存在")
		return false
	}
	defer file.Close()

	return true
}

func checkAndSetFlag() bool {
	if singleModeFlag == nil || ruleNameFlag == nil {
		return true
	}
	singleModeGlobal = *singleModeFlag
	ruleNameGlobal = *ruleNameFlag
	if !singleModeGlobal && ruleNameGlobal == "" {
		return true
	}
	if singleModeGlobal && ruleNameGlobal != "" {
		return true
	}

	return false
}
