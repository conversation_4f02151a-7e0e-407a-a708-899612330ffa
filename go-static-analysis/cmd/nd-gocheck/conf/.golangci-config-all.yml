
output:
  format:  colored-line-number
  print-issued-lines: true
linters:
  # enable-all: true
  # disable:
  #   - deadcode
  disable-all: true
  enable:
    - staticcheck
    - errcheck
    - govet
linters-settings:
  staticcheck:
    checks:
      - 'SA9001'
      - 'SA2001'
      - 'SA4004'
      - 'SA4006'
      - 'SA5000'
      - 'SA4010'
      - 'SA5011'
      - 'SA5002'
      - 'SA6000'
      - 'SA5003'
      - 'SA5004'
  errcheck:
    check-type-assertions: true
    check-blank: true
    ignore: fmt:.*,io/ioutil:^Read.*
    disable-default-exclusions: true
    exclude-functions:
      - io/ioutil.ReadFile
      - io.Copy(*bytes.Buffer)
      - io.Copy(os.Stdout)
  govet:
    # Report about shadowed variables.
    # Default: false
    check-shadowing: true
    settings:
      shadow:
        strict: true
    disable-all: true
    enable:
      - asmdecl
      - atomic
      - bools
      - buildtag
      - composites
      - lostcancel
      - stdmethods
      - tests
      - unsafeptr
      - assign
      - unusedresult
      - cgocall
      - copylocks
      - httpresponse
      - nilfunc
      - printf
      - shadow
      - shift
      - structtag