package main

import (
	"fmt"
	"os"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/cmd/commands"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

func init() {
	utils.HomeCache = os.Getenv("HOME")
}

func main() {
	e := commands.NewExecutor()
	if err := e.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "failed executing command with error %v\n", err)
		os.Exit(1)
	}
}
