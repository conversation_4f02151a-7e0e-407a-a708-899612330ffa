package commands

import (
	"fmt"
	"os"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/fatih/color"
	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

var isOldVersion bool

func init() {
	isOldVersion = isGoOldVersion()
}

func (e *Executor) initRun() {
	e.runCmd = &cobra.Command{
		Use:     "run",
		Short:   "run all linters",
		PreRun:  e.executePre,
		Run:     e.executeRun,
		PostRun: e.executePost,
	}
	e.rootCmd.AddCommand(e.runCmd)
}

func (e *Executor) initRunAll() {
	e.runAllCmd = &cobra.Command{
		Use:     "all",
		Short:   "run all linters",
		PreRun:  e.executePre,
		Run:     e.executeRun,
		PostRun: e.executePost,
	}
	e.runCmd.AddCommand(e.runAllCmd)
}

func (e *Executor) executeRun(cmd *cobra.Command, args []string) {
	// 执行所有规则

	separator := "\n#######################################################################################"

	fmt.Fprintln(&utils.WriteBuffer, separator)
	e.RunBugbye(cmd, args)
	fmt.Fprintln(&utils.WriteBuffer, separator)

	e.RunCodeRule(cmd, args)
	fmt.Fprintln(&utils.WriteBuffer, separator)

	e.SetCustomRuleIncre(false)
	e.RunCustom(cmd, args)

	e.SetCustomRuleIncre(true)
	e.RunCustom(cmd, args)
	fmt.Fprintln(&utils.WriteBuffer, separator)
}

func (e *Executor) executePre(_ *cobra.Command, args []string) {
	msg := "请将鼠标移动至下方检测结果左侧路径, 按 command 键 + 鼠标左键跳转到问题代码处, 查看相应的问题。" +
		"另外，计算组提供的其他工具文档见: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/SX81HQH0s5/Fta9cexjYxzWXU"
	s := color.BlackString(msg)
	fmt.Fprintln(&utils.WriteBuffer, s)

	// prefixOutput
	prefixOutput := color.BlueString("the nd-gocheck tool is running...")
	fmt.Println(prefixOutput)
	color.BlueString("请在代码commit前执行,如果代码已commit, 请加上--commit, 如: nd-gocheck run all --commit")

	utils.TimeStart = time.Now()

	UpdateOtherBinaryOrNot()
	UpdateConfigOrNot()
}

func (e *Executor) executePost(_ *cobra.Command, args []string) {
	utils.SendStatData(utils.CustomRuleResult)
	msg := "请将鼠标移动至上方检测结果左侧路径, 按 command 键 + 鼠标左键跳转到问题代码处, 查看相应的问题。" +
		"另外，计算组提供的其他工具文档见: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/SX81HQH0s5/Fta9cexjYxzWXU"

	s := color.BlackString(msg)
	fmt.Fprintln(&utils.WriteBuffer, s)
	_, err := utils.WriteBuffer.WriteTo(os.Stderr)
	if err != nil {
		fmt.Fprintln(os.Stderr, utils.WriteBuffer.String())
	}

	if utils.NeedSendHi {
		utils.SendWarnMessageToHi(utils.SendHiMsg)
	}

	// 更新自身
	UpdateBinCommandOrNot()

	// 更新使用时间
	updateUseTime()

	// 时间统计
	elapse := time.Since(utils.TimeStart).Seconds()
	elapseWithColour := color.GreenString("%v", elapse)
	msgWithColor := color.BlackString("规则执行耗时: ")
	fmt.Printf("%s%ss\n", msgWithColor, elapseWithColour)

}

func UpdateOtherBinaryOrNot() {
	// 二进制文件更新策略
	if LastUseTimeIsToday() {
		return
	}

	_, err := utils.ExecCommandWithRetry(utils.CustomRuleBinInstallCmd)
	if err != nil {
		fmt.Println("[warn] update failed. cmd: ", utils.CustomRuleBinInstallCmd, ", err: ", err)
	}
}

// 配置文件以及自身二进制文件更新
func UpdateConfigOrNot() {
	if LastUseTimeIsToday() {
		return
	}

	updateConfig()
}

// 更新自身二进制文件
func UpdateBinCommandOrNot() {
	if LastUseTimeIsToday() {
		return
	}

	output, err := utils.ExecCommandWithRetry(utils.NdGoCheckInstallCmd)
	if err != nil {
		fmt.Println("[warn] update failed. cmd: ", utils.NdGoCheckInstallCmd, ", err: ", err, ", output: ", output)
	}
}

func updateConfig() {
	// 备份配置文件
	_, err := utils.ExecCommand(utils.PreUpdateConfigCmd)
	if err != nil {
		// 当原有文件不存在时会抛出错误，不影响以下逻辑，该操作为了预防 wget 下来的新文件被重命名
	}

	// bugbye 配置下载
	_, err = utils.ExecCommandWithRetry(utils.BugbyeConfigCmd)
	if err != nil {
		// 配置下载出错以及利用老配置出错，此时抛出错误即可
		output, e := utils.ExecCommand(utils.RecoverBugbyeCmd)
		if e != nil {
			utils.NeedSendHi = true
			utils.SendHiMsg = "updateConfig failed"
			fmt.Printf("update config file failed and recovery config file failed. filename: .golangci-config-all.yml, output: %s\n", output)
			return
		}
	}

	// coderule 配置下载
	_, err = utils.ExecCommandWithRetry(utils.CodeRuleConfigCmd)
	if err != nil {
		output, e := utils.ExecCommand(utils.RecoverCodeRuleCmd)
		if e != nil {
			// 配置下载出错以及利用老配置出错，此时抛出错误即可
			utils.NeedSendHi = true
			utils.SendHiMsg = "updateConfig failed"
			fmt.Printf("update config file failed. filename: .golangci-config-diff.yml, output: %s\n", output)
			return
		}
	}
}

// 返回false，意味着需要进一步操作: 配置文件需要直接更新 & 二进制文件需要匹配版本号再决定是否更新。返回 true, 则不需要更新
func LastUseTimeIsToday() bool {
	byt, err := os.ReadFile(utils.UseTimeRecordFileName)
	if err != nil {
		return false
	}

	lastUseTime, err := strconv.Atoi(string(byt))
	if err != nil {
		return false
	}

	// 上一次使用时间如果是前 1 天则进行更新
	now := time.Now()
	t := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	if t.Unix() > int64(lastUseTime) {
		return false
	}

	return true
}

func updateUseTime() {
	fd, err := os.OpenFile(utils.UseTimeRecordFileName, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0666)
	if err != nil {
		return
	}
	defer fd.Close()

	fmt.Fprint(fd, time.Now().Unix())
}

// less then 1.24
func isGoOldVersion() bool {
	// 获取 Go 版本，如 "go1.20.5"
	versionStr := runtime.Version()

	// 去掉前缀 "go"
	if strings.HasPrefix(versionStr, "go") {
		versionStr = versionStr[2:]
	}

	// 分割主版本和次版本
	parts := strings.Split(versionStr, ".")
	if len(parts) < 2 {
		return false
	}

	major, err1 := strconv.Atoi(parts[0])
	minor, err2 := strconv.Atoi(parts[1])
	if err1 != nil || err2 != nil {
		return false
	}

	// 比较版本是否小于 1.24
	if major < 1 || (major == 1 && minor < 24) {
		return false
	}

	return true

}

/*""



 */
