package commands

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/printer"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/reader"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/result"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

// run code rule
func (e *Executor) initRunCodeRule() {
	e.codeRuleCmd = &cobra.Command{
		Use:     "coderule",
		Aliases: []string{"cr"},
		Short:   "run the code rule linters",
		PreRun:  e.executePre,
		Run:     e.RunCodeRule,
		PostRun: e.executePost,
	}
	e.runCmd.AddCommand(e.codeRuleCmd)
}

func (e *Executor) RunCodeRule(_ *cobra.Command, args []string) {
	if err := e.CodeRuleCheckValid(); err != nil {
		utils.NeedSendHi = true
		utils.SendHiMsg = "coderule check valid failed"
		fmt.Fprintln(&utils.WriteBuffer, "code rule check valid failed, err: ", err)
		return
	}

	// 检查 report.json 和 result.json 是否存在，存在则进行备份
	if err := e.CheckReportAndResultExistAndSetForRule(utils.CodeRuleName); err != nil {
		utils.NeedSendHi = true
		utils.SendHiMsg = "CodeRule checkReportAndResultExistAndSet failed"
		return
	}

	newFromRev := "HEAD"
	if utils.IsCommit {
		newFromRev = "HEAD~"
	}

	var cmd string
	if e.IsEasyContainer {
		cmd = fmt.Sprintf(`%s run -c=%s --new-from-rev=%s --out-format=json:%s  --max-issues-per-linter=100 --max-same-issues=30`,
			utils.EEGolangCINameForEasy, utils.DefaultDiffConfigFile, newFromRev, utils.ReportJSONName)
	} else {
		cmd = fmt.Sprintf("%s run -c=%s --new-from-rev=%s --out-format=json:%s  --max-issues-per-linter=100 --max-same-issues=30",
			utils.EEGolangCINameForOtherMachine, utils.DefaultDiffConfigFile, newFromRev, utils.ReportJSONName)
	}
	_, err := utils.ExecCommand(cmd)
	if err != nil {
		// 不作处理，这是因为 golangci-lint 检测出结果时，退出码即不为0，此时会报错
	}

	if err := e.PrintResultJSON(utils.CodeRuleName); err != nil {
		utils.NeedSendHi = true
		utils.SendHiMsg = "CodeRule PrintResultJSON failed\n"
		fmt.Fprintln(&utils.WriteBuffer, "print code rule result err: ", err.Error())
	}

	e.CleanAndMayRenameForGoStyleCheck(utils.CodeRuleName)
}

// 打印result.json
func (e *Executor) PrintResultJSON(codeRuleName string) error {
	codeRuleData, err := reader.GetCodeRuleData(utils.ResultJSONName)
	if err != nil {
		return err
	}

	issue, err := reader.CodeRuleData2Issues(codeRuleData)
	if err != nil {
		return err
	}

	textPrinter := printer.NewPrinter(codeRuleName, &utils.WriteBuffer)
	textPrinter.PrintIssues(issue)
	return nil
}

func (e *Executor) getIssuesFromResultJSON(resultJSONFileName string) ([]result.Issue, error) {
	codeRuleData, err := reader.GetCodeRuleData(resultJSONFileName)
	if err != nil {
		return nil, err
	}

	issues, err := reader.CodeRuleData2Issues(codeRuleData)
	if err != nil {
		return nil, err
	}

	return issues, nil
}

func (e *Executor) CodeRuleCheckValid() error {
	if err := utils.CheckFileExist(utils.DefaultDiffConfigFile); err != nil {
		return err
	}

	if e.IsEasyContainer {
		if ok := utils.CheckGoBinExist(utils.EEGolangCINameForEasy); !ok {
			return fmt.Errorf(utils.EEGolangCINameForEasy + " is not installed for easy container")
		}
	} else {
		if ok := utils.CheckGoBinExist(utils.EEGolangCINameForOtherMachine); !ok {
			return fmt.Errorf(utils.EEGolangCINameForOtherMachine + " is not installed for other machine")
		}
	}

	return nil
}

// 检查文件是否存在以及是否需要做备份
func (e *Executor) CheckReportAndResultExistAndSetForRule(ruleName string) error {

	// 检查 report.json 是否存在（对于go-stylecheck需要做该操作）
	if err := utils.CheckFileExist(utils.ReportJSONName); err == nil {
		if e := os.Rename(utils.ReportJSONName, utils.ReportJSONBackupName); e != nil {
			fmt.Fprintf(&utils.WriteBuffer, "os rename failed. oldName: %s, newName: %s, err: %s\n", utils.ReportJSONName, utils.ReportJSONBackupName, e.Error())
			return e
		}

		if ruleName == utils.BugbyeRuleName {
			utils.ReportFileIsExistForBugbye = true
		} else if ruleName == utils.CodeRuleName {
			utils.ReportFileIsExistForCodeRule = true
		}
	}

	// 检查 result.json 是否存在
	if err := utils.CheckFileExist(utils.ResultJSONName); err == nil {
		if e := os.Rename(utils.ResultJSONName, utils.ResultJSONBackupName); e != nil {
			fmt.Fprintf(&utils.WriteBuffer, "os nename failed. oldName: %s, newName: %s,  err: %s\n", utils.ResultJSONName, utils.ResultJSONName, e.Error())
			return e
		}

		if ruleName == utils.BugbyeRuleName {
			utils.ResultFileIsExistForBugbye = true
		} else if ruleName == utils.CodeRuleName {
			utils.ResultFileIsExistForCodeRule = true
		} else if ruleName == utils.CustomRuleName {
			utils.ResultFileIsExistForCustom = true
		}
	}

	return nil
}

// bugbye 和 代码规范都用 go-stylecheck，都会生成 result.json 和 report.json。这里根据程序集合名称来判断是否清理
func (e *Executor) CleanAndMayRenameForGoStyleCheck(ruleName string) {
	if err := utils.CheckFileExist(utils.ReportJSONName); err == nil {
		if err := os.Remove(utils.ReportJSONName); err != nil {
			utils.NeedSendHi = true
			utils.SendHiMsg = "CodeRuleCleanAndMayReName failed"
			fmt.Fprintln(&utils.WriteBuffer, "code rule clean failed, filename: ", utils.ReportJSONName, "err: ", err)
			// 删除 report.json 失败不影响后续的result.json Remove操作
		}
	}

	if err := utils.CheckFileExist(utils.ResultJSONName); err == nil {
		if err := os.Remove(utils.ResultJSONName); err != nil {
			utils.NeedSendHi = true
			utils.SendHiMsg = "CodeRuleCleanAndMayReName failed"
			fmt.Fprintln(&utils.WriteBuffer, "code rule clean failed, filename: ", utils.ResultJSONName, "err: ", err)
			// remove失败不影响后续的重命名操作
		}
	}

	// 在执行 bugbye 时候是否需要进行恢复
	if ruleName == utils.BugbyeRuleName {
		cleanAndMayRenameForGoStyeCheck(utils.ReportFileIsExistForBugbye, utils.ResultFileIsExistForBugbye)
	} else if ruleName == utils.CodeRuleName {
		cleanAndMayRenameForGoStyeCheck(utils.ReportFileIsExistForCodeRule, utils.ResultFileIsExistForCodeRule)
	}
}

func cleanAndMayRenameForGoStyeCheck(reportFileIsExist bool, resultFileIsExist bool) {
	// 是否恢复 report.json
	if reportFileIsExist {
		if err := utils.CheckFileExist(utils.ReportJSONBackupName); err == nil {
			if err := os.Rename(utils.ReportJSONBackupName, utils.ReportJSONName); err != nil {
				utils.NeedSendHi = true
				utils.SendHiMsg = "CodeRuleCleanAndMayReName failed"
				fmt.Fprintln(&utils.WriteBuffer, "os rename failed. old name:", utils.ReportJSONBackupName, ", new name: ", utils.ReportJSONName, ", err: ", err.Error())
			}
		}
	}

	// 是否恢复 result.json
	if resultFileIsExist {
		if err := utils.CheckFileExist(utils.ResultJSONBackupName); err == nil {
			if err := os.Rename(utils.ResultJSONBackupName, utils.ResultJSONName); err != nil {
				utils.NeedSendHi = true
				utils.SendHiMsg = "CodeRuleCleanAndMayReName failed"
				fmt.Fprintln(&utils.WriteBuffer, "os rename failed. old name:", utils.ResultJSONBackupName, ", new name: ", utils.ResultJSONName, ", err: ", err.Error())
			}
		}
	}
}
