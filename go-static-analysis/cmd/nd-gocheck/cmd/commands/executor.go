package commands

import (
	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

type Executor struct {
	rootCmd *cobra.Command

	initCmd       *cobra.Command // 下载配置 && go install pre/pvcp_sa_extension/movetotmp
	runCmd        *cobra.Command // 执行所有规则
	runAllCmd     *cobra.Command // 执行所有规则
	customRuleCmd *cobra.Command // 自定义规则命令

	bugbyeRuleCmd *cobra.Command // 执行 Bugbye 规则
	codeRuleCmd   *cobra.Command // 代码规范命令

	IsEasyContainer bool

	customRuleIncre bool // 默认 false
}

func (e *Executor) SetCustomRuleIncre(value bool) {
	e.customRuleIncre = value
}

func NewExecutor() *Executor {
	e := &Executor{}

	e.initRoot()

	e.initRun()
	e.initRunAll()
	e.initRunCustom()
	e.initRunBugbye()
	e.initRunCodeRule()

	e.initInit() // init 命令作用用于下载所有配置文件
	e.initFlag()

	e.IsEasyContainer = utils.IsEasyContainer()
	return e
}

func (e *Executor) Execute() error {
	return e.rootCmd.Execute()
}

func (e *Executor) initFlag() {
	// 为代码规范规则增加 flag commit
	e.initFlagOnCmdForCodeRule(e.runCmd)
	e.initFlagOnCmdForCodeRule(e.runAllCmd)
	e.initFlagOnCmdForCodeRule(e.codeRuleCmd)

	// 为自研规则增加 flag  easy-all
	e.initFlagOnCmdForCustom(e.runCmd)
	e.initFlagOnCmdForCustom(e.runAllCmd)
	e.initFlagOnCmdForCustom(e.customRuleCmd)
}

// 只为 coderule 子命令以及涵盖其的run/run all 命令设置 commit flag以对齐流水线
func (e *Executor) initFlagOnCmdForCodeRule(cmd *cobra.Command) {
	fs := cmd.Flags()
	fs.SortFlags = false
	fs.BoolVar(&utils.IsCommit, "commit", false, "执行该工具前如果已执行了 git commit, 请将该参数设置为 =true")
}

// 只为自研规则 custom 子命令以及包含该命令的 run/run all 命令设置开启easy规范增量和全量检测，默认增量
func (e *Executor) initFlagOnCmdForCustom(cmd *cobra.Command) {
	fs := cmd.Flags()
	fs.SortFlags = false
	fs.BoolVar(&utils.IsEasyAll, "easy-all", false, "开启 easy 全量检测")
}
