package commands

import (
	"fmt"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/printer"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/reader"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/result"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

// run code rule
func (e *Executor) initRunBugbye() {
	e.bugbyeRuleCmd = &cobra.Command{
		Use:     "bugbye",
		Aliases: []string{"bb"},
		Short:   "run the bugbye linters",
		PreRun:  e.executePre,
		Run:     e.RunBugbye,
		PostRun: e.executePost,
	}
	e.runCmd.AddCommand(e.bugbyeRuleCmd)
}

func (e *Executor) BugbyeCheckValid() error {
	// 检查是否安装了 golang
	goBin := "go"
	if !utils.CheckGoBinExist(goBin) {
		return fmt.Errorf("go is not installed")
	}

	// 检测 golangci-lint 依赖的文件
	if err := utils.CheckFileExist(utils.DefaultAllConfigFile); err != nil {
		return err
	}

	if e.IsEasyContainer {
		if ok := utils.CheckGoBinExist(utils.EEGolangCINameForEasy); !ok {
			return fmt.Errorf(utils.EEGolangCINameForEasy + " is not installed for easy container")
		}
	} else {
		if ok := utils.CheckGoBinExist(utils.EEGolangCINameForOtherMachine); !ok {
			return fmt.Errorf(utils.EEGolangCINameForOtherMachine + " is not installed for other machine")
		}
	}
	return nil
}

func (e *Executor) RunBugbye(_ *cobra.Command, args []string) {
	if err := e.BugbyeCheckValid(); err != nil {
		utils.NeedSendHi = true
		utils.SendHiMsg = "bugbye check valid failed"
		fmt.Fprintln(&utils.WriteBuffer, err)
		return
	}

	// bugbye golangci部分
	if err := e.CheckReportAndResultExistAndSetForRule(utils.BugbyeRuleName); err != nil {
		utils.NeedSendHi = true
		utils.SendHiMsg = "CodeRule heckReportAndResultExistAndSet failed"
		return
	}

	var cmd string
	if e.IsEasyContainer {
		cmd = fmt.Sprintf("%s run -c=%s --out-format=json:%s --max-issues-per-linter=100 --max-same-issues=30",
			utils.EEGolangCINameForEasy, utils.DefaultAllConfigFile, utils.ReportJSONName)
		logrus.Debug("easy container bugbye cmd: ", cmd)
	} else {
		cmd = fmt.Sprintf("%s run -c=%s --out-format=json:%s --max-issues-per-linter=100 --max-same-issues=30",
			utils.EEGolangCINameForOtherMachine, utils.DefaultAllConfigFile, utils.ReportJSONName)
		logrus.Debug("dev machine bugbye cmd: ", cmd)
	}
	_, err := utils.ExecCommand(cmd)
	if err != nil {
		// 不作处理，这是因为 golangci-lint 检测出结果时，退出码即不为0，此时会报错
	}
	golangCIIssue, err := e.getIssuesFromResultJSON(utils.ResultJSONName)
	if err != nil {
		utils.NeedSendHi = true
		utils.SendHiMsg = "Bugbye PrintResultJSON failed\n"
		fmt.Fprintln(&utils.WriteBuffer, "print code rule result err: ", err.Error())
		// 打印结果失败不能妨碍后续的重命名操作
	}

	// bugbye govet部分
	goVetOutputStr, err := utils.ExecCommand(utils.GoVetCmd)
	if err != nil {
		logrus.Error("go vet -json 报错(可能由于语法问题导致), 请手动执行命令 go vet -json ./... 查看错误")
		// 不应return，应往下走把 golangci 执行结果产出，以及最后的重命名操作
	}

	goVetIssues := reader.GoVetOutput2Issues(goVetOutputStr)

	var issues []result.Issue
	issues = append(issues, goVetIssues...)
	issues = append(issues, golangCIIssue...)

	printer := printer.NewPrinter(utils.BugbyeRuleName, &utils.WriteBuffer)

	printer.PrintIssues(issues)

	e.CleanAndMayRenameForGoStyleCheck(utils.BugbyeRuleName)
}
