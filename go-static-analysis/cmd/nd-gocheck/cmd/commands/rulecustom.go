package commands

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

// run code rule
func (e *Executor) initRunCustom() {
	e.customRuleCmd = &cobra.Command{
		Use:     "netdisk",
		Aliases: []string{"nd"},
		Short:   "run the netdisk custom linters",
		PreRun:  e.executePre,
		Run:     e.RunCustom,
		PostRun: e.executePost,
	}
	e.runCmd.AddCommand(e.customRuleCmd)
}

func (e *Executor) CustomCheckValid() error {
	if ok := utils.CheckGoBinExist(utils.NdGolangCIName); !ok {
		return fmt.Errorf("%s is not installed", utils.NdGolangCIName)
	}

	return nil
}

func (e *Executor) RunCustom(_ *cobra.Command, args []string) {
	if err := e.CustomCheckValid(); err != nil {
		fmt.Fprintln(&utils.WriteBuffer, "err: ", err)
		return
	}

	// 检查 result.json 是否存在，存在则进行备份
	if err := e.CheckReportAndResultExistAndSetForRule(utils.CustomRuleName); err != nil {
		utils.NeedSendHi = true
		utils.SendHiMsg = "CodeRule checkReportAndResultExistAndSet failed"
		return
	}

	newFromRev := "HEAD"
	if utils.IsCommit {
		newFromRev = "HEAD~"
	}

	var cmd string
	if e.customRuleIncre {
		if utils.IsEasyProject() {
			if utils.IsEasyAll {
				cmd = fmt.Sprintf("EASY_ENABLED_DIFF=false %s run  --new-from-rev=%s --disable-all -E nilaway", newFromRev, utils.CustomRuleName)
			} else {
				cmd = fmt.Sprintf("EASY_ENABLED_DIFF=true %s run  --new-from-rev=%s --disable-all -E nilaway", newFromRev, utils.CustomRuleName)
			}
		} else {
			cmd = fmt.Sprintf("%s run  --disable-all -E nilaway", utils.CustomRuleName)
		}
	} else {
		if utils.IsEasyProject() {
			if utils.IsEasyAll {
				cmd = fmt.Sprintf("EASY_ENABLED_DIFF=false %s run -E easy -disable nilaway", utils.CustomRuleName)
			} else {
				cmd = fmt.Sprintf("EASY_ENABLED_DIFF=true %s run -E easy -disable nilaway", utils.CustomRuleName)
			}
		} else {
			cmd = fmt.Sprintf("%s run", utils.CustomRuleName)
		}
	}

	_, err := utils.ExecCommand(cmd)
	if err != nil {
		// 不作处理，这是因为 nd-golangci 检测出结果时，退出码为1，此时会报错
	}

	if err := e.PrintCustomResult(); err != nil {
		fmt.Println("customResult print failed, err: ", err)
		// fmt 抛出错误即可，不能影响后续rename逻辑
	}

	e.CleanAndMayRenameForNdGolangCI()
}

func (e *Executor) PrintCustomResult() error {
	err := e.PrintResultJSON(utils.CustomRuleName)
	if err != nil {
		return err
	}

	return nil
}

func (e *Executor) CleanAndMayRenameForNdGolangCI() {
	if err := utils.CheckFileExist(utils.ResultJSONName); err == nil {
		if err := os.Remove(utils.ResultJSONName); err != nil {
			utils.NeedSendHi = true
			utils.SendHiMsg = "CleanAndMayRenameForNdGolangCI failed"
			fmt.Fprintln(&utils.WriteBuffer, "nd-golangci clean failed, filename: ", utils.ResultJSONName, "err: ", err)
			// 此时 result.json 文件存在&删除失败，但不影响后续的result.json-backup恢复为result.jsons的逻辑
		}
	}

	// 在执行 bugbye 时候是否需要进行恢复
	cleanAndMayRenameForNdGolangci()
}

func cleanAndMayRenameForNdGolangci() {
	// 是否恢复 result.json
	if utils.ResultFileIsExistForCustom {
		if err := utils.CheckFileExist(utils.ResultJSONBackupName); err == nil {
			if err := os.Rename(utils.ResultJSONBackupName, utils.ResultJSONName); err != nil {
				utils.NeedSendHi = true
				utils.SendHiMsg = "CustomRuleCleanAndMayReName failed"
				fmt.Fprintln(&utils.WriteBuffer, "os rename failed. old name:", utils.ResultJSONBackupName, ", new name: ", utils.ResultJSONName, ", err: ", err.Error())
				// 该函数为其他函数最后才进行调用，fmt抛出错误即可，不用向上返回
			}
		}
	}
}

/*

	if e.customRuleIncre {
		if utils.IsEasyProject() {
			if utils.IsEasyAll {
				cmd = fmt.Sprintf("EASY_ENABLED_DIFF=false %s run --disable-all -E nilaway", utils.CustomRuleName)
			} else {
				cmd = fmt.Sprintf("EASY_ENABLED_DIFF=true %s run --disable-all -E nilaway", utils.CustomRuleName)
			}
		} else {
			cmd = fmt.Sprintf("%s run  --disable-all -E nilaway", utils.CustomRuleName)
		}
	} else {
		if utils.IsEasyProject() {
			if utils.IsEasyAll {
				cmd = fmt.Sprintf("EASY_ENABLED_DIFF=false %s run -E easy -disable nilaway", utils.CustomRuleName)
			} else {
				cmd = fmt.Sprintf("EASY_ENABLED_DIFF=true %s run -E easy -disable nilaway", utils.CustomRuleName)
			}
		} else {
			cmd = fmt.Sprintf("%s run", utils.CustomRuleName)
		}
	}

*/
