package commands

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

func (e *Executor) initInit() {
	e.initCmd = &cobra.Command{
		Use:   "init",
		Short: "download the config file and the binary file we need",
		Run:   e.executeInit,
	}

	e.rootCmd.AddCommand(e.initCmd)
}

func (e *Executor) executeInit(_ *cobra.Command, args []string) {
	logrus.Info("nd-gocheck init start...")
	// install nd-golangci
	output, err := utils.ExecCommandWithRetry(utils.CustomRuleBinInstallCmd)
	if err != nil {
		logrus.WithField("cmd", utils.CustomRuleBinInstallCmd).WithField("output", output).WithError(err).Error("init failed")
		return
	}
	logrus.Info("download binary file nd-golangci done")

	// install nd-gocheck
	output, err = utils.ExecCommandWithRetry(utils.NdGoCheckInstallCmd)
	if err != nil {
		logrus.WithField("cmd", utils.NdGoCheckInstallCmd).WithField("output", output).WithError(err).Error("init failed")
		return
	}
	logrus.Info("download binary file nd-gocheck done")

	// go install 得到的bin拷贝覆盖which得到的bin路径
	goPath, err := utils.ExecCommand("go env GOPATH")
	if err != nil {
		logrus.WithError(err).Error("command go env GOPATH failed")
		return
	}
	goPath = strings.TrimSpace(goPath)
	ndGoCheckBinPath := filepath.Join(goPath, "bin", "nd-gocheck")
	realNdGoCheckBinPath, err := utils.ExecCommand("which nd-gocheck")
	if err != nil {
		logrus.Error("which nd-gocheck失败. nd-gocheck工具已下载, 默认放在$GOPATH/bin/目录下, 请将$GOPATH/bin加入到PATH中, 并重新执行init命令")
		return
	}
	realNdGoCheckBinPath = strings.TrimSpace(realNdGoCheckBinPath)

	ok, err := isSameFile(realNdGoCheckBinPath, ndGoCheckBinPath)
	if err != nil {
		logrus.WithError(err).Error("nd-gocheck bin path is not same")
	} else {
		if !ok {
			cmd := fmt.Sprintf("/usr/bin/cp -f %s %s", ndGoCheckBinPath, realNdGoCheckBinPath)
			output, err := utils.ExecCommand(cmd)
			if err != nil {
				logrus.Errorf("commnd %s failed, output: %s, err: %s\n", cmd, output, err.Error())
				return
			}
		}
	}

	// 全量规则配置下载
	output, err = utils.ExecCommandWithRetry(utils.BugbyeConfigCmd)
	if err != nil {
		logrus.WithField("cmd", utils.BugbyeConfigCmd).WithField("output", output).WithError(err).Error("init all config failed")
		return
	}
	logrus.Info("download all config file done")

	// 增量配置下载
	output, err = utils.ExecCommandWithRetry(utils.CodeRuleConfigCmd)
	if err != nil {
		logrus.WithField("cmd", utils.CodeRuleConfigCmd).WithField("output", output).WithError(err).Error("init diff config failed")
		return
	}
	logrus.Info("download diff config file done")

	// install go-stylecheck
	var cmd string
	if e.IsEasyContainer {
		cmd = utils.CodeRuleBinDownloadCmd
	} else {
		cmd = utils.CodeRuleBinDownloadCmdForOtherMachine
	}
	output, err = utils.ExecCommandWithRetry(cmd)
	if err != nil {
		logrus.WithField("cmd", cmd).WithField("output", output).WithError(err).Error("download binary file failed")
		return
	}
	logrus.Info("download coderule bin file done")
	logrus.Info("nd-gocheck init success!")
}

// 判断是否相同
func isSameFile(name1 string, name2 string) (bool, error) {
	f1, err := os.Stat(name1)
	if err != nil {
		return false, err
	}
	f2, err := os.Stat(name2)
	if err != nil {
		return false, err
	}
	return os.SameFile(f1, f2), nil
}
