package utils

import (
	"bytes"
	"fmt"
	"os"
	"time"
)

const (
	RetryCnt = 3

	// 配置文件路径
	DefaultAllConfigFile  = "~/.golangci-config-all.yml"
	DefaultDiffConfigFile = "~/.golangci-config-diff.yml"

	// 工具名称以及执行方式
	EEGolangCINameForOtherMachine = "$HOME/.config/go-stylecheck" // ForOtherMachine
	EEGolangCINameForEasy         = "go-stylecheck"               // ForEasy
	NdGolangCIName                = "nd-golangci"

	// 文件命名前缀路径
	NamePrefix = "nd-1233-pvcp-1233"

	CodeRuleName   = "coderule"
	BugbyeRuleName = "bugbye"

	CustomRuleName = "nd-golangci"

	ToolTypeGoLangCI = "golangci"
	ToolTypeGoVet    = "go vet"

	// go vet
	GoVetCmd = "go vet -json ./..."

	// 配置下载命令
	BugbyeConfigCmd = `
		if [ -f "$HOME/.golangci-config-all.yml" ]; then
			mv -f $HOME/.golangci-config-all.yml $HOME/.golangci-config-all-backup.yml
		fi
		wget -P $HOME http://10.151.240.20:8005/.golangci-config-all.yml
	`

	CodeRuleConfigCmd = `
	if [ -f "$HOME/.golangci-config-diff.yml" ]; then
		mv -f $HOME/.golangci-config-diff.yml $HOME/.golangci-config-diff-backup.yml
	fi

	wget -P $HOME http://10.151.240.20:8005/.golangci-config-diff.yml
	`

	// 10.151.240.20:8005 地址为 qa机器bjyz-ps-beehive-agent147396.bjyz.baidu.com 以下路径：/home/<USER>/gocheck/http_server/file
	// go-stylecheck 在go1.21下编译
	// 代码规范二进制文件下载
	// nolint
	CodeRuleBinDownloadCmd = `cd /tmp && rm -rf /tmp/output && rm -rf /tmp/go-stylecheck.tar.gz \
		&& wget -P /tmp --quiet -O go-stylecheck.tar.gz http://10.151.240.20:8005/output.tar.gz \
		&& cd /tmp && tar xvf go-stylecheck.tar.gz && mv -f /tmp/output/go-stylecheck /root/bae/sandbox_tools/bin/`
	// 支持其他机器go-stylecheck.tar.gz下载
	// nolint:lll
	CodeRuleBinDownloadCmdForOtherMachine = `cd $HOME && mkdir -p .config/pvcp-ndgocheck && cd .config/pvcp-ndgocheck \
		&& rm -rf output && rm -rf go-stylecheck.tar.gz \
		&& wget -P /tmp --quiet -O go-stylecheck.tar.gz http://10.151.240.20:8005/output.tar.gz \
		&& tar xvf go-stylecheck.tar.gz && mv -f output/go-stylecheck ../go-stylecheck`

	// 自研规则二进制文件下载
	CustomRuleBinInstallCmd = `go install icode.baidu.com/baidu/netdisk/nd-golangci/cmd/nd-golangci@latest`

	// nd-gocheck工具自更新命令
	NdGoCheckInstallCmd = `go install icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck@latest`

	// 配置文件更新前执行
	// nolint
	PreUpdateConfigCmd = `mv -f $HOME/.golangci-config-all.yml $HOME/.golangci-config-all-backup.yml \
							&& rm -rf $HOME/.golangci-config-all.yml" && mv -f $HOME/.golangci-config-diff.yml $HOME/.golangci-config-diff-backup.yml`
	RecoverBugbyeCmd   = "mv -f $HOME/.golangci-config-all-backup.yml $HOME/.golangci-config-all.yml"
	RecoverCodeRuleCmd = "mv -f $HOME/.golangci-config-diff-backup.yml $HOME/.golangci-config-diff.yml"

	ConnectTimeout   = 1000
	ReadWriteTimeout = 3000
)

var (
	// 判断是否提交
	IsCommit  bool
	IsEasyAll bool

	// HOME环境变量
	HomeCache string

	// 代码规范产出文件名称
	ResultJSONName = "result.json"
	ReportJSONName = "report.json"

	// 如果上述文件存在则进行备份
	ResultJSONBackupName = fmt.Sprintf("%s-result-backup.json", NamePrefix) // 代码规范，用于 rd 当前代码库下已经存在 result.json 文件的情形，将之重命名为该文件
	ReportJSONBackupName = fmt.Sprintf("%s-report-backup.json", NamePrefix) // 用于备份 rd 的 report文件
	BugbyeJSONName       = fmt.Sprintf("%s-bugbye-result.json", NamePrefix)

	// 用于判断是否备份文件
	ResultFileIsExistForBugbye bool
	ReportFileIsExistForBugbye bool

	ResultFileIsExistForCodeRule bool
	ReportFileIsExistForCodeRule bool

	ResultFileIsExistForCustom bool

	// 用于输出缓冲
	WriteBuffer bytes.Buffer

	// 用于数据统计
	CodeRuleCnt      int
	BugbyeRuleCnt    int
	CustomRuleCnt    int
	CustomRuleResult string

	// 存储最近一次使用时间的文件名称
	useTimeRecordFileName = fmt.Sprintf("%s-%s", NamePrefix, "gosa-lastused")
	UseTimeRecordFileName = fmt.Sprintf("%s/%s", os.TempDir(), useTimeRecordFileName)

	NeedSendHi bool
	SendHiMsg  string

	TimeStart time.Time
)
