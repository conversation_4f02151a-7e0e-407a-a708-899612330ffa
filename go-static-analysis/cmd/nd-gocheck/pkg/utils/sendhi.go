package utils

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

// 线下工具Hi报警
var URL = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d7cb838703690219d66a37971bd4a74bf"

func SendWarnMessageToHi(msg string) {
	u, err := ExecCommand("git config --get user.name")
	if err != nil {
		// 这里可不做处理，因在数据统计时已做了相应的输出
	}
	userName := strings.TrimSpace(u)

	pwd, err := os.Getwd()
	if err != nil {
		fmt.Println("获取代码库所在目录失败")
	}

	readMsg := fmt.Sprintf("user: %s\npwd: %s\nmsg: %s", userName, pwd, msg)

	headerMap := map[string]string{}
	headerMap["Content-Type"] = "application/json"

	conMaps := []map[string]interface{}{}
	conMap := map[string]interface{}{}
	conMap["type"] = "TEXT"
	conMap["content"] = readMsg
	conMaps = append(conMaps, conMap)

	bodyMap := map[string]interface{}{}
	bodyMap["body"] = conMaps

	messageMap := map[string]interface{}{}
	messageMap["message"] = bodyMap

	bodyJSON, _ := json.Marshal(messageMap)

	for i := 0; i < 3; i++ {
		_, err := httpclient.Post(URL, headerMap, 1000, 3000, string(bodyJSON))
		if err == nil {
			break
		}
	}
}
