package utils

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"os/exec"
	"path/filepath"
)

const (
	sandboxContainerFile = "/root/.record" // 沙盒容器中含有这个文件
)

func ExecCommandWithRetry(script string, args ...string) (string, error) {
	var output string
	var err error
	for i := 0; i < RetryCnt; i++ {
		output, err = ExecCommand(script, args...)
		if err == nil {
			break
		}
	}
	return output, err
}

func ExecCommand(script string, args ...string) (string, error) {
	cmd := exec.Command("/bin/bash", append([]string{"-s", "-"}, args...)...)
	wc, err := cmd.StdinPipe()
	if err != nil {
		return "", err
	}

	go func() {
		defer wc.Close()
		fmt.Fprintf(wc, "%s", script)
	}()

	output, err := cmd.CombinedOutput()
	return string(output), err
}

func CheckFileExist(path string) error {
	path, err := Expand(path)
	if err != nil {
		return err
	}

	info, err := os.Stat(path)
	if err != nil {
		return err
	}

	ok := info.IsDir()
	if ok {
		return fmt.Errorf("path is a dir, path is %s", path)
	}
	return nil
}

func CheckGoBinExist(bin string) bool {
	cmd := fmt.Sprintf("which %s", bin)
	_, err := ExecCommand(cmd)
	if err != nil {
		return false
	}

	return true
}

func Expand(path string) (string, error) {
	if len(path) == 0 {
		return path, nil
	}

	if path[0] != '~' {
		return path, nil
	}

	if len(path) > 1 && path[1] != '/' {
		return "", fmt.Errorf("the path format is illegal, path: %s", path)
	}

	return filepath.Join(HomeCache, path[1:]), nil
}

func IsEasyProject() bool {
	fileSet := token.NewFileSet()
	f, err := parser.ParseFile(fileSet, "main.go", nil, 0)
	if err != nil {
		return false
	}

	var res = false
	ast.Inspect(f, func(node ast.Node) bool {
		callExpr, ok := node.(*ast.CallExpr)
		if !ok {
			return true
		}

		selectorExpr, ok := callExpr.Fun.(*ast.SelectorExpr)
		if !ok {
			return true
		}

		x, ok := selectorExpr.X.(*ast.Ident)
		if !ok {
			return true
		}

		sel := selectorExpr.Sel
		if x.Name == "easy" && sel.Name == "Start" {
			res = true
		}
		return true
	})
	return res
}

// 在 easy icoding 中
func IsEasyContainer() bool {
	err := CheckFileExist(sandboxContainerFile)
	if err != nil {
		fmt.Println("this is not easy icoding")
		return false
	}

	fmt.Println("this is easy icoding")
	return true
}
