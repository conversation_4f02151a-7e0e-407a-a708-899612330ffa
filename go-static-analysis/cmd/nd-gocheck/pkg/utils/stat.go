package utils

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const StatURL = "http://10.143.142.21:8090/pvcp/extension/statistics/add"

func SendStatData(result string) {
	now := time.Now().Unix()
	moduleNameWithICode, _ := ExecCommand("head -1 go.mod") // 该错误先不予处理（也不好处理，运行在线下），出现错误只影响后续数据统计的完整性
	m := strings.TrimPrefix(moduleNameWithICode, "module icode.baidu.com/")
	moduleName := strings.TrimSpace(m)

	u, err := ExecCommand("git config --get user.name")
	if err != nil {
		fmt.Println("执行命令git config --get user.name失败, 无法统计到工具使用情况，请配置user.name为邮箱前缀")
	}

	userName := strings.TrimSpace(u)

	pwd, err := os.Getwd()
	if err != nil {
		fmt.Println("执行命令os.Getwd失败，无法统计代码库当前所在目录")
	}

	typ := "ND-GOCHECK"

	headerMap := map[string]string{}
	bodyMap := map[string]interface{}{}

	headerMap["Content-Type"] = "application/json"
	bodyMap["dir"] = pwd
	bodyMap["username"] = userName
	bodyMap["module_name"] = moduleName
	bodyMap["create_at"] = now
	bodyMap["type"] = typ
	bodyMap["result"] = result

	buildErrCnt := BugbyeRuleCnt + CodeRuleCnt
	checkResult := CustomRuleCnt

	bodyMap["total_cnt"] = buildErrCnt + checkResult
	bodyMap["builderr_cnt"] = buildErrCnt // 复用老表存储数据
	bodyMap["correct_cnt"] = checkResult

	bodyJSON, err := json.Marshal(bodyMap)
	if err != nil {
		fmt.Println("[stat data failed.][err: %w]", err)
		return
	}

	for i := 0; i < 3; i++ {
		_, err := httpclient.Post(StatURL, headerMap, ConnectTimeout, ReadWriteTimeout, string(bodyJSON))
		if err == nil {
			break
		}
	}
}
