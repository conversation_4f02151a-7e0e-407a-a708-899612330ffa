package printer

import (
	"fmt"
	"io"
	"reflect"
	"strings"

	"github.com/fatih/color"
	"github.com/sirupsen/logrus"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/result"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

type Printer struct {
	ruleName string // govet/coderule/bugbye
	w        io.Writer
}

func NewPrinter(ruleName string, w io.Writer) *Printer {
	return &Printer{
		ruleName: ruleName,
		w:        w,
	}
}

func (p *Printer) SprintfColored(ca color.Attribute, format string, args ...interface{}) string {
	c := color.New(ca)
	return c.Sprintf(format, args...)
}

func (p *Printer) PrintIssues(issues []result.Issue) {
	if p.ruleName == utils.CodeRuleName {
		utils.CodeRuleCnt = len(issues)
		text := p.SprintfColored(color.FgBlack, "代码规范 (增量检测) 检测结果数目: %d, 结果见下方:", utils.CodeRuleCnt)
		fmt.Fprintln(&utils.WriteBuffer, text)
	} else if p.ruleName == utils.BugbyeRuleName {
		utils.BugbyeRuleCnt = len(issues)
		text := p.SprintfColored(color.FgBlack, "流水线 bugbye 检测结果数目: %d, 结果见下方:", utils.BugbyeRuleCnt)
		fmt.Fprintln(&utils.WriteBuffer, text)
	} else if p.ruleName == utils.CustomRuleName {
		utils.CustomRuleCnt = len(issues)
		text := p.SprintfColored(color.FgBlack, "自研规则检测结果数目: %d, 结果见下方:", utils.CustomRuleCnt)
		fmt.Fprintln(&utils.WriteBuffer, text)
	}

	for i := range issues {
		p.printIssue(issues[i])
		p.printSourceCode(issues[i])
		p.printUnderLinePointer(issues[i])
	}
}

func (p *Printer) printIssue(i result.Issue) {
	switch i := i.(type) {
	case *result.GolangCIIssue:
		text := p.SprintfColored(color.FgRed, "%s", strings.TrimSpace(i.Text))
		text += fmt.Sprintf(" (%s)", i.FromLinter)
		pos := p.SprintfColored(color.Bold, "%s:%d", i.FilePath(), i.Line())
		if i.Pos.Column != 0 {
			pos += fmt.Sprintf(":%d", i.Pos.Column)
		}
		fmt.Fprintf(p.w, "%s: %s\n", pos, text)
	case *result.GoVetIssue:
		text := p.SprintfColored(color.FgRed, i.Message)
		pos := p.SprintfColored(color.Bold, i.Posn)
		fmt.Fprintf(p.w, "%s: %s\n", pos, text)
	default:
		logrus.Warnf("unsupported issue type %v", reflect.TypeOf(i))
	}
}

func (p *Printer) printSourceCode(i result.Issue) {
	switch i := i.(type) {
	case *result.GolangCIIssue:
		for _, line := range i.SourceLines {
			fmt.Fprintln(p.w, line)
		}
	case *result.GoVetIssue:
	default:
		logrus.Warnf("unsupported issue type ", reflect.TypeOf(i))
	}
}

func (p *Printer) printUnderLinePointer(i result.Issue) {
	switch i := i.(type) {
	case *result.GolangCIIssue:
		// if column == 0 it means column is unknown (e.g. for gosec)
		if len(i.SourceLines) != 1 || i.Pos.Column == 0 {
			return
		}

		col0 := i.Pos.Column - 1
		line := i.SourceLines[0]
		prefixRunes := make([]rune, 0, len(line))
		for j := 0; j < len(line) && j < col0; j++ {
			if line[j] == '\t' {
				prefixRunes = append(prefixRunes, '\t')
			} else {
				prefixRunes = append(prefixRunes, ' ')
			}
		}

		fmt.Fprintf(p.w, "%s%s\n", string(prefixRunes), p.SprintfColored(color.FgYellow, "^"))
	case *result.GoVetIssue:
		// 不用处理
	default:
		logrus.Warnf("unsupported issue type ", reflect.TypeOf(i))
	}
}
