package result

import (
	//nolint:gosec
	"go/token"

	"golang.org/x/tools/go/packages"
)

type Issue interface {
	issueNode()
}

type Range struct {
	From, To int
}

type Replacement struct {
	NeedOnlyDelete bool     // need to delete all lines of the issue without replacement with new lines
	NewLines       []string // if NeedDelete is false it's the replacement lines
	Inline         *InlineFix
}

type InlineFix struct {
	StartCol  int // zero-based
	Length    int // length of chunk to be replaced
	NewString string
}

type GolangCIIssue struct {
	FromLinter string
	Text       string

	Severity string

	// Source lines of a code with the issue to show
	SourceLines []string

	// If we know how to fix the issue we can provide replacement lines
	Replacement *Replacement

	// Pkg is needed for proper caching of linting results
	Pkg *packages.Package `json:"-"`

	LineRange *Range `json:",omitempty"`

	Pos token.Position

	// HunkPos is used only when golangci-lint is run over a diff
	HunkPos int `json:",omitempty"`

	// If we are expecting a nolint (because this is from nolintlint), record the expected linter
	ExpectNoLint bool

	ExpectedNoLintLinter string
}

func (i *GolangCIIssue) issueNode() {}

func (i *GolangCIIssue) FilePath() string {
	return i.Pos.Filename
}

func (i *GolangCIIssue) Line() int {
	return i.Pos.Line
}

func (i *GolangCIIssue) Column() int {
	return i.Pos.Column
}

func (i *GolangCIIssue) GetLineRange() Range {
	if i.LineRange == nil {
		return Range{
			From: i.Line(),
			To:   i.Line(),
		}
	}

	if i.LineRange.From == 0 {
		return Range{
			From: i.Line(),
			To:   i.Line(),
		}
	}

	return *i.LineRange
}
