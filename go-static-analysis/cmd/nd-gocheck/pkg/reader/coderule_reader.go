package reader

import (
	"encoding/json"
	"go/token"
	"io"
	"os"
	"strconv"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/result"
)

// 公司代码规范
func GetCodeRuleData(jsonFileName string) (result.CodeRuleData, error) {
	file, err := os.Open(jsonFileName)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	byt, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}

	res := result.CodeRuleData{}
	if err := json.Unmarshal(byt, &res); err != nil {
		return nil, err
	}

	return res, nil
}

func CodeRuleData2Issues(crData result.CodeRuleData) ([]result.Issue, error) {
	var res []result.Issue

	for fileName, RuleResult := range crData {
		for ruleName, LineIssue := range RuleResult {
			for line, data := range LineIssue {
				issue, err := makeIssue(fileName, ruleName, line, data)
				if err != nil {
					golog.Warn("[msg: strconc string to int failed, string: %s]", line)
					continue
				}
				res = append(res, issue)
			}
		}
	}
	return res, nil
}

func makeIssue(fileName string, ruleName string, lineString string, data [3]string) (*result.GolangCIIssue, error) {
	res := &result.GolangCIIssue{}
	res.Severity = data[0]
	res.Text = ruleName + "-" + data[1]
	res.FromLinter = data[2]

	line, err := strconv.Atoi(lineString)
	if err != nil {
		return nil, err
	}
	res.Pos = token.Position{
		Filename: fileName,
		Line:     line,
	}
	return res, nil
}

// 原始数据
// key:fileName

type CodeRuleData map[string]RuleResultData

// key:ruleName
type RuleResultData map[string]LineResultData

// key:lineNumber, value: severity, text, fromLinter
type LineResultData map[string][3]string
