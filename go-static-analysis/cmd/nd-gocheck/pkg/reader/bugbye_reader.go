package reader

import (
	"encoding/json"
	"strings"

	"github.com/sirupsen/logrus"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/result"
)

// goVetOutput 由多个 json 合并构成，其中由#和包名做分割
func GoVetOutput2Issues(goVetString string) []result.Issue {
	lines := strings.Split(goVetString, "\n")
	var outputs []result.GoVetOutput
	var content string
	for _, line := range lines {
		if !strings.HasPrefix(line, "#") {
			content += line + "\n"
		} else {
			newContent := content
			content = "" // 置空不影响后续的反序列化
			if strings.TrimSpace(newContent) == "{}" || strings.TrimSpace(newContent) == "" {
				continue
			}

			var goVetOutput result.GoVetOutput
			if err := json.Unmarshal([]byte(newContent), &goVetOutput); err != nil {
				logrus.WithField("content", newContent).Warn("unmarshal govet result faield")
				continue
			}
			outputs = append(outputs, goVetOutput)
		}
	}

	var goVetOutput result.GoVetOutput
	if err := json.Unmarshal([]byte(content), &goVetOutput); err != nil {
		logrus.WithField("content", content).Warn("unmarshal govet result faield")
	} else {
		outputs = append(outputs, goVetOutput)
	}

	var res []result.Issue
	for _, pkgRuleMap := range outputs {
		for _, ruleMap := range pkgRuleMap {
			for _, issueSlice := range ruleMap {
				for _, issue := range issueSlice {
					res = append(res, issue)
				}
			}
		}
	}

	return res
}
