package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"io/ioutil"
	"strings"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

type FunDecl struct {
	FunAstFun     *ast.FuncDecl
	PackageName   string
	FuncPrototype string
	FileName      string
	Line          int
}

func getFileList(dir string) error {
	if dir == "" {
		return fmt.Errorf("dir null")
	}

	files, err := ioutil.ReadDir(dir)
	if err != nil {
		return err
	}

	for _, file := range files {
		if file.IsDir() {
			//忽略隐藏目录
			dirName := file.Name()
			if dirName != "" && dirName[0:1] == "." {
				continue
			}
			getFileList(dir + "/" + file.Name())
		} else {
			if strings.HasSuffix(file.Name(), ".go") && !strings.HasSuffix(file.Name(), "_test.go") {
				fileList = append(fileList, dir+"/"+file.Name())
			}
		}
	}

	return nil
}

func getStructTypeInfo(typeName, dir string) string {
	packageName := ""

	fileList = []string{}
	getFileList(dir)

	for _, file := range fileList {
		fset := token.NewFileSet()
		f, err := parser.ParseFile(fset, file, nil, 0)
		if err != nil {
			return packageName
		}

		ast.Inspect(f, func(x ast.Node) bool {
			if genDel, ok := x.(*ast.GenDecl); ok {
				if genDel.Tok.String() == "type" {
					for _, varIn := range genDel.Specs {
						if typeSpec, ok := varIn.(*ast.TypeSpec); ok {
							if typeSpec.Name.Name == typeName {
								packageName = f.Name.Name
							}
						}
					}
				}
			}
			return true
		})
	}

	return packageName
}

func astAnalysis(fileList []string, moduleName, dir string) error {
	for _, file := range fileList {
		fset := token.NewFileSet()
		f, err := parser.ParseFile(fset, file, nil, 0)
		if err != nil {
			return err
		}

		ssaFile := strings.ReplaceAll(file, dir, moduleName)
		ast.Inspect(f, func(x ast.Node) bool {
			if fun, ok := x.(*ast.FuncDecl); ok {
				funcX := formatNode(x)
				end := strings.LastIndex(funcX, " {")
				if end <= 0 {
					//fmt.Println("ast find bad ", funcX)
					return false
				}

				funDecl := funcX[0:end]
				ssaFunName, err := getSsaName(fun, ssaFile)
				if err != nil {
					//fmt.Println("ssa find bad ", funcX)
					util.SendWarnMessageToHi(err.Error() + " " + dir)
					outputToConsole(8, 0, err.Error(), []interface{}{})
					return false
				}

				ssaFunc2Decl[ssaFunName] = &FunDecl{
					PackageName:   f.Name.Name,
					FuncPrototype: funDecl,
					FunAstFun:     fun,
					FileName:      file,
					Line:          fset.Position(fun.Pos()).Line,
				}
			}

			if importInfo, ok := x.(*ast.ImportSpec); ok {
				//别名$路径信息  hc "xxxx"
				if importInfo.Name == nil {
					fileImportList[file] = append(fileImportList[file], importInfo.Path.Value)
				} else {
					fileImportList[file] = append(fileImportList[file], importInfo.Name.Name+"$"+importInfo.Path.Value)
				}
			}
			return true
		})
	}

	return nil
}
