package main

import (
	"context"
	"encoding/json"
	"os"
	"os/exec"
	"strings"
	"sync"
	"time"

	"golang.org/x/tools/go/packages"
	"golang.org/x/tools/go/ssa"
	"golang.org/x/tools/go/ssa/ssautil"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

var mutex sync.Mutex

func setResult(parentFun string) {
	mutex.Lock()
	if _, ok := result[parentFun]; !ok {
		result[parentFun] = struct{}{}
	}
	mutex.Unlock()
}

func (fs *FunStack) funcCallStackRecv(curFun, parentFun string, depth int, funInstr *ssa.Call, parentInstr []*ssa.Call) {
	defer wg.Done()
	fs.funcCallStack(curFun, parentFun, depth, funInstr, parentInstr)
}

func (fs *FunStack) funcCallStack(curFun, parentFun string, depth int, funInstr *ssa.Call, parentInstr []*ssa.Call) {
	depth++
	if depth > fs.MaxDepth {
		setResult(parentFun)
		parentInstr = nil
		return
	}

	// 防止死循环
	funcStack := strings.Split(parentFun, " -> ")
	for _, v := range funcStack {
		if v == curFun {
			setResult(parentFun)
			parentInstr = nil
			return
		}
	}

	tmp := curFun
	if strings.Contains(curFun, "#") {
		arr := strings.Split(curFun, "#")
		curFun = arr[0]
	}

	if parentFun == "" {
		parentFun = tmp
	} else {
		parentFun = parentFun + " -> " + tmp
	}

	if funInstr != nil {
		if parentInstr == nil {
			parentInstr = []*ssa.Call{funInstr}
		} else {
			parentInstr = append(parentInstr, funInstr)
		}
	}

	// for _, v := range fs.Targets {
	// if curFun == v {

	//	return
	// }
	// }

	// 寻址前需剔除获取的参数部分
	if funStepCall[curFun] == nil {
		setResult(parentFun)
		parentInstr = nil
		return
	}

	funInstrs := funStepCall[curFun]
	for _, funInstr := range funInstrs {
		tmp := funInstr.Call.StaticCallee().String()
		fs.funcCallStack(tmp, parentFun, depth, funInstr, parentInstr)
	}
}

func (fs *FunStack) buildSSA(path ...string) (*ssa.Program, bool, []byte) {
	cfg := &packages.Config{
		Mode:  packages.LoadAllSyntax,
		Tests: false,
		Dir:   fs.Path,
		Env:   os.Environ(),
	}

	pkgs, err := packages.Load(cfg, path...)
	if err != nil {
		msg := "Loaded package error, source code need go build ok and use go module "
		util.SendWarnMessageToHi(msg + " " + err.Error() + " " + fs.FileName)
		v := outputToConsole(4, 0, msg+" "+err.Error()+" "+fs.FileName, []interface{}{})
		return nil, false, v
	}

	msg := ""
	packages.Visit(pkgs, nil, func(pkg *packages.Package) {
		for _, err := range pkg.Errors {
			msg += err.Error()
		}
	})

	if msg != "" {
		util.SendWarnMessageToHi(msg + fs.FileName)
		v := outputToConsole(4, 0, msg+fs.FileName, []interface{}{})
		return nil, false, v
	}

	prog, _ := ssautil.AllPackages(pkgs, ssa.SanityCheckFunctions)
	prog.Build()
	return prog, true, []byte{}
}

func (fs *FunStack) buildCallGraph(prog *ssa.Program) {
	var filePathName string

	// 只分析以baidu代码库函数开头的调用关系，没必要分析其他的代码库和函数的调用关系。
	// 函数为interface类型的无法建立调用链
	for fn := range ssautil.AllFunctions(prog) {
		if strings.Contains(fn.String(), BAIDU_CODE_FLAG) {
			lineNum := fn.Prog.Fset.Position(fn.Pos()).Line
			file := fn.Prog.Fset.File(fn.Pos())

			if file != nil {
				filePathName = file.Name()
			} else {
				filePathName = ""
			}

			// 针对函数指针嵌套等情况 lineNum == fs.Line + 1
			if (lineNum == fs.Line || lineNum == fs.Line+1) && (fs.FileName == filePathName) {
				fs.Froms = append(fs.Froms, fn.String())
			}

			funStrSsaFun[fn.String()] = fn
			funStepCall[fn.String()] = nil

			for _, block := range fn.DomPreorder() {
				for _, instr := range block.Instrs {
					switch instr := instr.(type) {
					case *ssa.Go:
					case *ssa.Call:
						if instr.Call.StaticCallee() != nil {
							if fs.Begin != 0 && fs.End != 0 && fs.End >= fs.Begin {
								callLine := fn.Prog.Fset.Position(instr.Call.Pos()).Line
								if callLine < fs.Begin || callLine > fs.End {
									continue
								}
							}

							funStepCall[fn.String()] = append(funStepCall[fn.String()], instr)
						}
					}
				}
			}
		}
	}
}

// (*icode.baidu.com/baidu/netdisk/pcs-go-pcsapi/action/file.File).FileRapidupload
// 解析出icode.baidu.com/baidu/netdisk/pcs-go-pcsapi
// icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go.main
// 解析出icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go
func getModuleName(name string) string {
	begin := strings.Index(name, BAIDU_CODE_FLAG)
	if begin == -1 {
		begin = 0
	}

	tmp := name[begin:]
	flagArr := strings.Split(tmp, "/")
	if len(flagArr) < 4 {
		return ""
	}

	moduName := ""
	for i := 0; i < 4; i++ {
		if i == 3 {
			arr := strings.Split(flagArr[i], ".")
			if len(arr) > 1 {
				moduName += arr[0]
			} else {
				moduName += flagArr[i]
			}
		} else {
			moduName += flagArr[i] + "/"
		}
	}

	// 临时添加白名单，todo要深入到具体路径，通过go.mod内容获取具体module信息。
	if moduName == "icode.baidu.com/baidu/netdisk/mips-scripts" {
		moduName = "icode.baidu.com/baidu/netdisk/mips-scripts/update_tag"
	}

	return moduName
}

// (*icode.baidu.com/baidu/netdisk/pcs-go-pcsapi/action/file.File).FileRapidupload
// 解析出icode.baidu.com/baidu/netdisk/pcs-go-pcsapi/action/file
func getFuncImportInfo(name string) string {
	importPath := ""
	name = strings.ReplaceAll(name, "(", "")
	name = strings.ReplaceAll(name, ")", "")
	name = strings.ReplaceAll(name, "*", "")

	end1 := strings.LastIndex(name, "/")
	importPath = name[:end1]

	tmp := name[end1:]
	end2 := strings.Index(tmp, ".")
	importPath += tmp[:end2]

	return importPath
}

func (fs *FunStack) getStackInfo() []interface{} {
	datas := []interface{}{}
	if len(result) == 0 && len(fs.Froms) > 0 {
		result[fs.Froms[0]] = struct{}{}
	}

	uniqStack := make(map[string]struct{}, len(result))

	for k := range result {
		// 过滤rd提的特殊case
		if strings.Contains(k, "ufc.UfcHttpRequest).Do") && strings.Contains(k, "ufc.UfcBypassContext).GetServer") {
			continue
		}

		items := []string{}
		arr := strings.Split(k, " -> ")
		for _, v := range arr {
			// 函数名为函数指针在ssa中有特殊标记
			v = strings.ReplaceAll(v, "#", "")
			v = strings.ReplaceAll(v, "$1", "")

			// 获取icode对应代码库的根名称
			if strings.Contains(v, BAIDU_CODE_FLAG) {
				mName := getModuleName(v)
				if mName != "" {
					if _, ok := moduleNames[mName]; !ok {
						moduleNames[mName] = struct{}{}
					}
				}
			}

			items = append(items, v)
		}

		path := strings.Join(items, ",")
		if _, ok := uniqStack[path]; !ok {
			uniqStack[path] = struct{}{}
			datas = append(datas, items)
		}
	}

	return datas
}

func getMuduleInfo(moduleName string) (ModuleInfo, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	var moduleInfo ModuleInfo
	defer cancel()

	cmd := exec.CommandContext(ctx, "bash", "-c", "go list -m -json "+moduleName)
	out, err := cmd.Output()

	if err != nil {
		return moduleInfo, err
	}

	if ctx.Err() == context.DeadlineExceeded {
		return moduleInfo, ctx.Err()
	}

	err = json.Unmarshal(out, &moduleInfo)
	if err != nil {
		return moduleInfo, err
	}

	return moduleInfo, nil
}
