package main

import (
	"bytes"
	"errors"
	"flag"
	"fmt"
	"go/ast"
	"go/format"
	"go/parser"
	"go/token"
	"go/types"
	"net"
	"net/http"
	"os"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/bitly/go-simplejson"
	"golang.org/x/tools/go/ssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

const (
	BAIDU_CODE_FLAG = "icode.baidu.com/"
)

type FunStack struct {
	FileName string
	Line     int
	Match    string
	Froms    []string
	Targets  []string
	Begin    int
	End      int
	Path     string
	MaxDepth int
	Detail   bool
	SsaCache bool
}

type ModuleInfo struct {
	Name    string
	Path    string
	Version string
	Dir     string
}

type Basic struct {
	Name        string
	Path        string
	PackageName string
	Version     string
	Module      string
	TypeName    string
}

type Prototypes struct {
	Name     string
	TypeInfo string
}

type VscodeData struct {
	FileName         string
	Line             int
	Function         Basic        `json:",omitempty"`
	Receiver         Basic        `json:",omitempty"`
	ParamPrototypes  []Prototypes `json:",omitempty"`
	ParamTypes       []Basic      `json:",omitempty"`
	ReturnPrototypes []Prototypes `json:",omitempty"`
	ReturnTypes      []Basic      `json:",omitempty"`
}

var ssaFunc2Decl map[string]*FunDecl
var funStepCall map[string][]*ssa.Call
var funStrSsaFun map[string]*ssa.Function
var result map[string]struct{}
var funSsaInfo map[string]*VscodeData

// 调用堆栈中的icode.baidu.com开头的名称
var moduleNames map[string]struct{}
var moduleInfo map[string]ModuleInfo

var fileImportList map[string][]string
var fileList []string

var wg sync.WaitGroup
var port string

func (fs *FunStack) getFuncDel() string {
	fset := token.NewFileSet()
	f, err := parser.ParseFile(fset, fs.FileName, nil, 0)
	if err != nil {
		return ""
	}

	beginStr := f.Name.Name
	endStr := ""

	ast.Inspect(f, func(x ast.Node) bool {
		if fun, ok := x.(*ast.FuncDecl); ok {
			if fs.Line == fset.Position(x.Pos()).Line {
				if fun.Recv.NumFields() == 0 {
					endStr = beginStr + "." + fun.Name.Name
					return true
				}

				for _, v := range fun.Recv.List {
					if val, ok := v.Type.(*ast.StarExpr); ok {
						if value, ok := val.X.(*ast.Ident); ok {
							endStr = beginStr + "." + value.Name + ")." + fun.Name.Name
						}
					}
					if val, ok := v.Type.(*ast.Ident); ok {
						endStr = beginStr + "." + val.Name + "." + fun.Name.Name
					}
				}
			}
		}
		return true
	})

	return endStr
}

func (fs *FunStack) checkParam(w http.ResponseWriter, r *http.Request) bool {
	r.ParseForm()
	line, _ := strconv.ParseInt(r.Form.Get("line"), 10, 64)
	fileName := r.Form.Get("name")
	path := r.Form.Get("path")

	if line <= 0 || len(fileName) == 0 || len(path) == 0 {
		v := outputToConsole(1, 0, "Need line not 0 and name not null. Use ./stack -h ", []interface{}{})
		w.WriteHeader(400)
		w.Write(v)
		return false
	}

	fs.FileName = fileName
	fs.Line = int(line)
	fs.Path = path

	fs.Match = fs.getFuncDel()
	if len(fs.Match) == 0 {
		v := outputToConsole(2, 0, "Not find funcdel at line num, Check name param and line param", []interface{}{})
		w.WriteHeader(400)
		w.Write(v)
		return false
	}

	if len(r.Form.Get("detail")) != 0 {
		fs.Detail = true
	}
	if len(r.Form.Get("cache")) != 0 {
		fs.SsaCache = true
	}

	depthStr := r.Form.Get("depth")
	fs.MaxDepth, _ = strconv.Atoi(depthStr)

	if fs.Detail {
		fs.MaxDepth = 1
	}

	return true
}

func outputToConsole(errno, hit int, msg string, datas []interface{}) []byte {
	res := simplejson.New()
	res.Set("errno", errno)
	res.Set("msg", msg)
	res.Set("hit", hit)
	res.Set("count", len(datas))
	res.Set("data", datas)

	if errno == 0 {
		res.Set("info", funSsaInfo)
	} else {
		res.Set("info", map[string]*VscodeData{})
	}

	v, _ := res.Encode()
	return v

	// result, _ := prettyString(string(v))
	// fmt.Println(result)
}

/*
func prettyString(str string) (string, error) {
	var prettyJSON bytes.Buffer
	if err := json.Indent(&prettyJSON, []byte(str), "", "    "); err != nil {
		return "", err
	}
	return prettyJSON.String(), nil
}*/

func getSsaName(fun *ast.FuncDecl, file string) (string, error) {
	end := strings.LastIndex(file, "/")
	retPrefix := file[:end]

	if fun.Recv != nil && fun.Recv.List != nil {
		for _, v := range fun.Recv.List {
			if val, ok := v.Type.(*ast.StarExpr); ok {
				if value, ok := val.X.(*ast.Ident); ok {
					retPrefix = "(*" + retPrefix + "." + value.Name + ")." + fun.Name.Name
				}
			}
			if val, ok := v.Type.(*ast.Ident); ok {
				retPrefix = "(" + retPrefix + "." + val.Name + ")." + fun.Name.Name
			}
		}
	} else {
		retPrefix += "." + fun.Name.Name
	}

	return retPrefix, nil
}

func formatNode(node ast.Node) string {
	buf := new(bytes.Buffer)
	if err := format.Node(buf, token.NewFileSet(), node); err != nil {
		return ""
	}

	return strings.Split(buf.String(), "\n")[0]
}

func (fs *FunStack) getItemInfo(vsData *VscodeData, v *FunDecl, item string) error {
	vsData.FileName = v.FileName
	vsData.Line = v.Line

	// 是否需要详细的receiver & params & return等信息
	if !fs.Detail {
		return nil
	}

	// Function信息
	begin := strings.LastIndex(item, ".")
	vsData.Function.Name = item[(begin + 1):]
	vsData.Function.Module = getModuleName(item)

	if len(vsData.Function.Module) == 0 {
		return errors.New("vsData.Function.Module error")
	}

	ssaFun, ok := funStrSsaFun[item]
	if !ok {
		return errors.New("funStrSsaFun server error")
	}

	if info, ok := moduleInfo[vsData.Function.Module]; ok {
		// path为import信息
		vsData.Function.Path = getFuncImportInfo(item)
		vsData.Function.Version = info.Version
		vsData.Function.PackageName = ssaFun.Pkg.Pkg.Name()
	}

	// Receiver信息 函数Receive和Function在package、module、path、version暂时按照一致对待。
	// func (fs *FunStack) buildSSA() (*ssa.Program, bool) {
	if v.FunAstFun.Recv != nil {
		for _, recv := range v.FunAstFun.Recv.List {
			if recv.Names[0] != nil {
				vsData.Receiver.Name = recv.Names[0].Name

				if star, ok := recv.Type.(*ast.StarExpr); ok {
					if ident, ok := star.X.(*ast.Ident); ok {
						vsData.Receiver.TypeName = "*" + ident.Name
					}
				}
				if ident, ok := recv.Type.(*ast.Ident); ok {
					vsData.Receiver.TypeName = ident.Name
				}
			}
		}
	}

	vsData.Receiver.Module = vsData.Function.Module
	vsData.Receiver.PackageName = ssaFun.Pkg.Pkg.Name()
	vsData.Receiver.Path = vsData.Function.Path
	vsData.Receiver.Version = vsData.Function.Version

	file := ssaFun.Prog.Fset.File(ssaFun.Pos())
	importList := fileImportList[file.Name()]

	// 函数参数相关数据
	protosP, basicsP, err := getFuncResult(ssaFun.Signature.Params(), importList, ssaFun.Pkg.Pkg.Name(), v)
	if err != nil {
		return err
	}

	protosP = replaceEllipsis(v.FunAstFun, protosP)
	vsData.ParamPrototypes = protosP
	vsData.ParamTypes = basicsP

	// 函数返回值相关数据
	protosR, basicsR, err := getFuncResult(ssaFun.Signature.Results(), importList, ssaFun.Pkg.Pkg.Name(), v)
	if err != nil {
		return err
	}

	protosR = replaceEllipsis(v.FunAstFun, protosR)
	vsData.ReturnPrototypes = protosR
	vsData.ReturnTypes = basicsR

	return nil
}

// 遍历语法树，找到可变参数的地方，进行符号替换。（ssa直接翻译为了切片[]，需要替换为省略号...）
func replaceEllipsis(astFun *ast.FuncDecl, protos []Prototypes) []Prototypes {
	protosEll := protos

	ast.Inspect(astFun, func(x ast.Node) bool {
		if fun, ok := x.(*ast.FuncDecl); ok {
			for _, param := range fun.Type.Params.List {
				// 获取到可变参数部分
				if _, ok := param.Type.(*ast.Ellipsis); ok {
					for k, proto := range protos {
						if len(param.Names) > 0 {
							if param.Names[0].Name == proto.Name {
								protosEll[k].TypeInfo = strings.ReplaceAll(proto.TypeInfo, "[]", "...")
							}
						}
					}
				}
			}
		}
		return true
	})

	return protosEll
}

// 存在一个函数部分参数方法名相同但包不同的case、同一个代码库不同包定义了相同结构体，遇到这种情况按无法获取处理。
func getPackageName(funcProto, typeinfo, dir string) (string, error) {
	arr := strings.Split(funcProto, " ")

	for k, v := range arr {
		v = strings.ReplaceAll(v, "(", "")
		v = strings.ReplaceAll(v, ")", "")
		arr[k] = v

		// 包名和路径名不同时，得扫下对应代码库，获取真实包名。
		// 不能使用packageAndMethodA[0]直接返回，因为里面还包含其他。如*map[string]aaa.bbb, 应该只返aaa，不能*map[string]
		if strings.Contains(v, ".") {
			packageAndMethodA := strings.Split(v, ".")
			packageAndMethodB := strings.Split(typeinfo, ".")

			if (packageAndMethodA[1] == packageAndMethodB[1]) && (packageAndMethodA[0] != packageAndMethodB[0]) {
				packageName := getStructTypeInfo(packageAndMethodB[1], dir)
				if len(packageName) == 0 {
					return "", errors.New("get struct type error")
				}

				return packageName, nil
			}
		}
	}

	return "", nil
}

// 理想态:
// a = []map[string]*golang.org/x/tools/go/ssa.Alloc
// b = golang.org/x/tools/go/ssa
// 实际b = aa/x/b/c/ssa
// 文本相似度实现，也不准确。
func checkMatch(a, b string) bool {
	aArr := strings.Split(a, "/")
	bArr := strings.Split(b, "/")
	aLen := len(aArr)
	bLen := len(bArr)

	if aLen == bLen {
		tmp := strings.Split(aArr[aLen-1], ".")
		if len(tmp) > 0 && tmp[0] == bArr[bLen-1] {
			return true
		}
	}

	return false
}

func getFuncResult(ssaFun *types.Tuple, importList []string, name string, v *FunDecl) ([]Prototypes, []Basic, error) {
	protos := []Prototypes{}
	basics := []Basic{}

	for i := 0; i < ssaFun.Len(); i++ {
		nameInfo := ssaFun.At(i).Name()
		typeInfo := ssaFun.At(i).Type().String()

		proto := Prototypes{
			Name: nameInfo,
		}

		// 通过/符号认为是包含着诸如结构体的复杂内置类型。contains包含相关/判断，index后不用判断。
		// 使用import信息进行遍历，找到path，做相关替换；否则认为是本地包。
		if strings.Contains(typeInfo, "/") {
			for _, importL := range importList {
				if strings.Contains(importL, "$") {
					importL = strings.Split(importL, "$")[1]
				}

				importL = strings.ReplaceAll(importL, "\"", "")

				if checkMatch(typeInfo, importL) {
					index1 := strings.LastIndex(importL, "/")
					typeInfo = strings.ReplaceAll(typeInfo, importL[:index1+1], "")
					index2 := strings.LastIndex(typeInfo, ".")

					info := ModuleInfo{}
					var err error
					if strings.Contains(importL, BAIDU_CODE_FLAG) {
						info, err = getMuduleInfoFromPackage(importL)
						if err != nil {
							return []Prototypes{}, []Basic{}, errors.New("getMuduleInfoFromPackage error")
						}
					}

					basic := Basic{
						Name:        nameInfo,
						TypeName:    importL[(index1+1):] + typeInfo[index2:],
						Path:        importL,
						PackageName: "",
						Version:     "",
						Module:      "",
					}

					basic.Module = info.Path
					basic.Version = info.Version

					packageName, err := getPackageName(v.FuncPrototype, typeInfo, info.Dir)
					if err != nil {
						return []Prototypes{}, []Basic{}, errors.New("getPackageName error")
					}

					// 替换特殊case：pathName != import path
					if len(packageName) != 0 {
						basic.PackageName = packageName
						basic.TypeName = packageName + typeInfo[index2:]
						typeInfo = strings.ReplaceAll(typeInfo, importL[(index1+1):], packageName)
					} else {
						basic.PackageName = importL[(index1 + 1):]
					}

					// 暂时硬编码先让业务可用，稍后系统性想下改进方法
					basic.PackageName = strings.ReplaceAll(basic.PackageName, "easy-go-sdk", "easy")
					basic.TypeName = strings.ReplaceAll(basic.TypeName, "easy-go-sdk", "easy")

					basics = append(basics, basic)
				}
			}
		}

		// 不在import的参数类型，应该是自身包内; contains包含相关/判断，index后不用判断
		// typeInfo example: *icode.baidu.com/baidu/netdisk/easy-go-sdk.IEntity 或 icode.baidu.com/baidu/netdisk/easy-go-sdk.IEntity  实际为*easy.IEntity
		if strings.Contains(typeInfo, BAIDU_CODE_FLAG) {
			begin := strings.Index(typeInfo, BAIDU_CODE_FLAG)
			end := strings.LastIndex(typeInfo, "/")
			arr := strings.Split(typeInfo[end+1:], ".")
			// 如果有分隔符代表是包名.结构体名，此类case下取包名，没有分隔符代表自身包，直接取
			path := typeInfo[begin:end+1] + arr[0]

			// 获取*=typeInfo[:begin]
			prefix := typeInfo[:begin]
			typeInfo = prefix + typeInfo[end+1:]
			index := strings.LastIndex(typeInfo, ".")
			typeName := name + typeInfo[index:]
			typeInfo = prefix + typeName

			info, err := getMuduleInfoFromPackage(path)
			if err != nil {
				return []Prototypes{}, []Basic{}, errors.New("getMuduleInfoFromPackage BAIDU_CODE_FLAG error")
			}

			basic := Basic{
				Name:        nameInfo,
				TypeName:    typeName,
				Path:        path,
				PackageName: name,
				Version:     "",
				Module:      "",
			}

			basic.Module = info.Path
			basic.Version = info.Version
			basics = append(basics, basic)
		}

		if strings.Contains(typeInfo, "/") {
			return []Prototypes{}, []Basic{}, errors.New("analysis typeInfo error")
		}

		proto.TypeInfo = strings.ReplaceAll(typeInfo, "easy-go-sdk", "easy")
		protos = append(protos, proto)
	}

	return protos, basics, nil
}

func getMuduleInfoFromPackage(importL string) (ModuleInfo, error) {
	times := 0
	end := 0
	max := 0

	module := ModuleInfo{}
	var err error

	module, err = getMuduleInfo(importL)
	if err == nil {
		return module, nil
	}

	// 当前import路径的
	arr := strings.Split(importL, "/")
	if len(arr) > 8 {
		max = 8
	} else {
		max = len(arr)
	}

	for times < max {
		end = strings.LastIndex(importL, "/")
		if end < 0 {
			break
		}

		module, err = getMuduleInfo(importL[:end])
		if err == nil {
			break
		}

		importL = importL[:end]
		times++
	}

	return module, err
}

func GetAvailablePort() (string, error) {
	address, err := net.ResolveTCPAddr("tcp", fmt.Sprintf("%s:0", "0.0.0.0"))
	if err != nil {
		return "", err
	}

	listener, err := net.ListenTCP("tcp", address)
	if err != nil {
		return "", err
	}

	defer listener.Close()
	port := listener.Addr().(*net.TCPAddr).Port
	return strconv.FormatInt(int64(port), 10), nil
}

var path = flag.String("path", "", "Must input absolute path of the go project, example: /home/<USER>/icode.baidu.com/baidu/netdisk/pcs-go-pcsapi")

func init() {
	flag.Parse()
	if len(*path) == 0 {
		return
	}

	fs := FunStack{}
	fs.Path = *path

	prog, bret, _ := fs.buildSSA(_getBuildSsaPath(fs))
	if bret {
		fs.buildCallGraph(prog)
	}
}

func _getBuildSsaPath(fs FunStack) string {
	funStepCall = nil
	funStrSsaFun = nil
	funStepCall = make(map[string][]*ssa.Call, 20480)
	funStrSsaFun = make(map[string]*ssa.Function, 20480)

	path := ""
	endIndex := strings.LastIndex(fs.FileName, "/")
	if endIndex != -1 {
		path = fs.FileName[0:endIndex]
	}

	return path
}

func main() {
	httpApiServ := http.NewServeMux()
	httpApiServ.HandleFunc("/rest/2.0/pvcp/analysis", Analysis)
	httpApiServ.HandleFunc("/rest/2.0/pvcp/info", Info)

	randomPort, err := GetAvailablePort()
	if err != nil {
		return
	}

	port = randomPort
	fmt.Println(port)
	http.ListenAndServe("127.0.0.1:"+port, httpApiServ)
}

func (fs *FunStack) getFsFroms() {
	var filePathName string
	for _, fn := range funStrSsaFun {
		lineNum := fn.Prog.Fset.Position(fn.Pos()).Line
		file := fn.Prog.Fset.File(fn.Pos())

		if file != nil {
			filePathName = file.Name()
		} else {
			filePathName = ""
		}
		// 针对函数指针嵌套等情况 lineNum == fs.Line + 1
		if (lineNum == fs.Line || lineNum == fs.Line+1) && (fs.FileName == filePathName) {
			fs.Froms = append(fs.Froms, fn.String())
		}
	}
}

func Info(w http.ResponseWriter, r *http.Request) {
	pid := os.Getpid()
	res := simplejson.New()
	res.Set("errno", 0)
	res.Set("msg", "success")
	res.Set("pid", strconv.FormatInt(int64(pid), 10))
	res.Set("port", port)

	v, _ := res.Encode()
	w.WriteHeader(200)
	w.Write(v)
}

func (fs *FunStack) CdCurrentPath() error {
	dir, err := os.Getwd()
	if err != nil {
		return err
	}

	if dir != fs.Path {
		err := os.Chdir(fs.Path)
		if err != nil {
			return err
		}
	}

	return nil
}

func Analysis(w http.ResponseWriter, r *http.Request) {
	begin := strconv.FormatInt(time.Now().Unix(), 10)

	fs := FunStack{}
	ret := fs.checkParam(w, r)
	if !ret {
		return
	}

	defer func() {
		if x := recover(); x != nil {
			msg := string(debug.Stack())
			util.SendWarnMessageToHi("[fs:" + string(fs.FileName) + "][recover err:]" + msg)
			v := outputToConsole(4, 0, "Anlysis error "+"[fs:"+string(fs.FileName)+"][recover err:]"+msg, []interface{}{})
			w.WriteHeader(500)
			w.Write(v)
			return
		}
	}()

	err := fs.CdCurrentPath()
	if err != nil {
		v := outputToConsole(3, 0, "CdCurrentPath error "+"[fs:"+string(fs.FileName)+"[error:]"+err.Error(), []interface{}{})
		w.WriteHeader(500)
		w.Write(v)
		return
	}

	fileImportList = make(map[string][]string, 1024)

	// 调用拓扑分支数、ssa函数和ast对应关系
	result = make(map[string]struct{}, 64)
	ssaFunc2Decl = make(map[string]*FunDecl, 10240)

	// 函数返回参数和细节
	funSsaInfo = make(map[string]*VscodeData, 256)
	moduleNames = make(map[string]struct{}, 64)
	moduleInfo = make(map[string]ModuleInfo, 64)

	if !fs.SsaCache || len(funStepCall) == 0 || len(funStrSsaFun) == 0 {
		// 一个代码库每个函数的调用拓扑、源函数指针。
		prog, bret, v := fs.buildSSA(_getBuildSsaPath(fs))
		if prog == nil || !bret {
			w.WriteHeader(500)
			w.Write(v)
			return
		}

		fs.buildCallGraph(prog)
	}

	// fmt.Println("before getFsFroms ", len(fs.Froms))
	// 预建立ssa时未赋值
	if len(fs.Froms) == 0 {
		fs.getFsFroms()
	}

	// fmt.Println("getFsFroms ", len(fs.Froms))

	mid := strconv.FormatInt(time.Now().Unix(), 10)
	for _, fromFun := range fs.Froms {
		if funInstrs, ok := funStepCall[fromFun]; ok {
			for _, funInstr := range funInstrs {
				curFun := funInstr.Call.StaticCallee().String()

				wg.Add(1)
				go fs.funcCallStackRecv(curFun, fromFun, 1, funInstr, nil)
			}
		}
	}

	wg.Wait()

	// 获取目标函数
	line := strconv.FormatInt(int64(fs.Line), 10)
	datas := fs.getStackInfo()

	if len(datas) == 0 {
		util.SendWarnMessageToHi("[fs:" + string(fs.FileName) + "][line:]" + line)
		v := outputToConsole(9, 0, "Get target funs error "+"[fs:"+string(fs.FileName)+"][line:"+line+"]", []interface{}{})
		w.WriteHeader(500)
		w.Write(v)
		return
	}

	// 建立icode代码库路径到在本地的实际路径、版本号等信息的映射
	for name := range moduleNames {
		info, err := getMuduleInfo(name)
		if err != nil {
			util.SendWarnMessageToHi("[fs:" + name + "][err:]" + err.Error() + "[line:]" + line)
			v := outputToConsole(5, 0, "Get module error "+err.Error()+" name "+name, []interface{}{})
			w.WriteHeader(500)
			w.Write(v)
			return
		}

		moduleInfo[name] = info
	}

	dir, err := os.Getwd()
	if err != nil {
		v := outputToConsole(12, 0, "Getwd error "+"[fs:"+string(fs.FileName)+"[error:]"+err.Error(), []interface{}{})
		w.WriteHeader(500)
		w.Write(v)
		return
	}

	// 针对分支函数对应的代码库进行语法树扫描，建立每个函数ast和ssa的对应。
	for name, info := range moduleInfo {
		fileList = []string{}
		// 对于软链接情况，需要再次赋值，上面os.Chdir不会生效。
		if (info.Dir == dir) && (dir != fs.Path) {
			info.Dir = fs.Path
		}

		err := getFileList(info.Dir)
		if err != nil {
			util.SendWarnMessageToHi("[dir:" + info.Dir + "][recover err:]" + err.Error() + "[line:]" + line)
			v := outputToConsole(6, 0, "Get getFileList error "+err.Error(), []interface{}{})
			w.WriteHeader(500)
			w.Write(v)
			return
		}

		if len(fileList) > 0 {
			err := astAnalysis(fileList, name, info.Dir)
			if err != nil {
				util.SendWarnMessageToHi("[fs:" + info.Dir + "][recover err:]" + err.Error() + "[line:]" + line)
				v := outputToConsole(7, 0, "Get astAnalysis error "+err.Error(), []interface{}{})
				w.WriteHeader(500)
				w.Write(v)
				return
			}
		}
	}

	for _, data := range datas {
		for _, item := range data.([]string) {
			if v, ok := ssaFunc2Decl[item]; ok {
				if _, ok := funSsaInfo[item]; !ok {
					vsData := &VscodeData{}
					err := fs.getItemInfo(vsData, v, item)
					if err != nil {
						util.SendWarnMessageToHi("[fs:" + fs.FileName + "][recover err:]" + err.Error() + "[line:]" + line)
						v := outputToConsole(8, 0, "Get getItemInfo error "+err.Error(), []interface{}{})
						w.WriteHeader(500)
						w.Write(v)
						return
					}

					funSsaInfo[item] = vsData
				}
			}
		}
	}

	end := strconv.FormatInt(time.Now().Unix(), 10)
	pvTmp := "[fs:" + fs.FileName + "][begin:" + begin + "][mid:" + mid + "][end:" + end + "][cache:" + strconv.FormatBool(fs.SsaCache) + "][detail:"
	pv := pvTmp + strconv.FormatBool(fs.Detail) + "]" + "[line:" + line + "][depth:" + strconv.FormatInt(int64(fs.MaxDepth), 10) + "][analysis success]"
	util.SendWarnMessageToHi(pv)

	hit := 0
	if len(datas) > 1 {
		hit = len(datas)
	}
	v := outputToConsole(0, hit, "success", datas)
	w.WriteHeader(200)
	w.Write(v)
}
