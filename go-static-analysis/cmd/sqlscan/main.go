package main

import (
	"fmt"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

func main() {
	scanner := sqlscan.NewSQLScanner()
	if err := scanner.Scan(); err != nil {
		fmt.Println("scan err: ", err)
		return
	}

	if err := scanner.SendResultToServer(); err != nil {
		golog.Error("send result to server failed, err: %v", err)
		return
	}
	golog.Warn("send result to server success")
}
