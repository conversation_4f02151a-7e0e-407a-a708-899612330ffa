package main

import (
	"fmt"
	"os"
	"sync"
	"time"

	"golang.org/x/tools/go/analysis/multichecker"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/extension/base"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/followedby"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/ifnil"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/ifnil/ifnilutil"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/nocomments"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/parameter"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/reverse"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/unfreed"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/unfreed/unfreedutil"
)

var (
	outputFormat string
)

func init() {
	tmp := os.Getenv("OFFLINE_TOOL")
	if tmp == "true" {
		outputFormat = "json"
	} else {
		outputFormat = "text"
	}
}

func main() {
	args := os.Args
	if len(args) > 1 && args[1] == "version" {
		fmt.Println(base.VERSION)
		return
	}

	dir, err := os.Getwd()
	if err != nil {
		return
	}

	t := 1 * time.Minute
	go func() {
		select {
		case <-time.After(t):
			base.SendWarnMessageToHi("This program is timeout! dir: " + dir)
			os.Exit(-1)
		}
	}()

	wg := &sync.WaitGroup{}
	wg.Add(2)
	func() {
		go func() {
			defer wg.Done()
			unfreed.Run(&unfreedutil.Option{OutputFormat: outputFormat})
		}()

		go func() {
			defer wg.Done()
			ifnil.Run(&ifnilutil.Option{OutputFormat: outputFormat})
		}()
	}()
	wg.Wait()

	multichecker.Main(
		followedby.Analyzer,
		reverse.Analyzer,
		parameter.Analyzer,
		nocomments.Analyzer,
	)
}
