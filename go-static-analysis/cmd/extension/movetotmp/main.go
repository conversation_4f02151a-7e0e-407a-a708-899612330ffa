package main

import (
	"errors"
	"flag"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/extension/base"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

var (
	isJSON       = flag.Bool("output-json", false, "output json format")
	outputFormat string
)

func main() {
	// 检查二进制文件是否存在以及是否最新，如果不存在/非最新，则下载/更新
	args := os.Args
	if len(args) > 1 && args[1] == "version" {
		fmt.Println(base.VERSION)
		return
	}

	var ctx *context = &context{}
	if err := ctx.init(); err != nil {
		msg := setSendHiMsg("ctx.init", "", err.Error())
		base.SendWarnMessageToHi(msg)
		return
	}

	if err := ctx.checkBinary(); err != nil {
		msg := setSendHiMsg("checkBinary", "", err.Error())
		base.SendWarnMessageToHi(msg)
		return
	}

	// 拷贝当前目录到 /tmp 目录下
	if err := ctx.moveCwdToTmp(); err != nil {
		msg := setSendHiMsg("moveCwdToTmp", "", err.Error())
		base.SendWarnMessageToHi(msg)
		return
	}

	if err := ctx.run(); err != nil {
		msg := setSendHiMsg("run()", "", err.Error())
		base.SendWarnMessageToHi(msg)
	}
}

func (c *context) run() error {
	// 当存在检测结果时（输入stderr)，以下 err 为：exit status 3, output 为具体的执行输出信息，因此将不予以检测
	output, _ := execScript(scriptAnalysis, c.cwd, c.preBinPath, c.runBinPath)
	s := c.transDir(output)    // 字符串替换
	fmt.Fprintln(os.Stderr, s) // 让 ts 侧进行过滤

	msg, err := c.ColloectIgnoreFile()
	if err != nil {
		return err
	}

	base.SendWarnMessageToHi(msg)

	if err := c.removeCwd(); err != nil {
		return err
	}

	return nil
}

func (c *context) ColloectIgnoreFile() (string, error) {
	suffix := "ignored.txt"

	cwd := c.cwd
	if len(cwd) == 0 {
		return "", errors.New("cwd len is 0")
	}

	fileName := ""
	if cwd[len(cwd)-1] == '/' {
		fileName = cwd + suffix
	} else {
		fileName = cwd + "/" + suffix
	}

	f, err := os.Open(fileName)
	if err != nil {
		return "", err
	}
	defer f.Close()

	body, err := io.ReadAll(f)
	if err != nil {
		return "", err
	}
	content := string(body)
	slice := strings.Split(content, "\n")

	output := fmt.Sprintf("the len(ignoreFile) is: %d\n, the dir is %s\n", len(slice), cwd)

	msg := setSendHiMsg("CollectIgnoreFile", output, "")
	return msg, nil
}

// 拷贝到 /tmp 下后的 GoMod, GoPath
type context struct {
	cwd    string
	repo   string
	prefix string // 当前代码库所在目录

	goPath     string
	preBinPath string // 预先跑一遍
	runBinPath string //
}

func (c *context) init() error {
	cwd, err := os.Getwd()
	if err != nil {
		return err
	}
	c.cwd = cwd

	repo := filepath.Base(cwd)
	c.repo = repo

	prefix := strings.TrimSuffix(cwd, repo) // a/b/c/
	c.prefix = prefix

	if c.cwd[len(c.cwd)-1] != '/' {
		c.cwd += "/"
	}

	// 检查不通过，就不再往下执行了
	if !checkGoPath() {
		return errors.New("function: checkGoPath, gopath hasPrefix /tmp")
	}

	// 用于检测是否为 go mod 格式
	_, err = execScript(`go mod edit -json`)
	if err != nil {
		return errors.New("this is not go mod!")
	}

	goPath, err := getGoPath()
	if err != nil {
		return err
	}
	c.goPath = goPath

	return nil
}

var scriptCopyToTmp = `
src=$1
dest=$2

cp -rf $src $dest
`

func (c *context) moveCwdToTmp() error {
	cwd, err := os.Getwd()
	if err != nil {
		return err
	}

	src := cwd
	dest := "/tmp/golang-sa/"

	if !IsDir(dest) {
		if err := os.MkdirAll(dest, os.ModePerm); err != nil {
			return err
		}
	}

	goPath := "/tmp/golang-sa/gopath"
	if !IsDir(goPath) {
		if err := os.MkdirAll(goPath, os.ModePerm); err != nil {
			return err
		}
	}

	output, err := execScript(scriptCopyToTmp, src, dest)
	if err != nil {
		return errors.New(output)
	}

	curCwd := dest + c.repo
	c.cwd = curCwd
	return nil
}

func IsDir(path string) bool {
	stat, err := os.Stat(path)
	if err != nil {
		return false
	}

	return stat.IsDir()
}

var runBinName = "pvcp_sa_extension"
var preBinName = "pre"

var binNameToInstallName = map[string]string{
	runBinName: "go install icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/extension/pvcp_sa_extension@latest",
	preBinName: "go install icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/pre@latest",
}

func (c *context) checkBinary() error {
	if err := c.checkRunBinName(); err != nil {
		return err
	}

	if err := c.checkPreBinName(); err != nil {
		return err
	}

	return nil
}

func (c *context) checkRunBinName() error {
	ok, err := c.checkBinPathIsExist(runBinName)
	if err != nil {
		// 此时 GoPath 不存在
		return err
	}

	if !ok {
		// 此时 pvcp_sa_extension 二进制文件不存在
		if err := c.installBinName(runBinName); err != nil {
			return err
		}
	}

	// 安装了以后应存在，再 check 下
	ok, err = c.checkBinPathIsExist(runBinName)
	if err != nil || !ok {
		errString := "go install run_bin_name success, but function checkBinPathIsExist() failed. "
		return errors.New(errString)
	}

	goPath, err := getGoPath()
	if err != nil {
		return err
	}
	absName := goPath + "/bin/" + runBinName
	c.runBinPath = absName

	// 二进制文件存在，但要进行更新
	if err := c.updateRunBinNameOrNot(); err != nil {
		return err
	}

	return nil
}

func (c *context) installBinName(binName string) error {
	script := binNameToInstallName[binName]
	_, err := execScript(script)
	if err != nil {
		return err
	}

	return nil
}

func (c *context) updateRunBinNameOrNot() error {
	script := c.runBinPath + " version"
	localVersion, err := execScript(script)
	if err != nil {
		msg := fmt.Sprintf("script: %s exec failed, and error is %s", script, err.Error())
		return errors.New(msg)
	}

	remoteVersion, err := c.getRemoteVersion()
	if err != nil {
		return err
	}

	localVersion = strings.TrimSpace(localVersion)
	remoteVersion = strings.TrimSpace(remoteVersion)

	if localVersion != remoteVersion {
		return c.installBinName(runBinName)
	}

	return nil
}

func (c *context) getRemoteVersion() (string, error) {
	url := "http://*************:8098/pvcp/extension/version"
	var connectTimeoutMs time.Duration = 1000    //1000ms
	var readwriteTimeoutMs time.Duration = 10000 //10000ms
	response, err := httpclient.Get(url, nil, connectTimeoutMs, readwriteTimeoutMs, nil)
	if err != nil {
		return "", err
	}

	body := response.Body
	return string(body), nil
}

func (c *context) checkPreBinName() error {
	ok, err := c.checkBinPathIsExist(preBinName)
	if err != nil {
		// gopath 不存在
		return err
	}

	if !ok {
		// pre 不存在
		if err := c.installBinName(preBinName); err != nil {
			return err
		}
	}

	// 这里 pre 不用更新
	// 安装了以后应存在，再 check 下
	ok, err = c.checkBinPathIsExist(preBinName)
	if err != nil || !ok {
		// 此时 GoPath 不存在
		// sendHi
		return err
	}

	goPath, err := getGoPath()
	if err != nil {
		return err
	}
	absName := goPath + "/bin/" + preBinName
	c.preBinPath = absName

	return nil
}

// 检查 gopath 是否含有 /tmp 目录下的东西
func checkGoPath() bool {
	const script = `go env GOPATH`
	output, err := execScript(script)
	if err != nil {
		return false
	}

	output = strings.TrimSpace(output)
	goPaths := strings.Split(output, ":")

	for _, goPath := range goPaths {
		if strings.HasPrefix(goPath, "/tmp/golang-sa/gopath") {
			return false
		}
	}

	return true
}

// 获取 第一个 gopath(go install 的位置)
func getGoPath() (string, error) {
	output, err := execScript(`go env GOPATH`)
	if err != nil {
		return output, err
	}

	output = strings.TrimSpace(output)
	tmp := strings.Split(output, ":")
	if len(tmp) > 0 {
		return tmp[0], nil
	}

	return "", errors.New("go env GOPATH returns nothing ")
}

// 检查 binpath 是否存在，不存在则进行更新
func (c *context) checkBinPathIsExist(binName string) (bool, error) {
	goPath, err := getGoPath()
	if err != nil {
		return false, err
	}

	absName := goPath + "/bin/" + binName
	if IsFile(absName) {
		return true, nil
	}

	return false, nil
}

func IsFile(fileName string) bool {
	f, err := os.Stat(fileName)
	if err != nil {
		return false
	}

	return !f.IsDir()
}

func execScript(script string, args ...string) (string, error) {
	cmd := exec.Command("/bin/bash", append([]string{"-s", "-"}, args...)...)
	wc, err := cmd.StdinPipe()
	if err != nil {
		return "", err
	}

	go func() {
		defer wc.Close()
		fmt.Fprintf(wc, "%s", script)
	}()

	output, err := cmd.CombinedOutput()
	return string(output), err
}

func setSendHiMsg(fn string, output string, errString string) string {
	userName, _ := execScript("git config --get user.name")
	userName = strings.TrimSpace(userName)

	msg := ""
	msg += "user name: " + userName + "\n"
	msg += "fn/command: " + fn + "\n"
	if output != "" {
		msg += "output: " + output + "\n"
	}
	msg += "err: " + errString + "\n"
	return msg
}

const tmpDir = "/tmp/golang-sa/"
const tmpGoPath = "/tmp/golang-sa/gopath"

func (c *context) transDir(result string) string {
	slice := strings.Split(result, "\n")
	res := ""
	for _, output := range slice {
		if strings.HasPrefix(output, tmpGoPath) {
			s := strings.TrimPrefix(output, tmpGoPath) // 去除GoPath前缀
			str := c.goPath + s
			res += str + "\n"
		} else if strings.HasPrefix(output, tmpDir) {
			s := strings.TrimPrefix(output, tmpDir) // 去除当前目录前缀
			str := c.prefix + s
			res += str + "\n"
		} else {
			res += output + "\n"
		}
	}
	return res
}

const scriptAnalysis = `
pre_bin_path=$2
run_bin_path=$3

cwd=$1
cd $cwd

export GOPATH=/tmp/golang-sa/gopath 
export GOMODCACHE=/tmp/golang-sa/gopath/pkg/mod
go mod tidy 

# ignore "unused" files
ignore_unused()
{
	while true ; do
		echo N >tmp.out

		# !!! the "while" is in another shell
		$pre_bin_path ./... 2>&1 | sed -n -E \
			-e 's/^([^:]*\.go):[0-9]+:[0-9]+: .*$/\1/p' | while read filename ; do
			if [[ "$filename" != "/tmp"* ]] ; then 
				break
			fi 

			if [[ -f "$filename" ]] && [[ "$filename" == "/tmp/golang-sa"* ]] ; then
				echo Y >tmp.out
				echo "$filename" >>ignored.txt
				mv -f "$filename" "${filename}.ignored"
			fi
		done

		if [[ "$(cat tmp.out)" == "N" ]] ; then
			break
		fi
	done
}

ignore_unused

$run_bin_path ./...
`

func (c *context) removeCwd() error {
	output, err := execScript(removeCwdScript, c.cwd)
	if err != nil {
		return errors.New(output)
	}

	return nil
}

const removeCwdScript = `
cwd=$1
cd $cwd

if [[ "$cwd" == "/tmp/golang-sa"* ]] ; then
	rm -rf $cwd
fi
`
