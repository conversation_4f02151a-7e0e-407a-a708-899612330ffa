package main

import (
	"os"
	"path/filepath"

	"golang.org/x/tools/go/analysis/singlechecker"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/reverse"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

var in bool

func init() {
	path := filepath.Dir(os.Args[0])
	absPath, err := filepath.Abs(path)
	if err != nil {
		return
	}
	in = util.IsInWhiteList(absPath, reverse.WhiteList)
}

func main() {
	if in {
		return
	}
	singlechecker.Main(reverse.Analyzer)
}
