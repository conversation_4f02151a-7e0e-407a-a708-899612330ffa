package parameter

import (
	"fmt"
	"go/token"
	"strconv"
	"strings"
	"sync"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/ssa"
)

type CGRelation struct {
	Instr *ssa.Call
	Fn    *ssa.Function
}

type CallGraph map[string][]CGRelation
type GlobalMapVar map[ssa.Value]*ssa.Value
type GlobalString map[string]string

var mutex sync.Mutex

const MAXDEEP = 16
const MAXSTACK = 1024

type MapData struct {
	Mapped     bool
	Vulnerable bool
	Count      int
}

type TaintedCode struct {
	SourceCode     string
	SourceFilename string
	SourceLineNum  int
	ParentFunction string
}

type TaintAnalyzer struct {
	taint_map   map[uint64]MapData
	TaintSource []TaintedCode
	pass        *analysis.Pass
	location    token.Pos
}

type KeyOrIndex struct {
	Key   ssa.Value
	Index ssa.Value
}

func CreateTaintAnalyzer(pass *analysis.Pass, location token.Pos) TaintAnalyzer {
	return TaintAnalyzer{
		make(map[uint64]MapData),
		[]TaintedCode{},
		pass,
		location,
	}
}

func AnalyzeFunction(cg CallGraph, gv GlobalMapVar, gs GlobalString, fn *ssa.Function, depth int) {
	if depth > MAXDEEP {
		return
	}

	depth++

	for _, block := range fn.DomPreorder() {
		for _, instr := range block.Instrs {
			switch instr := instr.(type) {
			case *ssa.Call:
				if instr.Call.StaticCallee() != nil {
					calleeName := instr.Call.StaticCallee().String()

					mutex.Lock()
					if len(cg[calleeName]) < MAXSTACK {
						if _, ok := cg[calleeName]; !ok {
							cg[calleeName] = append(cg[calleeName], CGRelation{instr, fn})
						}
					}
					mutex.Unlock()

					/*
						if _, ok := (instr.Call.Value).(*ssa.Function); ok {
							tmp := instr.Call.Value.(*ssa.Function)
							AnalyzeFunction(cg, gv, gs, tmp, depth)
						}*/
				}

			case *ssa.Store:
				mutex.Lock()
				gv[instr.Addr] = &(instr.Val)
				gs[instr.Addr.Name()] = instr.Val.Name()
				mutex.Unlock()
			}
		}
	}
}

func (ta *TaintAnalyzer) ContainsTaint(startCall *ssa.CallCommon, val *ssa.Value, cg CallGraph, threshold int64) (bool, int64) {
	_, srcResult := ta.ContainsTaintRecurse(startCall, val, val, cg, 0, []ssa.Value{}, nil)
	//log.Println("val string: ", (*val).String(), " result ", srcResult, " threshold: ", threshold)

	result := strings.ReplaceAll(srcResult, "\"", "")
	if result != "" {
		paramsMsg, err := strconv.ParseInt(result, 10, 64)
		if err == nil && paramsMsg > threshold {
			return true, paramsMsg
		}
	}

	return false, 0
}

// 相同逻辑的case不能合并。
// 由于多层递归先不拆解多个函数。
func (ta *TaintAnalyzer) ContainsTaintRecurse(startCall *ssa.CallCommon, val *ssa.Value, lastVal *ssa.Value, cg CallGraph, depth int, visitedMutable []ssa.Value, keyOrIndex *KeyOrIndex) (bool, string) {
	result := ""
	if *val == nil {
		return false, result
	}

	out := ""
	for i := 0; i < depth; i++ {
		out += "  "
	}

	//log.Printf("depth %d %s%s (%T) \n", depth, out, *val, *val)
	//log.Printf(" lastVal depth %d %s%s (%T) \n", depth, out, *lastVal, *lastVal)

	//防止无限循环遍历下去
	call, isCall := (*val).(*ssa.Call)
	if isCall {
		if startCall == &call.Call {
			return false, result
		}
	}
	for _, visitedVal := range visitedMutable {
		if *val == visitedVal {
			return false, result
		}
	}

	map_status1 := ta.taint_map[SSAvalToHash(val)]
	ta.Memoize(val, map_status1.Vulnerable)
	map_status := ta.taint_map[SSAvalToHash(val)]

	if map_status.Count > MAXDEEP {
		return false, result
	}
	if map_status.Mapped {
		//log.Printf("map_status.Mapped go there ")
		return map_status.Vulnerable, result
	}

	vulnerable := false
	switch expr := (*val).(type) {
	case *ssa.Const:
		if expr.Value != nil {
			result = expr.Value.String()
		} else {
			result = expr.Name()
		}

		return false, result

	case *ssa.Parameter:
		var values []*ssa.Value
		values = cg.ResolveParam(expr)

		if len(values) > 0 {
			bV, result := ta.ContainsTaintRecurse(startCall, values[0], val, cg, depth+1, visitedMutable, keyOrIndex)
			vulnerable = vulnerable || bV
			return vulnerable, result
		}

	case *ssa.FreeVar:
		if expr.String() != "" {
			result = expr.String()
		}
		return false, result

	case *ssa.Field:
		bV, result := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		vulnerable = vulnerable || bV
		return vulnerable, result

	case *ssa.Next:
		bV, result := ta.ContainsTaintRecurse(startCall, &expr.Iter, val, cg, depth+1, visitedMutable, keyOrIndex)
		vulnerable = vulnerable || bV
		return vulnerable, result

	case *ssa.TypeAssert:
		bV, result := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		vulnerable = vulnerable || bV
		return vulnerable, result

	case *ssa.Range:
		bV, result := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		vulnerable = vulnerable || bV
		return vulnerable, result

	case *ssa.Phi:
		mapping := MapData{Mapped: true, Vulnerable: false}
		ta.taint_map[SSAvalToHash(val)] = mapping

		for _, edge := range (*expr).Edges {
			if edge != expr {
				bV, result := ta.ContainsTaintRecurse(startCall, &edge, val, cg, depth+1, visitedMutable, keyOrIndex)
				vulnerable = vulnerable || bV
				return vulnerable, result
			}
		}

	case *ssa.UnOp:
		vulnerable, result = ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		return vulnerable, result

	case *ssa.BinOp:
		bX, sX := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		bY, sY := ta.ContainsTaintRecurse(startCall, &expr.Y, val, cg, depth+1, visitedMutable, keyOrIndex)
		return bX || bY, strconv.FormatInt(ParseOp(expr.Op.String(), sX, sY), 10)

	case *ssa.Extract:
		vulnerable, result := ta.ContainsTaintRecurse(startCall, &expr.Tuple, val, cg, depth+1, visitedMutable, keyOrIndex)
		return vulnerable, result

	case *ssa.Slice:
		valSlice := ssa.Slice(*expr)
		valSliceX := valSlice.X
		bX, sX := ta.ContainsTaintRecurse(startCall, &valSliceX, val, cg, depth+1, visitedMutable, keyOrIndex)
		return bX, sX

	case *ssa.Convert:
		vulnerable, result := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		return vulnerable, result

	case *ssa.ChangeType:
		vulnerable, result := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		return vulnerable, result

	case *ssa.MakeInterface:
		vulnerable, result := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		return vulnerable, result

	case *ssa.Lookup:
		keyOrIndex := &KeyOrIndex{
			Key: expr.Index,
		}
		bX, sX := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		bY, sY := ta.ContainsTaintRecurse(startCall, &expr.Index, val, cg, depth+1, visitedMutable, keyOrIndex)

		if sX != "" {
			return bX || bY, sX
		}
		if sY != "" {
			return bX || bY, sY
		}

	case *ssa.Index:
		bX, sX := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		bY, sY := ta.ContainsTaintRecurse(startCall, &expr.Index, val, cg, depth+1, visitedMutable, keyOrIndex)

		if sX != "" {
			return bX || bY, sX
		}
		if sY != "" {
			return bX || bY, sY
		}

	case *ssa.ChangeInterface:
		vulnerable, result := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		return vulnerable, result

	case *ssa.Call:
		if dest := expr.Common().StaticCallee(); dest != nil {
			returns := ReturnValues(dest)
			if len(returns) > 0 {
				for _, retval := range returns {
					if len(retval) > 0 {
						bV, sV := ta.ContainsTaintRecurse(startCall, &retval[0], val, cg, depth+1, visitedMutable, keyOrIndex)
						vulnerable = bV || vulnerable
						result = sV
					}
				}
			} else {
				for _, arg := range expr.Call.Args {
					bV, sV := ta.ContainsTaintRecurse(startCall, &arg, val, cg, depth+1, visitedMutable, keyOrIndex)
					vulnerable = bV || vulnerable
					result = sV
				}
			}
		} else {
			for _, arg := range expr.Call.Args {
				bV, sV := ta.ContainsTaintRecurse(startCall, &arg, val, cg, depth+1, visitedMutable, keyOrIndex)
				vulnerable = bV || vulnerable
				result = sV
			}
		}

		return vulnerable, result

	case *ssa.IndexAddr:
		vulnerable, result := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, &KeyOrIndex{Index: expr.Index})
		return vulnerable, result

		//todo解决map寻址问题
	case *ssa.MakeMap:
		items := []*ssa.Value{}
		operand_items := expr.Operands(items)

		refers := expr.Referrers()
		res := ""
		for _, refer := range *refers {
			switch refer := refer.(type) {
			case *ssa.MapUpdate:
				if keyOrIndex != nil {
					key := keyOrIndex.Key
					if key == nil {
						continue
					}
					if key.Type() != refer.Key.Type() {
						continue
					}

					c1, ok1 := key.(*ssa.Const)
					c2, ok2 := refer.Key.(*ssa.Const)
					if !ok1 || !ok2 {
						continue
					}

					if c1.Value == nil || c2.Value == nil {
						continue
					}

					if c1.Value.ExactString() == c2.Value.ExactString() {
						ok, tmp := ta.ContainsTaintRecurse(startCall, &refer.Value, val, cg, depth+1, visitedMutable, keyOrIndex)
						if !ok {
							res = tmp
						}
					}
				}
			}
		}

		if res != "" {
			return true, res
		}

		for operand_idx := range operand_items {
			_, sV := ta.ContainsTaintRecurse(startCall, operand_items[operand_idx], val, cg, depth+1, visitedMutable, keyOrIndex)
			if sV != "" {
				return false, sV
			}
		}

	case *ssa.FieldAddr:
		vulnerable, result := ta.ContainsTaintRecurse(startCall, &expr.X, val, cg, depth+1, visitedMutable, keyOrIndex)
		return vulnerable, result

	case *ssa.Alloc:
		alloc_refs := expr.Referrers()
		mapping := MapData{Mapped: true, Vulnerable: false}
		ta.taint_map[SSAvalToHash(val)] = mapping

		for alloc_item := range *alloc_refs {
			alloc_ref := (*alloc_refs)[alloc_item]

			switch instr := (alloc_ref).(type) {
			case *ssa.IndexAddr:
				if keyOrIndex == nil {
					continue
				}

				index := keyOrIndex.Index
				if index == nil {
					continue
				}

				c1, ok1 := index.(*ssa.Const)
				c2, ok2 := instr.Index.(*ssa.Const)
				if !ok1 || !ok2 {
					continue
				}
				if c1.Value == nil || c2.Value == nil {
					continue
				}
				if c1.Value.ExactString() != c2.Value.ExactString() {
					continue
				}

				for indexaddr_ref_idx := range *instr.Referrers() {
					indexaddr_ref := (*instr.Referrers())[indexaddr_ref_idx]
					switch instr2 := (indexaddr_ref).(type) {
					case *ssa.Store:
						bV, sV := ta.ContainsTaintRecurse(startCall, &instr2.Val, val, cg, depth+1, visitedMutable, keyOrIndex)
						if sV != "" {
							return bV, sV
						}
					}
				}
			case *ssa.FieldAddr:
				for _, ref := range *instr.Referrers() {
					expr, isStore := (ref).(*ssa.Store)
					if expr != nil && expr.Val != nil && isStore {
						newMutable := make([]ssa.Value, len(visitedMutable)+1)
						copy(newMutable, visitedMutable)
						newMutable = append(newMutable, *val)

						bV, sV := ta.ContainsTaintRecurse(startCall, &expr.Val, val, cg, depth+1, newMutable, keyOrIndex)
						if sV != "" {
							return bV, sV
						}
					}
				}
			}

			var items []*ssa.Value
			operand_items := alloc_ref.Operands(items)
			for operand_idx := range operand_items {
				_, sV := ta.ContainsTaintRecurse(startCall, operand_items[operand_idx], val, cg, depth+1, visitedMutable, keyOrIndex)
				if sV != "" {
					return false, sV
				}
			}
		}

	case *ssa.Global:
		/*test := GenerateTaintedCode(ta.pass, (*val).Parent(), (*val).Pos())
		log.Println("Global variable found: ", test.SourceCode, " In file ", test.SourceFilename, " Line: ", test.SourceLineNum)*/
		if _, ok := gv[*val]; ok {
			//log.Println("gv[*val] ok : ", gv[*val])
			return ta.ContainsTaintRecurse(startCall, gv[*val], val, cg, depth+1, visitedMutable, keyOrIndex)
		}
		if _, ok := gv[(*lastVal)]; ok {
			//log.Println("gv[*lastVal] ok : ", gv[*lastVal])
			return ta.ContainsTaintRecurse(startCall, gv[*lastVal], val, cg, depth+1, visitedMutable, keyOrIndex)
		}

	case nil:
		vulnerable = false
	default:
		vulnerable = true
	}

	ta.Memoize(val, vulnerable)
	return vulnerable, result
}

func ParseOp(op string, sX string, sY string) int64 {
	iX, _ := strconv.ParseInt(sX, 10, 64)
	iY, _ := strconv.ParseInt(sY, 10, 64)

	if op == "*" {
		return iX * iY
	} else if op == "+" {
		return iX + iY
	} else if op == "-" {
		return iX - iY
	} else if op == "/" {
		return iX / iY
	} else if op == "%" {
		return iX % iY
	} else {
		return 0
	}
}

func SSAvalToHash(val *ssa.Value) uint64 {
	b_arrayPointer := []byte(fmt.Sprintf("%v", *val))
	val_string := string(b_arrayPointer)
	if (*val).Parent() != nil {
		b_arrayParent := (*val).Parent().String()
		val_string += b_arrayParent
	}

	hash := HashString64(val_string)
	return hash
}

// ResolveParam returns the caller nodes of a parameter. This is used for tracing parameters back to their source.
func (cg CallGraph) ResolveParam(p *ssa.Parameter) []*ssa.Value {
	// Determine which argument we are in the parent function
	pFunc := p.Parent()
	pIdx := -1
	for i, arg := range pFunc.Params {
		if p.Pos() == arg.Pos() {
			pIdx = i
		}
	}
	// Find all the places the function is called
	callerNodes := make([]*ssa.Value, len(cg[pFunc.String()]))
	for i, rel := range cg[pFunc.String()] {
		callerNodes[i] = &rel.Instr.Call.Args[pIdx]
	}

	return callerNodes
}

// Memoize hashes an ssa.Value and then adds it to the Taint Map while updating the metadata
func (ta TaintAnalyzer) Memoize(val *ssa.Value, vulnerable bool) {
	switch (*val).(type) {
	case *ssa.Phi:
		// Don't want to memoize Phi nodes as recursion will then not check all edges
	default:
		hash := SSAvalToHash(val)
		map_status := ta.taint_map[hash]
		new_count := map_status.Count + 1
		mapping := MapData{Mapped: map_status.Mapped, Vulnerable: map_status.Vulnerable, Count: new_count}
		ta.taint_map[hash] = mapping
	}

}

type ReturnSet = []ssa.Value

// ReturnValues returns a set of the return values of the function
func ReturnValues(fn *ssa.Function) []ReturnSet {
	res := []ReturnSet{}

	for _, block := range fn.DomPreorder() {
		// a returning block ends in a Return instruction and has no successors
		if len(block.Succs) != 0 {
			continue
		}

		if ret, ok := block.Instrs[len(block.Instrs)-1].(*ssa.Return); ok {
			res = append(res, ret.Results[:])
		}
	}

	return res
}
