package parameter

type funcInfo struct {
	Name      string `json:"name"`
	Index     int    `json:"index"`
	Greater   int    `json:"greater"`
	Less      int64  `json:"less"`
	Param     string `json:"param"`
	ParamType string `json:"paramType"`
}

type params struct {
	PackageName string      `json:"package"`
	FuncInfo    []*funcInfo `json:"func"`
}

type Conf struct {
	Rules []*params `json:"rules"`
}

var Config Conf

func setConf(c *Conf) {
	v1 := &params{
		PackageName: "icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient",
		FuncInfo: []*funcInfo{
			{
				Name:    "SendRequest",
				Index:   3,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "SendRequest",
				Index:   4,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "Post",
				Index:   2,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "Post",
				Index:   3,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "Put",
				Index:   2,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "Put",
				Index:   3,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "Get",
				Index:   2,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "Get",
				Index:   3,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "Head",
				Index:   2,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "Head",
				Index:   3,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "Delete",
				Index:   2,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "Delete",
				Index:   3,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "MultiGet",
				Index:   2,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "MultiGet",
				Index:   3,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "MultiPost",
				Index:   2,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "MultiPost",
				Index:   3,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "NewHTTPRequest",
				Index:   0,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "NewHTTPRequest",
				Index:   1,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
		},
	}
	c.Rules = append(c.Rules, v1)

	v2 := &params{
		PackageName: "icode.baidu.com/baidu/netdisk/pcs-go-lib/ufc",
		FuncInfo: []*funcInfo{
			{
				Name:    "NewUfcRequest",
				Index:   7,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "NewUfcRequest",
				Index:   8,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "NewUFCClient",
				Index:   0,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "NewUFCClient",
				Index:   1,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
		},
	}
	c.Rules = append(c.Rules, v2)

	v3 := &params{
		PackageName: "icode.baidu.com/baidu/netdisk/nd-golib/src/pcs/ufc",
		FuncInfo: []*funcInfo{
			{
				Name:    "NewUfcRequest",
				Index:   7,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "NewUfcRequest",
				Index:   8,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
			{
				Name:    "NewUFCClient",
				Index:   7,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "NewUFCClient",
				Index:   8,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
		},
	}
	c.Rules = append(c.Rules, v3)

	v4 := &params{
		PackageName: "icode.baidu.com/baidu/netdisk/clouddisk-golib/ufc",
		FuncInfo: []*funcInfo{
			{
				Name:    "NewUfcRequest",
				Index:   7,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "NewUfcRequest",
				Index:   8,
				Greater: 0,
				Less:    1800000,
				Param:   "ReadWriteTimeoutMs",
			},
		},
	}
	c.Rules = append(c.Rules, v4)

	v5 := &params{
		PackageName: "(*icode.baidu.com/baidu/netdisk/clouddisk-golib/utils.UfcHelper)",
		FuncInfo: []*funcInfo{
			{
				Name:    "CurlProxyByUfc",
				Index:   8,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "CurlProxyByUfc",
				Index:   9,
				Greater: 0,
				Less:    1800000,
				Param:   "requestTimeout",
			},
			{
				Name:    "CurlProxyByUfcAll",
				Index:   8,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "CurlProxyByUfcAll",
				Index:   9,
				Greater: 0,
				Less:    1800000,
				Param:   "requestTimeout",
			},
		},
	}
	c.Rules = append(c.Rules, v5)

	v6 := &params{
		PackageName: "(*icode.baidu.com/baidu/netdisk/clouddisk-golib/pcs.PcsService)",
		FuncInfo: []*funcInfo{
			{
				Name:    "CurlProxyByUfc",
				Index:   7,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "CurlProxyByUfc",
				Index:   8,
				Greater: 0,
				Less:    1800000,
				Param:   "requestTimeout",
			},
			{
				Name:    "CurlProxyByUfcWithoutUI",
				Index:   7,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "CurlProxyByUfcWithoutUI",
				Index:   8,
				Greater: 0,
				Less:    1800000,
				Param:   "requestTimeout",
			},
			{
				Name:    "CurlProxyByUfcWithRetryForLite",
				Index:   7,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "CurlProxyByUfcWithRetryForLite",
				Index:   8,
				Greater: 0,
				Less:    1800000,
				Param:   "requestTimeout",
			},
		},
	}
	c.Rules = append(c.Rules, v6)

	v7 := &params{
		PackageName: "(*icode.baidu.com/baidu/netdisk/nd-point/src/points/models.SigninEventModel)",
		FuncInfo: []*funcInfo{
			{
				Name:    "CurlProxyByUfc",
				Index:   8,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "CurlProxyByUfc",
				Index:   9,
				Greater: 0,
				Less:    1800000,
				Param:   "requestTimeout",
			},
			{
				Name:    "CurlProxyByUfcWithoutUI",
				Index:   7,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "CurlProxyByUfcWithoutUI",
				Index:   8,
				Greater: 0,
				Less:    1800000,
				Param:   "requestTimeout",
			},
			{
				Name:    "CurlProxyByUfcWithRetryForLite",
				Index:   7,
				Greater: 0,
				Less:    600000,
				Param:   "ConnectTimeoutMs",
			},
			{
				Name:    "CurlProxyByUfcWithRetryForLite",
				Index:   8,
				Greater: 0,
				Less:    1800000,
				Param:   "requestTimeout",
			},
		},
	}
	c.Rules = append(c.Rules, v7)
}
