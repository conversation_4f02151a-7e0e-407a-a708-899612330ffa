package parameter

import (
	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/buildssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

var gv GlobalMapVar
var gs GlobalString

var Analyzer = &analysis.Analyzer{
	Name: "parameter",
	Doc:  "func param check",
	Requires: []*analysis.Analyzer{
		buildssa.Analyzer,
	},
	Run: run,
}

func init() {
	err := util.LoadConf("parameter.json", &Config)
	if err != nil { // 不存在配置文件时，走下面逻辑
		setConf(&Config)
	}
}

func run(pass *analysis.Pass) (interface{}, error) {
	ssaFuncs := pass.ResultOf[buildssa.Analyzer].(*buildssa.SSA).SrcFuncs
	maxDepth := 1

	cg := make(CallGraph)
	gv = make(GlobalMapVar)
	gs = make(GlobalString)

	for _, fn := range append(ssaFuncs) {
		AnalyzeFunction(cg, gv, gs, fn, maxDepth)
	}

	/* log.Println("gv %v", gv)
	   log.Println("gs %v", gs)

		for k, v := range cg {
			log.Println("to ", k, " len: ", len(v))
			for _, vv := range v {
				log.Println("from  ", vv.Fn.String())
			}
		}*/

	for _, rule := range Config.Rules {
		for _, funcInfo := range rule.FuncInfo {
			current_function := rule.PackageName + "." + funcInfo.Name
			//log.Println("current_function : ", current_function)

			for _, upFunc := range cg[current_function] {
				//log.Println("current_function : ", current_function, " upFunction : ", upFunc, " funcInfo.Name: ", funcInfo.Name, " funcInfo.Param: ", funcInfo.Param)
				taint_analyzer := CreateTaintAnalyzer(pass, upFunc.Fn.Pos())
				ret, _ := taint_analyzer.ContainsTaint(&upFunc.Instr.Call, &upFunc.Instr.Call.Args[funcInfo.Index], cg, funcInfo.Less)
				if ret {
					pass.Reportf(upFunc.Instr.Pos(), "[netdisk-sa][Parameter]: 下游超时值设置过大，如下游故障会带来自身雪崩风险。要考虑到golib sdk会再乘time.Second类似单位, 此处仅写数字即可，且数字建议连接超时不大于1分钟，读写超时不大于5分钟")
				}
			}
		}
	}

	return nil, nil
}
