package easylogger

import (
	"go/token"
	"strings"
	"sync"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/buildssa"
	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

const (
	StructSLogType   = "icode.baidu.com/baidu/netdisk/easy-go-sdk/library/slog.StructLog"
	NormLogSLogPrint = "(icode.baidu.com/baidu/netdisk/easy-go-sdk/library/slog.NormLog).Print"
)

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:      "easylogger",
		Name:     "easylogger",
		Run:      run,
		Requires: []*analysis.Analyzer{buildssa.Analyzer},
	}
}

var whiteList = []string{
	"baidu/netdisk/poms-meta-tangram",
	"baidu/netdisk/poms-tape-read",
	"baidu/netdisk/nd-digital-mall",
}
var onceDo sync.Once
var isInWhilteList bool

func run(pass *analysis.Pass) (interface{}, error) {
	onceDo.Do(func() {
		isInWhilteList = isInWhiteListFunc()
	})

	if isInWhilteList {
		return nil, nil
	}

	bs := pass.ResultOf[buildssa.Analyzer].(*buildssa.SSA)
	for _, fn := range bs.SrcFuncs {
		if isTestFile(pass, fn.Pos()) {
			continue
		}

		DoSLogCheck(pass, fn)
	}
	return nil, nil
}

func DoSLogCheck(pass *analysis.Pass, fn *ssa.Function) {
	for _, blocks := range fn.DomPreorder() {
		for _, instr := range blocks.Instrs {
			switch instr := instr.(type) {
			case *ssa.Call:
				DoRule(pass, instr)
			}
		}
	}
}

func DoRule(pass *analysis.Pass, call *ssa.Call) {
	if isLoggerCall(call) && !ignoreCall(pass, call) {
		pos := call.Pos()
		msg := "增量代码请使用结构化日志Slog来替代Logger打印日志, Slog使用文档见: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p。"
		issue := diff.NewIssue(pass.Fset, pos, msg)
		if diff.Differ.IssueNeedReport(issue) {
			pass.Report(*issue.Diagnostic)
		}
	}
}

func isLoggerCall(call *ssa.Call) bool {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return false
	}
	if fn.Pkg == nil || fn.Pkg.Pkg == nil {
		return false
	}

	const pkgName = "icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
	var logFuncMap = map[string]struct{}{
		"Debug":      {},
		"Info":       {},
		"Notice":     {},
		"Warning":    {},
		"Error":      {},
		"Emergency":  {},
		"FDebug":     {},
		"FInfo":      {},
		"FNotice":    {},
		"FWarning":   {},
		"FError":     {},
		"FEmergency": {},
	}

	pkgPath := fn.Pkg.Pkg.Path()
	if _, ok := logFuncMap[fn.Name()]; !ok {
		return false
	}

	if !strings.Contains(pkgPath, pkgName) {
		return false
	}

	args := call.Call.Args
	if len(args) == 0 {
		return false
	}

	const loggerType = "icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger.Logger"
	firstArg := args[0] // 第一个参数可能为logger对象或者 const
	_, ok := firstArg.(*ssa.Const)
	if !ok { // 第一个参数为logger，第二个为const
		if len(args) <= 1 {
			return false
		}
		// len(args) >= 2
		_, ok = args[1].(*ssa.Const)
		if !ok /* || args[0].Type().String() != loggerType*/ {
			return false
		}
	}
	return true
}

func ignoreCall(pass *analysis.Pass, call *ssa.Call) bool {
	fileName := pass.Fset.Position(call.Pos()).Filename
	if strings.Contains(fileName, "/lib/conf/") {
		return true
	}
	return false
}

// 过滤单测
func isTestFile(pass *analysis.Pass, pos token.Pos) bool {
	fileName := pass.Fset.Position(pos).Filename
	return strings.HasSuffix(fileName, "_test.go")
}

func isInWhiteListFunc() bool {
	repoName, err := utils.GetProjectName()
	if err != nil {
		return false
	}

	for _, whiteListRepoName := range whiteList {
		if repoName == whiteListRepoName {
			return true
		}
	}
	return false
}
