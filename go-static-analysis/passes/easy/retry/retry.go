package easyretry

import (
	"fmt"
	"go/token"
	"go/types"
	"strings"
	"sync"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/buildssa"
	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

const (
	EasySDK            = "icode.baidu.com/baidu/netdisk/easy-go-sdk"
	FieldNameUfcClient = "UfcClient"
	FuncNameDo         = "Do" // 检测 client.Do() 函数是否重试，其中client变量中内嵌了 easy.UfcClient

	Depth = 2 // 可递归深度，考虑到性能，这里设置递归深度为2
)

var whiteList = []string{
	"baidu/netdisk/nd-search-textann",
	"baidu/netdisk/nd-ai-search",
	"baidu/netdisk-global/trade-order",
	"baidu/netdisk-apaas/apaas-worker",
	"baidu/netdisk/clouddisk-mbox",
}

type ReportMsg struct {
	Pos token.Pos
	Msg string
}

func NewRetryAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Name:     "easyretry",
		Run:      run,
		Doc:      "the rule check the retry code of easy rule",
		Requires: []*analysis.Analyzer{buildssa.Analyzer},
	}
}

var onceDo sync.Once
var isInWhilteList bool

func run(pass *analysis.Pass) (interface{}, error) {
	onceDo.Do(func() {
		isInWhilteList = isInWhiteListFunc()
	})

	if isInWhilteList {
		return nil, nil
	}

	bs := pass.ResultOf[buildssa.Analyzer].(*buildssa.SSA)
	funcs := bs.SrcFuncs

	for _, fn := range funcs {
		processFn(pass, fn)
	}
	return nil, nil
}

/*
depth: 递归深度
needCheckInForBody: 是否需要判断在for循环里面，第一次调用需要，后面递归进去不需要,
*/
func processFn(pass *analysis.Pass, fn *ssa.Function) {
	// 需要检查for循环
	valueMap, ok := collectValueBeforeForBody(fn)
	if !ok {
		return
	}

	doRuleInForBody(pass, fn, valueMap)
}

// 在 forloop 阶段时收集变量
// [for.loop, for.body) 阶段收集变量
func collectValueBeforeForBody(fn *ssa.Function) (map[ssa.Value]struct{}, bool) {
	blocks := fn.DomPreorder()

	start := -1
	end := -1
	for i, block := range blocks {
		if block.Comment == "for.loop" {
			start = i
		}
		if block.Comment == "for.body" {
			end = i
		}
	}

	if start == -1 {
		return nil, false
	}

	if start >= end {
		return nil, false
	}

	res := make(map[ssa.Value]struct{})
	blks := blocks[start:end]
	for _, block := range blks {
		for _, instr := range block.Instrs {
			if value, ok := instr.(ssa.Value); ok {
				res[value] = struct{}{}
			}
		}
	}

	return res, true
}

func doRuleCommonForSSAFunction(pass *analysis.Pass, fn *ssa.Function, values map[ssa.Value]struct{}, callerPos []token.Pos) {
	for _, blocks := range fn.DomPreorder() {
		for _, instr := range blocks.Instrs {
			if call, ok := instr.(*ssa.Call); ok {
				doRuleForSSACall(pass, call, values, callerPos)
			}
		}
	}
}

func doRuleForSSACall(pass *analysis.Pass, call *ssa.Call, values map[ssa.Value]struct{}, callerPos []token.Pos) {
	// 递归深度限制
	if len(callerPos) >= Depth {
		return
	}

	c := call.Call
	f := c.StaticCallee()
	if f == nil || f.Signature == nil {
		return
	}

	if !fnNameIsMatch(f) {
		doRuleCommonForSSAFunction(pass, f, values, append(callerPos, c.Pos()))
	}

	recv := f.Signature.Recv()
	if recv == nil {
		return
	}

	recvType := recv.Type()
	if ptrType, ok := recvType.(*types.Pointer); ok {
		recvType = ptrType.Elem()
	}

	if structType, ok := recvType.Underlying().(*types.Struct); ok {
		for i := 0; i < structType.NumFields(); i++ {
			field := structType.Field(i)
			if strings.Contains(field.Name(), FieldNameUfcClient) {
				msg := ""
				if len(callerPos) == 0 {
					msg = "client.Do()函数不应当在for循环中, easy函数内部已做了请求重试"
				} else {
					callerPosition := ""
					space := "  "
					for _, pos := range callerPos {
						p := pass.Fset.Position(pos).String()
						callerPosition += fmt.Sprintf("%s%s ->\n", space, p)
						space += "  "
					}

					p := pass.Fset.Position(call.Pos()).String()
					callerPosition += fmt.Sprintf("%s%s ->\n", space, p)

					msg = fmt.Sprintf("client.Do函数被调用处不应当在for循环中,easy函数内部已做了请求重试。请检查以下调用链:\n%s", callerPosition)
				}
				pos := call.Pos()
				issue := diff.NewIssue(pass.Fset, pos, msg)
				if diff.Differ.IssueNeedReport(issue) {
					pass.Report(*issue.Diagnostic)
				}

			}
		}
	}
}

// 在【for.body, for.done）中间处理规则
func doRuleInForBody(pass *analysis.Pass, fn *ssa.Function, values map[ssa.Value]struct{}) {
	blocks := fn.DomPreorder()

	start := -1
	end := -1
	for i, block := range blocks {
		if block.Comment == "for.body" {
			start = i
		}
		if block.Comment == "for.done" {
			end = i
		}
	}

	if start == -1 || end == -1 {
		return
	}

	if start >= end {
		return
	}

	blks := blocks[start:end]

	for _, block := range blks {
		for _, instr := range block.Instrs {
			ops := make([]*ssa.Value, 0)
			ops = instr.Operands(ops)

			for _, op := range ops {
				if _, ok := values[*op]; ok {
					return
				}
			}

			if call, ok := instr.(*ssa.Call); ok {
				doRuleForSSACall(pass, call, values, nil)
			}
		}
	}
}

func isInWhiteListFunc() bool {
	repoName, err := utils.GetProjectName()
	if err != nil {
		return false
	}

	for _, whiteListRepoName := range whiteList {
		if repoName == whiteListRepoName {
			return true
		}
	}
	return false
}

func fnNameIsMatch(fn *ssa.Function) bool {
	return fn.Name() == FuncNameDo
}
