package golibcheck

import (
	"fmt"
	"go/ast"
	"strings"
	"sync"

	"golang.org/x/tools/go/analysis"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

const svcLink = "https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/8MCddZCpuH/k73vwkNTP1/yV8OXbtk_XoGe0"

var netdiskLibList = []string{
	"baidu/netdisk/pcs-go-lib/ufc",
	"baidu/netdisk/pcs-go-lib/httpclient",
	"baidu/netdisk-global/pcs-go-lib/ufc", // pcs
	"baidu/netdisk-global/pcs-go-lib/httpclient",

	"baidu/netdisk/nd-membership-lib/httpclient",
	"baidu/netdisk/nd-membership-lib/ufc", // 商业化
	// 智能化
	"baidu/netdisk/clouddisk-golib/ufc",  // 主端
	"baidu/netdisk/clouddisk-golib/mbox", // 业务逻辑
	"baidu/netdisk/clouddisk-golib/httpclient",

	"baidu/netdisk/mall-golib-utils/addr",
	"baidu/netdisk/mall-golib-utils/ufc",
	"baidu/netdisk/mall-golib-utils/httpclient",

	"baidu/netdisk-apaas/apaas-golib/ufc", // tob，这个lib库没有 /ufc 包，为了避免其后续增加，这里提前加下
	"baidu/netdisk-apaas/apaas-golib/httpclient",

	"baidu/nd-jupiter/commonlib/ufc",        // 风控
	"baidu/nd-jupiter/commonlib/httpclient", // 风控

	"baidu/netdisk/rc-go-lib/ufc",
	"baidu/netdisk/rc-go-lib/httpclient",

	"baidu/netdisk/pcs-go-lib/framework",
}

var whiteList = []string{
	"baidu/netdisk/easy-party",
	"baidu/netdisk-global/pcs-data-flow",
}

var onceDo sync.Once
var isInWhilteList bool

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:  "golibcheck",
		Name: "golibcheck",
		Run:  run,
	}
}

func run(pass *analysis.Pass) (interface{}, error) {
	onceDo.Do(func() {
		isInWhilteList = isInWhiteListFunc()
	})

	if isInWhilteList {
		return nil, nil
	}

	for _, file := range pass.Files {
		for _, importSpec := range file.Imports {
			checkImports(pass, importSpec)
		}
	}
	return nil, nil
}

func isInWhiteListFunc() bool {
	repoName, err := utils.GetProjectName()
	if err != nil {
		return false
	}

	for _, whiteListRepoName := range whiteList {
		if repoName == whiteListRepoName {
			return true
		}
	}
	return false
}

func checkImports(pass *analysis.Pass, importSpec *ast.ImportSpec) {
	if importSpec == nil || importSpec.Path == nil {
		return
	}

	for _, lib := range netdiskLibList {
		path := trimQuote(importSpec.Path.Value)
		if strings.HasSuffix(path, lib) {
			issue := diff.NewIssue(pass.Fset, importSpec.Pos(), "easy项目不允许使用自己封装的golib: %s, 可在 easy 平台创建壳服务去解决。参考：%s", path, svcLink)
			if diff.Differ.IssueNeedReport(issue) { // 检测是否需要过滤（增量检测）
				pass.Reportf(importSpec.Pos(), "easy项目不允许使用自己封装的golib: %s", path)
			} else {
				// 非新增的引入， 发送至hi报警推 rd 改代码
				msg := fmt.Sprintf("%s模块 使用了golib:%s\n", pass.Fset.Position(importSpec.Pos()), path)
				utils.SendWarnMessageToHiForDailyMessage(msg)
			}
		}
	}
}

func trimQuote(input string) string {
	input = strings.TrimSuffix(input, "\"")
	input = strings.TrimPrefix(input, "\"")
	return input

}
