package easyfn

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easyfn/pkg/easyfn"
)

func Run() {
	r := easyfn.NewFnMsgRunner()

	pwd, err := os.Getwd()
	if err != nil {
		fmt.Println("os get wd err: ", err)
		return
	}

	dir := filepath.Join(pwd, "common")
	if err := r.Init<PERSON>ll(false, dir, []string{}); err != nil {
		fmt.Println("init err: ", err)
		log.Fatal(err)
	}

	r.Run()
	if err := r.SendR<PERSON>ult(); err != nil {
		fmt.Println("send err: ", err)
	}
}
