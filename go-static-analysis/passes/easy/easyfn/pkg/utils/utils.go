package utils

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"
)

func ExecCommand(script string, args ...string) (string, error) {
	cmd := exec.Command("/bin/bash", append([]string{"-s", "-"}, args...)...)
	wc, err := cmd.StdinPipe()
	if err != nil {
		return "", err
	}

	go func() {
		defer wc.Close()
		fmt.Fprintf(wc, "%s", script)
	}()

	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func ExecCommandWithTimeout(script string, args ...string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "/bin/bash", append([]string{"-s", "-"}, args...)...)
	wc, err := cmd.StdinPipe()
	if err != nil {
		return "", err
	}

	go func() {
		defer wc.Close()
		fmt.Fprintf(wc, "%s", script)
	}()

	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}
