package easyfn

import (
	"encoding/json"
	"errors"
	"fmt"
	"go/build"
	"go/types"
	"log"
	"path/filepath"
	"strings"
	"time"

	"golang.org/x/tools/go/packages"
	"golang.org/x/tools/go/ssa"
	"golang.org/x/tools/go/ssa/ssautil"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easyfn/pkg/utils"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const (
	EasySDKPkg      = "icode.baidu.com/baidu/netdisk/easy-go-sdk"
	BaiduRepoSubStr = "8235/baidu"

	EasyFnURL = "http://10.11.97.54:80/easyfn?method=add"
)

type CallRelation struct {
	Caller string          `json:"caller"`
	Callee []*CallRelation `json:"callee,omitempty"`
}

type FnMsgRunner struct {
	RepoName  string
	Branch    string
	ICodeName string
	CommonDir string
	CommitAt  string
	CommitID  string

	Prog               *ssa.Program
	AllPackages        []*ssa.Package
	Functions          map[*ssa.Function]struct{} // 非api函数
	FuncsVisit         map[*ssa.Function]struct{}
	ChainRequestStruct *ChainRequest
	FuncInfoList       []*FuncInfo

	StructUIDMap       map[string]*types.Struct
	UIDStructMap       map[*types.Struct]string
	TypesDefinitionMap map[string]string // uid -> type
}

func NewFnMsgRunner() *FnMsgRunner {
	return &FnMsgRunner{
		Functions:    make(map[*ssa.Function]struct{}),
		FuncsVisit:   make(map[*ssa.Function]struct{}),
		FuncInfoList: make([]*FuncInfo, 0),
	}
}

func (r *FnMsgRunner) AppendFn(fnInfo *FuncInfo) {
	r.FuncInfoList = append(r.FuncInfoList, fnInfo)
}

func (r *FnMsgRunner) InitAll(tests bool, dir string, args []string) error {
	if err := r.initProg(tests, dir, args); err != nil {
		return err
	}
	if err := r.initRepoMsg(); err != nil {
		return err
	}
	r.initSetChainRequest()
	r.initFunctions()
	return nil
}

func (r *FnMsgRunner) initSetChainRequest() {
	r.ChainRequestStruct = &ChainRequest{
		Repo:     r.ICodeName,
		Branch:   r.Branch,
		CreateAt: time.Now().Unix(),
		CommitAt: r.CommitAt,
		CommitID: r.CommitID,
	}
}

func (r *FnMsgRunner) initProg(tests bool, dir string, args []string) error {
	cfg := &packages.Config{
		Mode:       packages.LoadAllSyntax,
		Tests:      tests,
		Dir:        dir,
		BuildFlags: build.Default.BuildTags,
	}

	initial, err := packages.Load(cfg, args...)
	if err != nil {
		return err
	}

	if packages.PrintErrors(initial) > 0 {
		return errors.New("packages contain errors")
	}

	// Create and build SSA-form program representation.
	prog, pkgs := ssautil.AllPackages(initial, 0)
	prog.Build()

	r.Prog = prog
	r.AllPackages = pkgs
	return nil
}

func (r *FnMsgRunner) initRepoMsg() error {
	repoName, err := utils.ExecCommandWithTimeout("go list -m")
	if err != nil {
		return err
	}
	r.RepoName = strings.TrimSpace(repoName)

	icodeName, err := utils.ExecCommand(`git remote -v | head -1 | awk {'print $2'}`)
	if err != nil {
		return err
	}
	icodeName = strings.TrimSpace(icodeName)
	i := strings.Index(icodeName, BaiduRepoSubStr)
	if i == -1 || i+5 < 0 {
		return errors.New("get icode name err")
	}
	r.ICodeName = icodeName[i+5:]

	r.CommonDir = filepath.Join(r.RepoName, "common")

	commitAt, err := utils.ExecCommand(`git log -1 --format="%cD"`)
	if err != nil {
		return errors.New("get commit time err")
	}
	r.CommitAt = commitAt

	commitID, err := utils.ExecCommand(`git log -1 --format="%H"`)
	if err != nil {
		return errors.New("get commit id err")
	}
	r.CommitID = commitID

	branch, err := utils.ExecCommand("git rev-parse --abbrev-ref HEAD")
	if err != nil {
		return errors.New("get branch err")
	}
	r.Branch = branch
	return nil
}

func isEasyGoSDKFn(fn *ssa.Function) bool {
	if fn == nil {
		return false
	}
	if strings.Contains(fn.String(), EasySDKPkg) {
		return true
	}
	return false
}

func isEasyGoSDKType(importPath string) bool {
	easyPkg := []string{
		"icode.baidu.com/baidu/netdisk/easy-go-sdk",
		"icode.baidu.com/baidu/netdisk/easy-adapter",
		"icode.baidu.com/baidu/netdisk/tangram-iface",
	}
	if importPath != "" {
		for _, pkg := range easyPkg {
			if strings.Contains(importPath, pkg) {
				return true
			}
		}
	}
	return false
}

func (r *FnMsgRunner) isLocalFn(fn *ssa.Function) bool {
	if fn == nil {
		return false
	}
	if strings.Contains(fn.String(), r.RepoName) {
		return true
	}
	return false
}

func (r *FnMsgRunner) isLocalType(typ types.Type) bool {
	if typ == nil {
		return false
	}
	if strings.Contains(typ.String(), r.RepoName) {
		return true
	}
	return false
}

func (r *FnMsgRunner) initFunctions() {
	pkgs := r.Prog.AllPackages()
	for _, pkg := range pkgs {
		for _, member := range pkg.Members {
			fn, ok := member.(*ssa.Function)
			if !ok {
				continue
			}
			if ok := r.isLocalFn(fn); !ok {
				continue
			}
			if isPkgInitFn(fn) {
				continue
			}
			r.Functions[fn] = struct{}{}
		}
	}
}

func (r *FnMsgRunner) Run() {
	for fn := range r.Functions {
		if !r.isCommonDirFn(fn) {
			continue
		}
		fnInfo := r.RunFunction(fn)
		r.AppendFn(fnInfo)
	}
}

func (r *FnMsgRunner) SendResult() error {
	data, err := json.Marshal(r.FuncInfoList)
	if err != nil {
		log.Println("json marshal err", err)
		return err
	}
	r.ChainRequestStruct.Data = string(data)
	body, err := json.Marshal(r.ChainRequestStruct)
	if err != nil {
		log.Println("json marshal err", err)
		return err
	}

	bodyStr := string(body)
	for i := 0; i < 3; i++ {
		_, err = httpclient.Post(EasyFnURL, map[string]string{}, 1000, 3000, bodyStr)
		if err == nil {
			break
		}
	}
	if err != nil {
		log.Printf("[post err] [url: %s] [data: %s]", EasyFnURL, bodyStr)
		return err
	}

	return nil
}

func (r *FnMsgRunner) RunFunction(fn *ssa.Function) *FuncInfo {
	fnInfo := &FuncInfo{}
	r.genFuncDescription(fn, fnInfo)
	fnInfo.Params = r.genFuncParams(fn)
	fnInfo.Returns = r.genFuncReturns(fn)

	return fnInfo
}

func (r *FnMsgRunner) genFuncDescription(fn *ssa.Function, fnInfo *FuncInfo) {
	if fnInfo == nil {
		fnInfo = &FuncInfo{}
	}

	fnInfo.ImportPath = fn.Pkg.Pkg.Path()
	fnInfo.Name = fn.Name()
	fnInfo.PackageName = fn.Pkg.Pkg.Name()
}

func (r *FnMsgRunner) isStructor(p *ssa.Parameter) (*types.Struct, bool) {
	typ := p.Type()
	return r.isTypesHasStruct(typ)
}

func (r *FnMsgRunner) genFuncParams(fn *ssa.Function) []*FuncParam {
	params := make([]*FuncParam, 0)
	for index, param := range fn.Params {
		funcParam := r.genFuncParam(fn, param, index)
		params = append(params, funcParam)
	}
	return params
}

func (r *FnMsgRunner) isTypesHasStruct(typ types.Type) (*types.Struct, bool) {
	maxCnt := 10
	index := 0
	for {
		index++
		if index > maxCnt {
			break
		}

		switch t := typ.(type) {
		case *types.Pointer:
			typ = t.Elem()
		case *types.Named:
			typ = t.Underlying()
		case *types.Struct:
			return t, true
		default:
			break
		}
	}
	return nil, false
}

type ParamOrReturnParentsInfo struct {
	Field       string
	Type        string
	StructType  string
	PackageName string
	ImportPath  string
}

func DSLConvertToParamOrReturnParentsInfo(v *ValueDSL) *ParamOrReturnParentsInfo {
	return &ParamOrReturnParentsInfo{
		Field:       v.Field,
		Type:        v.Type,
		StructType:  v.StructType,
		PackageName: v.PackageName,
		ImportPath:  v.ImportPath,
	}
}

func NewParamParentsInfo(dsl *ValueDSL) *ParamOrReturnParentsInfo {
	res := &ParamOrReturnParentsInfo{}
	res.Field = dsl.Field
	res.ImportPath = dsl.ImportPath
	res.PackageName = dsl.PackageName
	res.StructType = dsl.StructType
	res.Type = dsl.Type
	return res
}

func GetParamStructDSL(v *types.Var) *ParamOrReturnParentsInfo {
	res := &ParamOrReturnParentsInfo{}

	pkgName, importPath, name := getPkgInfoByVar(v)

	res.Field = v.Name()
	res.Type = v.Type().String()
	res.StructType = name
	res.ImportPath = importPath
	res.PackageName = pkgName
	return res
}

func GetTypeImportPkgMsg(t types.Type) (imoprtPath string, importPkg string) {
	switch typ := t.(type) {
	case *types.Named:
		obj := typ.Obj()
		if obj == nil {
			return "", ""
		}
		pkg := obj.Pkg()
		if pkg == nil {
			return "", ""
		}
		return pkg.Path(), pkg.Name()
	case *types.Pointer:
		return GetTypeImportPkgMsg(typ.Elem())
	case *types.Basic:
		return "", ""
	case *types.Array:
		return GetTypeImportPkgMsg(typ.Elem())
	case *types.Slice:
		return GetTypeImportPkgMsg(typ.Elem())
	default:
		return "", ""
	}
}

func GetTypeTypes(t types.Type) string {
	switch typ := t.(type) {
	case *types.Named:
		underlying := typ.Underlying()
		switch underlying.(type) {
		case *types.Struct:
			return GetTypeTypes(underlying)

		default:
			return t.String()
		}
	case *types.Pointer:
		return "*" + GetTypeTypes(typ.Elem())
	case *types.Struct:
		return "struct"
	case *types.Basic:
		return t.String()
	case *types.Interface:
		return t.String()
	case *types.Array:
		return fmt.Sprintf("[%d]%s", typ.Len(), GetTypeTypes(typ.Elem()))
	case *types.Slice:
		return "[]" + GetTypeTypes(typ.Elem())
	default:
		return t.String()
	}
}

func (r *FnMsgRunner) genFuncParam(fn *ssa.Function, p *ssa.Parameter, index int) *FuncParam {
	funcParam := &FuncParam{}
	funcParam.Field = p.Name()
	importPath, importPkg := GetTypeImportPkgMsg(p.Type())
	funcParam.ImportPath = importPath
	funcParam.PackageName = importPkg
	funcParam.EasySDK = isEasyGoSDKType(importPath)
	funcParam.Type = GetTypeTypes(p.Type())
	funcParam.Value = p.Name()

	structure, ok := r.isStructor(p)
	if ok {
		structType := GetStructType(p.Type().String())
		funcParam.StructType = structType

		parentParentInfo := &ParamOrReturnParentsInfo{}
		parentParentInfo.ImportPath = funcParam.ImportPath
		parentParentInfo.PackageName = funcParam.PackageName
		parentParentInfo.StructType = funcParam.StructType
		parentParentInfo.Field = funcParam.Field
		parentParentInfo.Type = funcParam.Type
		funcParam.DSL = r.genFuncItemDSL(parentParentInfo, structure, make(map[types.Type]struct{}), "", false)
	}

	return funcParam
}

func (r *FnMsgRunner) genFuncReturns(fn *ssa.Function) []*FuncReturn {
	res := make([]*FuncReturn, 0)

	results := fn.Signature.Results()
	for i := 0; i < results.Len(); i++ {
		v := results.At(i)
		fnReturn := r.genFnReturn(v)
		res = append(res, fnReturn)
	}

	return res
}

func (r *FnMsgRunner) genFnReturn(v *types.Var) *FuncReturn {
	re := &FuncReturn{}
	pkgName, importPath, _ := getPkgInfoByVar(v)
	isEasyGoSDK := isEasyGoSDKType(importPath)
	re.EasySDK = isEasyGoSDK
	re.PackageName = pkgName
	re.ImportPath = importPath
	re.Type = GetTypeTypes(v.Type())
	if _, ok := r.isTypesHasStruct(v.Type()); ok {
		re.StructType = GetStructType(v.Type().String())
	} else {
		re.StructType = ""
	}

	if isEasyGoSDK {
		re.DSL = nil
		return re
	}

	st, ok := r.isTypesHasStruct(v.Type())
	vDSL := GetDSLFromVar(v)
	if !ok {
		re.DSL = nil
	} else {
		re.DSL = r.genFuncItemDSL(DSLConvertToParamOrReturnParentsInfo(vDSL), st, make(map[types.Type]struct{}), "", true)
	}
	return re
}

func getPackageInfoByStruct(v *types.Struct) string {
	return GetTypeTypes(v.Underlying())
}

func getPackageInfoByVar(v *types.Var) (name string, typ string, pkgName string, importPath string) {
	name = v.Name()
	typ = v.Type().String()
	pkgName = v.Pkg().Name()
	importPath = v.Pkg().Path()

	fmt.Printf("getPackageInfoByVar. name:%s, typ:%s, pkgName:%s, importPath:%s\n", name, typ, pkgName, importPath)
	return name, typ, pkgName, importPath
}

func getPackageInfo(typ types.Type) (importPath string, packageName string, name string) {
	typStr := typ.String()
	//fmt.Println("typeStr: ", typStr)
	typStrSlice := strings.Split(typStr, ".")
	if len(typStrSlice) <= 1 {
		importPath = ""
		packageName = ""
		name = typStr
	} else {
		importPath = typStrSlice[0]
		if strings.HasSuffix(typStrSlice[0], EasySDKPkg) {
			packageName = strings.ReplaceAll(typStrSlice[0], EasySDKPkg, "easy")
			if strings.HasPrefix(packageName, "*") {
				packageName = strings.TrimPrefix(packageName, "*")
				name += "*" + typStrSlice[1]
			} else {
				name = typStrSlice[1]
			}
		}
	}

	return
}

// nolint:lll
func (r *FnMsgRunner) genFuncItemDSL(info *ParamOrReturnParentsInfo, s *types.Struct, typesMap map[types.Type]struct{}, preValue string, isReturn bool) *ValueDSL {
	dsl := &ValueDSL{}
	dsl.Field = info.Field
	dsl.Desc = ""
	dsl.Type = info.Type
	dsl.StructType = info.StructType

	if !isReturn {
		if preValue == "" {
			dsl.Value = info.Field
		} else {
			dsl.Value = preValue + "." + info.Field
		}
	}

	dsl.PackageName = info.PackageName
	dsl.ImportPath = info.ImportPath

	isEasyGOSDK := isEasyGoSDKType(info.ImportPath)
	dsl.EasySDK = isEasyGOSDK
	if isEasyGOSDK {
		dsl.Children = make([]*ValueDSL, 0)
		return dsl
	}

	for i := 0; i < s.NumFields(); i++ {
		v := s.Field(i)
		pkgName, importPath, _ := getPkgInfoByVar(v)
		dslChildren := &ValueDSL{}
		dslChildren.Field = v.Name()
		dslChildren.Type = GetTypeTypes(v.Type())
		if _, ok := r.isTypesHasStruct(v.Type()); ok {
			dslChildren.StructType = GetStructType(v.Type().String())
		} else {
			dslChildren.StructType = ""
		}
		dslChildren.ImportPath = importPath
		dslChildren.PackageName = pkgName
		dslChildren.Desc = ""
		dslChildren.EasySDK = isEasyGoSDKType(importPath)

		if !isReturn {
			if dsl.Value == "" {
				dslChildren.Value = v.Name()
			} else {
				dslChildren.Value = dsl.Value + "." + v.Name()
			}
		}

		if dslChildren.EasySDK {
			dslChildren.Children = make([]*ValueDSL, 0)
			dsl.Children = append(dsl.Children, dslChildren)
			continue
		}

		hasStruct, ok := r.isTypesHasStruct(v.Type())
		if ok {
			if _, ok := typesMap[s.Underlying()]; ok {
				dslChildrenChildren := make([]*ValueDSL, 0)
				dslChildren.Children = dslChildrenChildren
				return nil
			}
			newTypesMap := copyMap(typesMap)
			dslChildrenChildren := r.genFuncItemDSL(GetParamStructDSL(v), hasStruct, newTypesMap, dslChildren.Value, isReturn)
			dslChildren.Children = append(dslChildren.Children, dslChildrenChildren)
		} else {
			dslChildren.Children = make([]*ValueDSL, 0)
		}
		dsl.Children = append(dsl.Children, dslChildren)
	}
	return dsl
}

func GetStructType(typ string) string {
	slice := strings.Split(typ, ".")
	return slice[len(slice)-1]
}

func GetDSLFromVar(v *types.Var) *ValueDSL {
	dsl := &ValueDSL{}
	pkgName, importPath, _ := getPkgInfoByVar(v)
	dsl.Field = v.Name()
	dsl.Type = GetTypeTypes(v.Type())
	dsl.StructType = GetStructType(v.Type().String())
	dsl.ImportPath = importPath
	dsl.PackageName = pkgName
	return dsl
}

func getPkgInfoByVar(v *types.Var) (pkgName string, importPath string, structName string) {
	importPath, pkgName = GetTypeImportPkgMsg(v.Type())
	structName = GetStructType(v.Type().String())
	return
}

func copyMap(old map[types.Type]struct{}) map[types.Type]struct{} {
	newMap := make(map[types.Type]struct{})
	for k, v := range old {
		newMap[k] = v
	}
	return newMap
}

func (r *FnMsgRunner) isCommonDirFn(fn *ssa.Function) bool {
	return strings.Contains(fn.String(), r.CommonDir)
}

func isPkgInitFn(fn *ssa.Function) bool {
	if fn.Name() != "init" {
		return false
	}

	sig := fn.Signature
	if sig.Params().Len() != 0 {
		return false
	}
	if sig.Results().Len() != 0 {
		return false
	}
	return true
}
