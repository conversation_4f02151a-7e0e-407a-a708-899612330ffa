package easyfn

type ChainRequest struct {
	Repo     string `json:"repo"`
	Branch   string `json:"branch"`
	CommitAt string `json:"commit_at"`
	CommitID string `json:"commit_id"`
	CreateAt int64  `json:"create_at"`
	Data     string `json:"data"`
}

type FuncInfo struct {
	Name        string `json:"name"`
	ImportPath  string `json:"importPath"`
	PackageName string `json:"packageName"`

	Description string        `json:"description"`
	Params      []*FuncParam  `json:"params"`
	Returns     []*FuncReturn `json:"returns"`
}

type FuncParam struct {
	EasySDK     bool      `json:"easySDK"`
	Field       string    `json:"field"`
	Type        string    `json:"type"`
	StructType  string    `json:"structType"`
	Value       string    `json:"value"`
	Desc        string    `json:"desc"`
	ImportPath  string    `json:"importPath"`
	PackageName string    `json:"packageName"`
	DSL         *ValueDSL `json:"dsl"`
}

type FuncReturn struct {
	EasySDK     bool      `json:"easySDK"`
	Type        string    `json:"type"`
	StructType  string    `json:"structType"`
	ImportPath  string    `json:"importPath"`
	PackageName string    `json:"packageName"`
	DSL         *ValueDSL `json:"dsl"`
}

type ValueDSL struct {
	EasySDK     bool        `json:"easySDK"`
	Field       string      `json:"field"`
	Type        string      `json:"type"`
	StructType  string      `json:"structType"`
	ImportPath  string      `json:"importPath"`
	PackageName string      `json:"packageName"`
	Desc        string      `json:"desc"`
	Value       string      `json:"value"`
	Children    []*ValueDSL `json:"children"`
}
