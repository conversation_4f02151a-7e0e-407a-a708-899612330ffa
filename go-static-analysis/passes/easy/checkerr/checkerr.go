package checkerr

import (
	"go/token"
	"go/types"
	"strings"
	"sync"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/buildssa"
	"golang.org/x/tools/go/ssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

const (
	CheckFuncName   = "CustomError"
	CheckPkgName    = "icode.baidu.com/baidu/netdisk/easy-go-sdk"
	CheckReturnType = "icode.baidu.com/baidu/netdisk/easy-go-sdk.ErrorDto"
)

var whiteList = []string{
	"baidu/netdisk/easy-party",
}

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:      "checkerrs",
		Name:     "checkerrs",
		Requires: []*analysis.Analyzer{buildssa.Analyzer},
		Run:      run,
	}
}

var onceDo sync.Once
var isInWhilteList bool

func run(pass *analysis.Pass) (interface{}, error) {
	onceDo.Do(func() {
		isInWhilteList = isInWhiteListFunc()
	})

	if isInWhilteList {
		return nil, nil
	}

	bs := pass.ResultOf[buildssa.Analyzer].(*buildssa.SSA)

	funcs := bs.SrcFuncs
	for _, members := range bs.Pkg.Members {
		if global, ok := members.(*ssa.Global); ok {
			position := pass.Fset.Position(members.Pos())
			if strings.Contains(position.Filename, "lib/errs/errs.go") {
				continue
			}
			doGlobal(pass, global)
		}
	}
	for _, fn := range funcs {
		position := pass.Fset.Position(fn.Pos())
		if strings.Contains(position.Filename, "lib/errs/errs.go") {
			continue
		}

		if isTestFile(pass, fn.Pos()) {
			continue
		}

		doFnRule(pass, fn)
	}
	return nil, nil
}

func doGlobal(pass *analysis.Pass, global *ssa.Global) {
	typ := global.Type()
	for {
		ptrType, ok := typ.(*types.Pointer)
		if ok {
			typ = ptrType.Elem()
		} else {
			break
		}
	}

	_, ok := typ.(*types.Named)
	if !ok {
		return
	}

	// reflect typ: types.Named, typ.Underlying() typ: types.Struct
	if strings.Contains(typ.String(), CheckReturnType) {
		issue := diff.NewIssue(pass.Fset, global.Pos(), "easy项目不允许自定义错误码,需要在平台中进行注册")
		if diff.Differ.IssueNeedReport(issue) {
			pass.Report(*issue.Diagnostic)
		}
	}
}

func isInWhiteListFunc() bool {
	repoName, err := utils.GetProjectName()
	if err != nil {
		return false
	}

	for _, whiteListRepoName := range whiteList {
		if repoName == whiteListRepoName {
			return true
		}
	}
	return false
}

func doFnRule(pass *analysis.Pass, fn *ssa.Function) {
	for _, block := range fn.Blocks {
		for _, instr := range block.Instrs {
			switch instr := instr.(type) {
			case *ssa.Call:
				processCall(pass, instr)
			case *ssa.Alloc:
				processAlloc(pass, instr)
			default:
			}
		}
	}
}

func processCall(pass *analysis.Pass, call *ssa.Call) {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return
	}
	if fn.Pkg == nil || fn.Pkg.Pkg == nil {
		return
	}
	pkgPath := fn.Pkg.Pkg.Path()
	if fn.Name() == CheckFuncName && strings.Contains(pkgPath, CheckPkgName) {
		if !isParamConstForCustomError(&call.Call) {
			return
		}

		issue := diff.NewIssue(pass.Fset, call.Pos(), "easy项目不允许自定义错误码,需要在平台中进行注册")
		if diff.Differ.IssueNeedReport(issue) {
			pass.Report(*issue.Diagnostic)
		}
		return
	}

	/*
		// 对于非 easy.CustomError函数返回了结构体easy.ErrorDto的方法调用不予以检测
		cc := call.Call.Signature()
		tuples := cc.Results()
		for i := 0; i < tuples.Len(); i++ {
			v := tuples.At(i)
			typ := v.Type()

			if ptrType, ok := typ.(*types.Pointer); ok {
				typ = ptrType.Elem()
			}

			// reflect typ: types.Named, typ.Underlying() typ: types.Struct
			if typ.String() == CheckReturnType {
				issue := diff.NewIssue(pass.Fset, call.Pos(), "easy项目不允许自定义错误码,需要在平台中进行注册")
				if diff.Differ.IssueNeedReport(issue) {
					pass.Report(*issue.Diagnostic)
				}
			}
		}
	*/
}

func processAlloc(pass *analysis.Pass, alloc *ssa.Alloc) {
	typ := alloc.Type()

	if ptrType, ok := typ.(*types.Pointer); ok {
		typ = ptrType.Elem()
	}

	// reflect typ: types.Named, typ.Underlying() typ: types.Struct
	if typ.String() == CheckReturnType {
		issue := diff.NewIssue(pass.Fset, alloc.Pos(), "easy项目不允许自定义错误码,需要在平台中进行注册")
		if diff.Differ.IssueNeedReport(issue) {
			pass.Report(*issue.Diagnostic)
		}

	}
}

// 检测easy.CustomError函数中第一项和第二项是否为常数值
func isParamConstForCustomError(callCommon *ssa.CallCommon) bool {
	args := callCommon.Args
	if len(args) != 3 {
		return false
	}

	return isConst(args[0]) && isConst(args[1])
}

func isConst(v ssa.Value) bool {
	_, ok := v.(*ssa.Const)
	return ok
}

// 过滤单测
func isTestFile(pass *analysis.Pass, pos token.Pos) bool {
	fileName := pass.Fset.Position(pos).Filename
	return strings.HasSuffix(fileName, "_test.go")
}
