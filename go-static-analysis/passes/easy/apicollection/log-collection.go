package apicollection

import (
	"fmt"
	"strings"

	"golang.org/x/tools/go/callgraph"
	"golang.org/x/tools/go/ssa"
)

const (
	StructSLogType = "icode.baidu.com/baidu/netdisk/easy-go-sdk/library/slog.StructLog"
	LogTypeWarning = "Warning"
	LogTypeError   = "Error"
)

func (r *Runner) collectLogMsg(routerName string, root *callgraph.Node) {
	apiName := root.Func.Name()
	curDepth := 0
	r.collectLogMsgRecursion(routerName, apiName, root, curDepth)
}

func (r *Runner) collectLogMsgRecursion(routerName string, apiName string, node *callgraph.Node, curDepth int) {
	if curDepth > r.MaxDepth {
		return
	}

	fn := node.Func
	if fn == nil {
		return
	}
	if _, ok := r.FuncsVisit[fn]; ok {
		return
	}
	r.FuncsVisit[fn] = struct{}{}

	for _, block := range fn.DomPreorder() {
		for _, instr := range block.Instrs {
			switch instr := instr.(type) {
			case *ssa.Call:
				r.collectLogMsgForSSACall(routerName, apiName, instr)
			default:
				// do nothing
			}
		}
	}

	for _, out := range node.Out {
		if !r.isNodeNeedVisit(out.Callee) {
			continue
		}

		calleeNode := out.Callee
		curDepth = curDepth + 1
		r.collectLogMsgRecursion(routerName, apiName, calleeNode, curDepth)
	}

	delete(r.FuncsVisit, fn)

}

func (r *Runner) collectLogMsgForSSACall(routerName string, apiName string, call *ssa.Call) {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return
	}
	if fn.Pkg == nil || fn.Pkg.Pkg == nil {
		return
	}

	r.addTangramLogggerLog(routerName, apiName, fn, call)
	r.addSlogLog(routerName, apiName, fn, call)
}

// adapter.Logger对象下的日志
func (r *Runner) addTangramLogggerLog(routerName string, apiName string, fn *ssa.Function, call *ssa.Call) {
	const pkgName = "icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
	pkgPath := fn.Pkg.Pkg.Path()
	if !strings.Contains(pkgPath, pkgName) {
		return
	}

	fnName := fn.Name()
	if fnName != LogTypeError && fnName != LogTypeWarning {
		return
	}
	logType := fnName

	args := call.Call.Args
	if len(args) == 0 {
		return
	}

	const loggerType = "icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger.Logger"
	firstArg := args[0] // 第一个参数可能为logger对象或者 const
	v, ok := firstArg.(*ssa.Const)
	if !ok {
		if len(args) <= 1 {
			return
		}

		v, ok = args[1].(*ssa.Const)
		if !ok {
			return
		}
	}

	logMsgConst := v.Value.ExactString()
	logMsgSlice := r.filterConstMsg(logMsgConst)
	for _, logMsg := range logMsgSlice {
		r.addLogResult(routerName, apiName, logType, logMsg)
	}
}

func (r *Runner) addSlogLog(routerName string, apiName string, fn *ssa.Function, call *ssa.Call) {
	logType, logMsg, found := findSlogFunc(fn, call)
	if found {
		r.addLogResult(routerName, apiName, logType, logMsg)
	}
}

func findSlogFunc(fn *ssa.Function, instr *ssa.Call) (string, string, bool) {
	str := instr.String()
	if ok := strings.Contains(str, StructSLogType); !ok {
		return "", "", false
	}

	fnName := fn.Name()
	if fnName != LogTypeError && fnName != LogTypeWarning {
		return "", "", false
	}
	logType := fnName

	if len(instr.Call.Args) < 2 {
		return "", "", false
	}

	logValue := instr.Call.Args[1]
	logConst, ok := logValue.(*ssa.Const)
	if !ok {
		return "", "", false
	}

	logMsg := logConst.Value.ExactString()
	logMsg = strings.Trim(logMsg, "\"")

	if stringHasWildcard(logMsg) {
		return "", "", false
	}

	return logType, logMsg, true
}

func (r *Runner) filterConstMsg(logMsg string) []string {
	// 有些日志格式不标准, 需要更改以下日志提取的算法
	// 不标准：
	// e.g a: "[msg:params [fsIdList] error] [cid:%+v] [uid:%+v] [opType:%+v] [fsIdList:%+v]"
	// 				--> return []string{"params [fsIdList] error"}
	// e.g b: "superfile2 upload start, uid[%d] uploadPath[%s] uploadid[%s]" --> return  []string{"superfile2 upload start"}
	// 标准写法：
	// e.g c:  [msg:aaa, bbb][k1:v1] [k2:v2]  --> return []string{"aaa"}
	// e.g d： [msg:aaa][k1:v1]               --> return []string{"aaa"}
	// e.g e: SuperFile2Commit failed         --> return []string{"SuperFile2Commit failed"}

	logMsg = strings.Trim(logMsg, "\"") // 过滤传入的引号

	var res []string        // 所有结果
	var item []byte         // 结果的一项
	var bracketStackCnt int // 用于记录存放括号的栈的长度(此处的栈不存在, 类似括号匹配算法）
	var meetLeftBracket = false

	logMsg = strings.TrimSpace(logMsg)
	for i := 0; i < len(logMsg); i++ {
		if logMsg[i] == '[' && !meetLeftBracket && len(item) != 0 { // 适配上述e.g b
			tmp := getConstString(item)
			if tmp != "" {
				res = append(res, tmp)
			}
			item = nil
			bracketStackCnt++
			meetLeftBracket = true
		} else {
			item = append(item, logMsg[i]) // 为了适配 e.g a，因此以下 '[' 和 ']' 也需要append
			if logMsg[i] == '[' {
				bracketStackCnt++
				meetLeftBracket = true
			} else if logMsg[i] == ']' {
				if bracketStackCnt >= 1 {
					bracketStackCnt--
				}
				// stack pop 时，只有栈为空时才进行提取，为了适配 e.g a
				if bracketStackCnt == 0 {
					tmpStr := getConstString(item)
					if tmpStr != "" {
						res = append(res, tmpStr)
					}
					item = nil
				}
			}
		}
	}

	if len(item) != 0 {
		tmpStr := getConstString(item)
		if tmpStr != "" {
			res = append(res, tmpStr)
		}
	}

	return res
}

func getConstString(inputByte []byte) string {
	// 对于 msg:aaa, bbb -> 只返回 aaa
	input := string(inputByte)
	if stringHasWildcard(input) {
		return ""
	}

	input = strings.TrimSpace(input)
	input = strings.TrimPrefix(input, "[")
	input = strings.TrimSuffix(input, "]")
	input = strings.TrimSpace(input)

	tmpSlice := strings.Split(input, ":")
	if len(tmpSlice) == 0 {
		return ""
	} else if len(tmpSlice) == 1 {
		input = tmpSlice[0]
	} else if len(tmpSlice) >= 2 {
		input = tmpSlice[1]
	}

	tmpSlice = strings.Split(input, ",")
	if len(tmpSlice) != 0 {
		input = tmpSlice[0]
	}

	return strings.TrimSpace(input)
}

func stringHasWildcard(str string) bool {
	if strings.Contains(str, "%") {
		return true
	}
	return false
}

func (r *Runner) collectLogMsgForSSAReturn(routerName string, apiName string, ret *ssa.Return) {
	if len(ret.Results) == 0 {
		return
	}

	errValue := ret.Results[len(ret.Results)-1]
	switch errValue := errValue.(type) {
	case *ssa.MakeInterface:
		r.ssaReturnItemIsMakeInterface(routerName, apiName, errValue)
	case *ssa.UnOp:
		r.getErrorDtoFromSSAUnOp(routerName, apiName, errValue)
	}
}

func (r *Runner) addLogResult(routerName, apiName string, logType string, logMsg string) {
	apiMap, ok := r.LogResults[routerName]
	if !ok {
		apiMap = make(map[string]map[string]string)
	}

	logMap, ok := apiMap[apiName]
	if !ok {
		logMap = make(map[string]string)
	}

	logMap[logMsg] = logType
	apiMap[apiName] = logMap
	r.LogResults[routerName] = apiMap
}

func (r *Runner) printLogResult() {
	for routerName, apiMap := range r.LogResults {
		for apiName, resultMap := range apiMap {
			fmt.Println("groupName", routerName, ", apiName: ", apiName)
			for logMsg := range resultMap {
				fmt.Printf("	log: -%s-\n", logMsg)
			}
			fmt.Println("")
		}
	}
}
