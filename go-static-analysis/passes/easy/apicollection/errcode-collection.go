package apicollection

import (
	"fmt"
	"strings"

	"golang.org/x/tools/go/callgraph"
	"golang.org/x/tools/go/ssa"
)

type CustomErrParam struct {
	ErrNo    string
	Msg      string
	HTTPCode string
}

func NewCustomErrParam(errCode string, msg string, httpCode string) *CustomErrParam {
	res := &CustomErrParam{
		ErrNo:    errCode,
		Msg:      msg,
		HTTPCode: httpCode,
	}
	return res
}

func (r *Runner) collectErrCode(routerName string, root *callgraph.Node) {
	apiName := root.Func.Name()
	curDepth := 0
	r.collectErrCodeRecursion(routerName, apiName, root, curDepth)
}

func (r *Runner) collectErrCodeRecursion(routerName string, apiName string, node *callgraph.Node, curDepth int) {
	if curDepth > r.MaxDepth {
		return
	}

	fn := node.Func
	if fn == nil {
		return
	}
	if _, ok := r.FuncsVisit[fn]; ok {
		return
	}
	r.FuncsVisit[fn] = struct{}{}

	for _, block := range fn.DomPreorder() {
		for _, instr := range block.Instrs {
			switch instr := instr.(type) {
			case *ssa.Return:
				r.collectErrCodeForSSAReturn(routerName, apiName, instr)
			case *ssa.Call:
				r.collectErrCodeForCallParam(routerName, apiName, instr)
			case *ssa.UnOp:
				r.getErrorDtoFromSSAUnOp(routerName, apiName, instr)
			default:
				// do nothing
			}
		}
	}

	for _, out := range node.Out {
		if !r.isNodeNeedVisit(out.Callee) {
			continue
		}

		calleeNode := out.Callee
		curDepth = curDepth + 1
		r.collectErrCodeRecursion(routerName, apiName, calleeNode, curDepth)
	}
	delete(r.FuncsVisit, fn)

}

func (r *Runner) collectErrCodeForCallParam(routerName string, apiName string, call *ssa.Call) {
	var values []*ssa.Value
	ops := call.Operands(values)
	for _, op := range ops {
		switch instr := (*op).(type) {
		case *ssa.UnOp:
			r.getErrorDtoFromSSAUnOp(routerName, apiName, instr)
		}
	}
}

func (r *Runner) collectErrCodeForSSAReturn(routerName string, apiName string, ret *ssa.Return) {
	if len(ret.Results) == 0 {
		return
	}

	errValue := ret.Results[len(ret.Results)-1]
	switch errValue := errValue.(type) {
	case *ssa.MakeInterface:
		r.ssaReturnItemIsMakeInterface(routerName, apiName, errValue)
	case *ssa.UnOp:
		r.getErrorDtoFromSSAUnOp(routerName, apiName, errValue)
	}
}

func (r *Runner) ssaReturnItemIsMakeInterface(routerName string, apiName string, value *ssa.MakeInterface) {
	var ops []*ssa.Value
	ops = value.Operands(ops)

	for _, opPtr := range ops {
		switch op := (*opPtr).(type) {
		case *ssa.UnOp:
			r.getErrorDtoFromSSAUnOp(routerName, apiName, op)
		default:
			// do nothing
		}
	}
}

func (r *Runner) getErrorDtoFromSSAUnOp(routerName string, apiName string, op *ssa.UnOp) {
	if global, ok := op.X.(*ssa.Global); ok {
		_, ok := r.getCustomErrCache(global)
		if !ok {
			return
		}
		r.addErrCodeResult(routerName, apiName, global.Name())
	}
}

func (r *Runner) getCustomErrMsgForCall(call *ssa.Call) (*CustomErrParam, bool) {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return nil, false
	}

	if fn.Pkg == nil || fn.Pkg.Pkg == nil {
		return nil, false
	}

	const pkgName = "icode.baidu.com/baidu/netdisk/easy-go-sdk"
	const funcName = "CustomError"

	pkgPath := fn.Pkg.Pkg.Path()
	if fn.Name() != funcName || !strings.Contains(pkgPath, pkgName) {
		return nil, false
	}

	args := call.Call.Args
	if len(args) != 3 {
		return nil, false
	}

	errCode := args[0]
	msg := args[1]
	httpCode := args[2]

	errCodeConst, ok1 := isConst(errCode)
	msgConst, ok2 := isConst(msg)
	httpCodeConst, ok3 := isConst(httpCode)
	if !ok1 || !ok2 || !ok3 {
		return nil, false
	}

	realErrCode := errCodeConst.Value.ExactString()
	realMsg := msgConst.Value.ExactString()
	realHTTPCode := httpCodeConst.Value.ExactString()

	res := NewCustomErrParam(realErrCode, realMsg, realHTTPCode)
	return res, true
}

func (r *Runner) getCustomErrCache(global *ssa.Global) (*CustomErrParam, bool) {
	res, ok := r.CustomErrParamCache[global]
	return res, ok
}

func (r *Runner) addErrCodeResult(routerName string, apiName string, varName string) {
	groupMap, ok := r.ErrCodeResults[routerName]
	if !ok {
		groupMap = make(map[string]map[string]struct{})
	}

	specificAPINameMap, ok := groupMap[apiName]
	if !ok {
		specificAPINameMap = make(map[string]struct{})
	}

	specificAPINameMap[varName] = struct{}{}
	groupMap[apiName] = specificAPINameMap
	r.ErrCodeResults[routerName] = groupMap
}

func (r *Runner) setCustomErrCacheForStore(store *ssa.Store) {
	global, call, ok := r.getGlobalAndCallFromStore(store)
	if !ok {
		return
	}

	customErrParam, ok := r.getCustomErrMsgForCall(call)
	if !ok {
		return
	}

	r.CustomErrParamCache[global] = customErrParam
}

func (r *Runner) printErrCodeResult() {
	for routerName, apiMap := range r.ErrCodeResults {
		for apiName, resultMap := range apiMap {
			fmt.Println("groupName: ", routerName, "apiName: ", apiName)
			for errCode := range resultMap {
				fmt.Println("	errcode: ", errCode)
			}
		}
	}
}
