package apicollection

import (
	"fmt"
	"strings"

	"golang.org/x/tools/go/callgraph"
	"golang.org/x/tools/go/ssa"
)

func (r *Runner) collectDownStream(routerName string, root *callgraph.Node) {
	apiName := root.Func.Name()
	curDepth := 0
	r.collectDownStreamRecursion(routerName, apiName, root, curDepth)
}

func (r *Runner) collectDownStreamRecursion(routerName string, apiName string, node *callgraph.Node, curDepth int) {
	if curDepth > r.MaxDepth {
		return
	}

	fn := node.Func
	if fn == nil {
		return
	}
	if _, ok := r.FuncsVisit[fn]; ok {
		return
	}
	r.FuncsVisit[fn] = struct{}{}

	for _, block := range fn.DomPreorder() {
		for _, instr := range block.Instrs {
			switch instr := instr.(type) {
			case *ssa.Call:
				r.collectDownStreamForSSACall(routerName, apiName, instr)
			default:
				// do nothing
			}
		}
	}

	for _, out := range node.Out {
		if !r.isNodeNeedVisit(out.Callee) {
			continue
		}

		calleeNode := out.Callee
		curDepth = curDepth + 1
		r.collectDownStreamRecursion(routerName, apiName, calleeNode, curDepth)
	}

	delete(r.FuncsVisit, fn)

}

func (r *Runner) collectDownStreamForSSACall(routerName string, apiName string, call *ssa.Call) {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return
	}

	if fn.Pkg == nil || fn.Pkg.Pkg == nil {
		return
	}

	pkgPath := fn.Pkg.Pkg.Path()
	if fn.Name() != "Do" || !strings.HasSuffix(pkgPath, "entity/remote") {
		return
	}

	args := call.Call.Args
	if len(args) == 0 {
		return
	}

	receiver := args[0]
	receiverType := receiver.Type()
	if receiverType == nil {
		return
	}

	downStream, ok := r.DownStreamCache[receiverType.String()]
	if !ok {
		return
	}

	downStream = strings.TrimSuffix(downStream, "\"")
	downStream = strings.TrimPrefix(downStream, "\"")
	r.addDownStreamResult(routerName, apiName, downStream)
}

func (r *Runner) addDownStreamResult(routerName string, apiName string, downStream string) {
	groupMap, ok := r.DownStreamResults[routerName]
	if !ok {
		groupMap = make(map[string]map[string]struct{})
	}

	apiDownStreamMap, ok := groupMap[apiName]
	if !ok {
		apiDownStreamMap = make(map[string]struct{})
	}

	apiDownStreamMap[downStream] = struct{}{}
	groupMap[apiName] = apiDownStreamMap
	r.DownStreamResults[routerName] = groupMap
}

func (r *Runner) printDownStreamResult() {
	for groupName, apiMap := range r.DownStreamResults {
		for apiName, resultMap := range apiMap {
			fmt.Println("groupName: ", groupName, ", apiName: ", apiName)
			for log := range resultMap {
				fmt.Println("	downstream: ", log)
			}
			fmt.Println("")
		}
	}
}
