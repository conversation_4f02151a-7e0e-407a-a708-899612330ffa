package apicollection

import (
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"net/http"
	"os"

	"github.com/bitly/go-simplejson"
	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const (
	RetryCnt            = 3
	ConnectTimeout      = 1000
	ReadWriteTimeout    = 5000
	MaxDepth            = 36 // 函数递归深度为36层
	APIMsgCollectionURL = "http://10.138.34.59:8089/code/ssaInfo"
)

var (
	testFlag = flag.Bool("tests", false, "Include test code.")
)

func Main() {
	flag.Parse()

	args := flag.Args()

	prog, g, err := NewProgramAndGraph("", *testFlag, args)
	if err != nil {
		fmt.Println(err)
		os.Exit(1)
	}

	runner := NewRunner(<PERSON>Depth, prog, g)
	if err := runner.initAll(); err != nil {
		fmt.Println("init all failed, err is: ", err)
		os.Exit(1)
	}

	runner.run()
	/*
		runner.printLogResult()
		runner.printErrCodeResult()
		runner.printDownStreamResult()
	*/

	if err := runner.sendAPIMsgCollection(); err != nil {
		fmt.Println("sendAPIMsgCollection failed, err is ", err)
		return
	}

}

func (r *Runner) run() {
	for fn, routerName := range r.TargetFunctions {
		r.runForRootNode(fn, routerName) // 并发效果不明显，需要对几个Map加锁，锁冲突严重，此处串行执行
	}
}

func (r *Runner) runForRootNode(fn *ssa.Function, routerName string) {
	root, ok := r.Graph.Nodes[fn]
	if !ok {
		return
	}

	r.collectErrCode(routerName, root)    // 错误码收集
	r.collectLogMsg(routerName, root)     // 日志收集
	r.collectDownStream(routerName, root) // 收集下游服务
}

type APIMsgCollectionReqBody struct {
	ICode string                     `json:"icode"`
	Info  []*APIMsgCollectionReqInfo `json:"info"`
}

type APIMsgCollectionReqInfo struct {
	Router      string   `json:"router"`
	APIName     string   `json:"api_name"`
	GroupName   string   `json:"group_name"`
	Errs        []string `json:"errs"`
	LogsWarn    []string `json:"log_warn"`
	LogsError   []string `json:"log_error"`
	DownStreams []string `json:"downstreams"`
}

func (r *Runner) newAPIMsgCollectionReqBody() (*APIMsgCollectionReqBody, error) {
	res := &APIMsgCollectionReqBody{}
	res.ICode = r.ICodeName

	type APIMsg struct {
		Errs        []string `json:"errs"`
		LogsWarn    []string `json:"log_warn"`
		LogsError   []string `json:"log_error"`
		DownStreams []string `json:"downstreams"`
	}

	tmpAPIMsgMap := map[string]map[string]*APIMsg{}

	// 合并三份数据

	// 合并日志数据
	for routerName, apiMap := range r.LogResults {
		apiMsgMap, ok := tmpAPIMsgMap[routerName]
		if !ok {
			apiMsgMap = make(map[string]*APIMsg)
		}
		for apiName, logMap := range apiMap {
			apiMsg, ok := apiMsgMap[apiName]
			if !ok {
				apiMsg = &APIMsg{}
			}
			for logMsg, logType := range logMap {
				if logType == LogTypeWarning {
					apiMsg.LogsWarn = append(apiMsg.LogsWarn, logMsg)
				} else if logType == LogTypeError {
					apiMsg.LogsError = append(apiMsg.LogsError, logMsg)
				}

			}

			apiMsgMap[apiName] = apiMsg
		}
		tmpAPIMsgMap[routerName] = apiMsgMap
	}

	// 合并错误码数据
	for routerName, apiMap := range r.ErrCodeResults {
		apiMsgMap, ok := tmpAPIMsgMap[routerName]
		if !ok {
			apiMsgMap = make(map[string]*APIMsg)
		}
		for apiName, errCodeMap := range apiMap {
			apiMsg, ok := apiMsgMap[apiName]
			if !ok {
				apiMsg = &APIMsg{}
			}
			for errCode := range errCodeMap {
				apiMsg.Errs = append(apiMsg.Errs, errCode)
			}

			apiMsgMap[apiName] = apiMsg
		}
		tmpAPIMsgMap[routerName] = apiMsgMap
	}

	// 合并下游数据
	for routerName, apiMap := range r.DownStreamResults {
		apiMsgMap, ok := tmpAPIMsgMap[routerName]
		if !ok {
			apiMsgMap = make(map[string]*APIMsg)
		}
		for apiName, downStreamMap := range apiMap {
			apiMsg, ok := apiMsgMap[apiName]
			if !ok {
				apiMsg = &APIMsg{}
			}
			for downStream := range downStreamMap {
				apiMsg.DownStreams = append(apiMsg.DownStreams, downStream)
			}
			apiMsgMap[apiName] = apiMsg
		}
		tmpAPIMsgMap[routerName] = apiMsgMap
	}

	var infos []*APIMsgCollectionReqInfo
	for routerName, apiMap := range tmpAPIMsgMap {
		for apiName, apiMsg := range apiMap {
			if apiMsg == nil {
				continue
			}
			reqInfo := &APIMsgCollectionReqInfo{}
			reqInfo.APIName = apiName
			reqInfo.Router = routerName
			reqInfo.GroupName = r.RouterGroupMap[routerName]

			if apiMsg.LogsWarn == nil {
				reqInfo.LogsWarn = []string{}
			} else {
				reqInfo.LogsWarn = apiMsg.LogsWarn
			}
			if apiMsg.LogsError == nil {
				reqInfo.LogsError = []string{}
			} else {
				reqInfo.LogsError = apiMsg.LogsError
			}

			if apiMsg.Errs == nil {
				reqInfo.Errs = []string{}
			} else {
				reqInfo.Errs = apiMsg.Errs
			}

			if apiMsg.DownStreams == nil {
				reqInfo.DownStreams = []string{}
			} else {
				reqInfo.DownStreams = apiMsg.DownStreams
			}

			infos = append(infos, reqInfo)
		}
	}

	if len(infos) == 0 {
		utils.SendWarnMessageToHiForDailyMessage("api collect msg is empty")
		return nil, errors.New("api collect msg is empty")
	}

	res.Info = infos
	return res, nil
}

func (r *Runner) sendAPIMsgCollection() error {
	header := make(map[string]string)
	header["Content-Type"] = "application/json"

	body, err := r.newAPIMsgCollectionReqBody()
	if err != nil {
		fmt.Printf("newAPIMsgCollectionReqBody failed, err is %v\n", err)
		return err
	}

	bodyJSON, err := json.Marshal(body)
	if err != nil {
		fmt.Printf("json marshal failed, err is %v\n", err)
		return err
	}

	bodyJSONString := string(bodyJSON)
	/*
		fmt.Println("body json string: ")
		fmt.Println(bodyJSONString)
	*/
	var resp httpclient.HttpResponse
	var postErr error
	for i := 0; i < RetryCnt; i++ {
		resp, postErr = httpclient.Post(APIMsgCollectionURL, header, ConnectTimeout, ReadWriteTimeout, bodyJSONString)
		if postErr != nil {
			continue
		} else {
			fmt.Println("url is: ", APIMsgCollectionURL, ", api msg collection success body is: ", bodyJSONString)
		}
		// http Post成功，可直接break退出处理
		break
	}

	if postErr != nil {
		fmt.Printf("send apimsg failed, err is %v", postErr)
		return postErr
	}

	js, err := simplejson.NewJson(resp.Body)
	if err != nil {
		fmt.Printf("simplejson.NewJson failed err is %v", err)
		return err
	}

	errno, err := js.Get("errno").Int()
	if err != nil {
		fmt.Println("get errno failed, err is ", err)
		return err
	}
	msg, err := js.Get("msg").String()
	if err != nil {
		fmt.Println("get msg failed, err is ", err)
		return err
	}

	if errno != http.StatusOK {
		errMsg := fmt.Sprintf("send api msg collection failed, errno: %d, msg: %s", errno, msg)
		utils.SendWarnMessageToHiForDailyMessage(errMsg)
		return errors.New(errMsg)
	}

	return nil
}
