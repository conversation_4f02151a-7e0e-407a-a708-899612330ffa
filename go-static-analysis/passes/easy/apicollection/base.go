package apicollection

import (
	"errors"
	"fmt"
	"go/build"
	"go/constant"
	"go/token"
	"go/types"
	"log"
	"strings"

	"golang.org/x/tools/go/callgraph"
	"golang.org/x/tools/go/callgraph/cha"
	"golang.org/x/tools/go/packages"
	"golang.org/x/tools/go/ssa"
	"golang.org/x/tools/go/ssa/ssautil"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

type Runner struct {
	Prog  *ssa.Program
	Graph *callgraph.Graph

	MaxDepth        int
	ICodeName       string
	RepoName        string
	ControllerPairs []ControllerPair
	TargetFunctions map[*ssa.Function]string

	// 一些处理过程中需要获取的cache
	DownStreamCache map[string]string

	CustomErrParamCache map[*ssa.Global]*CustomErrParam

	FuncsVisit map[*ssa.Function]struct{}

	RouterGroupMap map[string]string

	LogResults        map[string]map[string]map[string]string   // api->logresult
	ErrCodeResults    map[string]map[string]map[string]struct{} // api->errcode
	DownStreamResults map[string]map[string]map[string]struct{} // api->downstream
}

type ControllerPair struct {
	Name string
	Path string
}

func NewRunner(maxDepth int, prog *ssa.Program, gragh *callgraph.Graph) *Runner {
	runner := &Runner{
		MaxDepth: maxDepth,
		Prog:     prog,
		Graph:    gragh,
	}

	runner.TargetFunctions = make(map[*ssa.Function]string)

	runner.DownStreamCache = make(map[string]string)
	runner.CustomErrParamCache = make(map[*ssa.Global]*CustomErrParam)

	// 检测结果
	runner.LogResults = make(map[string]map[string]map[string]string)
	runner.ErrCodeResults = make(map[string]map[string]map[string]struct{})
	runner.DownStreamResults = make(map[string]map[string]map[string]struct{})
	runner.FuncsVisit = make(map[*ssa.Function]struct{})
	runner.RouterGroupMap = make(map[string]string)

	return runner
}

func NewProgramAndGraph(dir string, tests bool, args []string) (*ssa.Program, *callgraph.Graph, error) {
	cfg := &packages.Config{
		Mode:       packages.LoadAllSyntax,
		Tests:      tests,
		Dir:        dir,
		BuildFlags: build.Default.BuildTags,
	}

	initial, err := packages.Load(cfg, args...)
	if err != nil {
		return nil, nil, err
	}

	if packages.PrintErrors(initial) > 0 {
		return nil, nil, errors.New("packages contain errors")
	}

	prog, _ /*pkg*/ := ssautil.AllPackages(initial, 0)
	prog.Build()
	graph := cha.CallGraph(prog)

	return prog, graph, nil
}

func (r *Runner) initAll() error {
	if err := r.initRepoNameAndICodeName(); err != nil {
		return err
	}

	r.initControllerNames()
	if err := r.initFunctions(); err != nil {
		return err
	}

	r.initCustomErrCache()
	r.initDownStreamCache()

	return nil
}

func (r *Runner) initControllerNames() {
	var controllerPair []ControllerPair

	const (
		actionPkgName = "action" // controller 所在包
		structSuffix  = "Controller"
	)

	for _, pkg := range r.Prog.AllPackages() {
		if pkg.Pkg.Name() != actionPkgName {
			continue
		}

		for _, mem := range pkg.Members {
			ssaType, ok := mem.(*ssa.Type)
			if !ok {
				continue
			}
			if !strings.HasSuffix(ssaType.Name(), structSuffix) {
				continue
			}

			path := r.Prog.Fset.Position(ssaType.Pos()).Filename
			name := ssaType.Name()

			pair := ControllerPair{
				Name: name,
				Path: path,
			}
			controllerPair = append(controllerPair, pair)

		}
	}

	r.ControllerPairs = controllerPair
}

func (r *Runner) initFunctions() error {
	controllerNameMap, err := r.getCotrollerObjects()
	if err != nil {
		return err
	}

	const targetFunctionSuffix = "Core"
	// 遍历每个结构体
	fmt.Println("map: ", controllerNameMap)
	for pair := range controllerNameMap {
		methodSet := r.Prog.MethodSets.MethodSet(pair.Type)
		if methodSet == nil {
			continue
		}

		groupName := strings.TrimSuffix(pair.Name, "Controller")
		// 获取 route name
		file := r.Prog.Fset.File(pair.Pos).Name()
		router, err := r.getRouter(file)
		if err != nil {
			continue
		}
		r.RouterGroupMap[router] = groupName

		// 遍历该结构体的所有方法以获取所需的方法
		for i := 0; i < methodSet.Len(); i++ {
			selection := methodSet.At(i)
			if selection == nil || selection.Obj() == nil {
				continue
			}

			name := selection.Obj().Name()
			if strings.HasSuffix(name, targetFunctionSuffix) {
				fn := r.Prog.MethodValue(selection)
				r.TargetFunctions[fn] = router
			}
		}
	}

	return nil
}

func (r *Runner) getRouter(fileName string) (string, error) {
	actionName := strings.Join([]string{r.ICodeName, "action/"}, "/")
	icodeIndex := strings.Index(fileName, actionName)

	subName := fileName[icodeIndex+len(actionName):]

	lastIndex := strings.LastIndex(subName, "/")
	if lastIndex == -1 {
		return "", fmt.Errorf("get router name failed, file: %s", fileName)
	}
	res := subName[:lastIndex]
	fmt.Println("subName: ", subName, ", res: ", res, ", icode: ", r.ICodeName, ", fileName: ", fileName)

	return res, nil
}

func (r *Runner) initCustomErrCache() { // 需要先获取，否则在代码上下文中无法关联起来
	allPkgs := r.Prog.AllPackages()
	for _, pkg := range allPkgs {
		if !isPkgNeedVisitForInitCustomErrCache(pkg) {
			continue
		}

		r.initCustomErrCacheForPkg(pkg)
	}
}

func (r *Runner) initCustomErrCacheForPkg(pkg *ssa.Package) {
	for _, member := range pkg.Members {
		fn, ok := member.(*ssa.Function)
		if !ok {
			continue
		}

		if !strings.Contains(fn.Name(), "init") {
			continue
		}

		for _, block := range fn.DomPreorder() {
			for _, instr := range block.Instrs {
				switch instr := instr.(type) {
				case *ssa.Store:
					r.setCustomErrCacheForStore(instr)
				default:
					// do nothing
				}
			}
		}
	}
}

func isPkgNeedVisitForInitCustomErrCache(pkg *ssa.Package) bool {
	if strings.Contains(pkg.Pkg.String(), "lib/errs") {
		return true
	}

	return false
}

func (r *Runner) initDownStreamCache() {
	for _, pkg := range r.Prog.AllPackages() {
		if pkg.Pkg.Name() == "remote" && strings.HasSuffix(pkg.Pkg.Path(), "entity/remote") {
			r.initDownStreamCacheForRemotePkg(pkg)
		}
	}
}

// 下游服务定义在 remote 包
func (r *Runner) initDownStreamCacheForRemotePkg(pkg *ssa.Package) {
	if pkg.Pkg.Name() != "remote" || !strings.HasSuffix(pkg.Pkg.Path(), "entity/remote") {
		return
	}

	for _, member := range pkg.Members {
		switch member := member.(type) {
		case *ssa.Function:
			r.initDownStreamCacheForFunction(member)
		default:
			// do nothing
		}
	}
}

func (r *Runner) initDownStreamCacheForFunction(fn *ssa.Function) {
	if !r.isNewClientFn(fn) {
		return
	}

	r.initDownStreamCacheForNewClientFn(fn)
}

func (r *Runner) isNewClientFn(fn *ssa.Function) bool {
	fnName := fn.Name()

	if !strings.HasPrefix(fnName, "New") || !strings.HasSuffix(fnName, "Client") {
		return false
	}

	if len(fn.Params) != 1 {
		return false
	}

	param := fn.Params[0]
	if !strings.Contains(param.Type().String(), "icode.baidu.com/baidu/netdisk/easy-go-sdk.Context") {
		return false
	}

	return true
}

func (r *Runner) initRepoNameAndICodeName() error {
	repoName, err := utils.ExecCommand("go list -m")
	if err != nil {
		return err
	}

	r.RepoName = strings.TrimSpace(repoName)

	icodeName, err := utils.ExecCommand(`git remote -v | head -1 | awk {'print $2'}`)
	if err != nil {
		return err
	}
	icodeName = strings.TrimSpace(icodeName)
	i := strings.Index(icodeName, "8235/baidu")
	if i == -1 || i+5 < 0 {
		return errors.New("repo not fount 8235/baidu in git remote")
	}

	r.ICodeName = icodeName[i+5:]
	return nil
}

func (r *Runner) initDownStreamCacheForNewClientFn(fn *ssa.Function) {
	for _, block := range fn.DomPreorder() {
		for _, instr := range block.Instrs {
			switch instr := instr.(type) {
			case *ssa.Store:
				r.initDownStreamCacheForSSAStore(instr)
			default:
				// do nothing
			}
		}
	}
}

func (r *Runner) initDownStreamCacheForSSAStore(store *ssa.Store) {
	addr := store.Addr
	val := store.Val

	constVal, ok := val.(*ssa.Const)
	if !ok {
		return
	}

	if constVal.Value.Kind() != constant.String {
		return
	}

	fieldAddr, ok := addr.(*ssa.FieldAddr)
	if !ok {
		return
	}

	var opsForConstClientName []*ssa.Value
	opsForConstClientName = fieldAddr.Operands(opsForConstClientName)
	if len(opsForConstClientName) != 1 {
		return
	}

	ufcClientValue := *opsForConstClientName[0]
	if !strings.Contains(ufcClientValue.Type().String(), "icode.baidu.com/baidu/netdisk/easy-go-sdk.UfcClient") {
		// type: *icode.baidu.com/baidu/netdisk/easy-go-sdk.UfcClient
		return
	}

	ufcClientValueFiledAddr, ok := ufcClientValue.(*ssa.FieldAddr)
	if !ok {
		return
	}

	var opsForUfcClient []*ssa.Value
	opsForUfcClient = ufcClientValueFiledAddr.Operands(opsForUfcClient)
	if len(opsForUfcClient) != 1 {
		return
	}

	newClientValue := *opsForUfcClient[0]
	newClientValueIsSSAAlloc, ok := newClientValue.(*ssa.Alloc)
	if !ok {
		return
	}

	typ := newClientValueIsSSAAlloc.Type()
	if typ == nil {
		return
	}
	r.DownStreamCache[typ.String()] = constVal.Value.ExactString()
}

func (r *Runner) getGlobalAndCallFromStore(store *ssa.Store) (*ssa.Global, *ssa.Call, bool) {
	addr := store.Addr
	val := store.Val

	g, ok := addr.(*ssa.Global)
	if !ok {
		return nil, nil, false
	}

	c, ok := val.(*ssa.Call)
	if !ok {
		return nil, nil, false
	}

	return g, c, true
}

type TypePosPair struct {
	Name string
	Type types.Type
	Pos  token.Pos
}

func (r *Runner) getCotrollerObjects() (map[TypePosPair]struct{}, error) {
	res := make(map[TypePosPair]struct{})
	for _, controllerPair := range r.ControllerPairs {
		typ, pos, err := r.getCotorlllerObject(controllerPair)
		if err != nil {
			log.Println(err)
			continue
		}

		if typ == nil {
			continue
		}

		realType := typ
		if _, ok := typ.(*types.Named); ok {
			realType = types.NewPointer(typ)
		}

		typePosPair := TypePosPair{
			Name: controllerPair.Name,
			Type: realType,
			Pos:  pos,
		}
		res[typePosPair] = struct{}{}
	}
	return res, nil
}

func (r *Runner) getCotorlllerObject(pair ControllerPair) (types.Type, token.Pos, error) {
	for _, pkg := range r.Prog.AllPackages() {
		obj := pkg.Pkg.Scope().Lookup(pair.Name)
		if obj == nil {
			continue
		}

		if r.Prog.Fset.Position(obj.Pos()).Filename != pair.Path {
			continue
		}

		fmt.Println("file name: ", r.Prog.Fset.Position(obj.Pos()).Filename)
		fmt.Println("path: ", pair.Path)

		fmt.Println("match")
		fmt.Println("")

		return obj.Type(), obj.Pos(), nil

	}

	return nil, token.NoPos, fmt.Errorf("the controller %s not found", pair)
}

func (r *Runner) isNodeNeedVisit(node *callgraph.Node) bool {
	if strings.Contains(node.Func.String(), r.RepoName) {
		return true
	}

	// 该函数需要跟进去，否则获取不了其中我们传入的闭包函数的日志
	if node.Func.Name() == "ExecWithTx" && strings.Contains(node.Func.String(), "baidu/netdisk/easy-go-sdk") {
		return true
	}

	return false
}

func isConst(value ssa.Value) (*ssa.Const, bool) {
	res, ok := value.(*ssa.Const)
	if ok {
		return res, true
	}
	return nil, false
}
