package naccheck

import (
	"go/ast"
	"strings"

	"golang.org/x/tools/go/analysis"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
)

// 如果 import 的库中使用了以下库，则可能出现了使用 nac 的情形
var NacLib = []string{
	"baidu/netdisk/clouddisk-golib/nac",
	"baidu/netdisk-apaas/apaas-golib/nac",
	"baidu/giano/giano-sdk",
	"baidu/netdisk/NAC",
}

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:  "naccheck",
		Name: "naccheck",
		Run:  run,
	}
}

func run(pass *analysis.Pass) (interface{}, error) {
	for _, file := range pass.Files {
		for _, importSpec := range file.Imports {
			checkImports(pass, importSpec)
		}
	}
	return nil, nil
}

func checkImports(pass *analysis.Pass, importSpec *ast.ImportSpec) {
	if importSpec == nil || importSpec.Path == nil {
		return
	}

	for _, lib := range NacLib {
		path := importSpec.Path.Value
		if strings.Contains(path, lib) {
			issue := diff.NewIssue(pass.Fset, importSpec.Pos(), "easy项目已提供了访问nac能力,请在平台上进行配置,无需自己进行调用")
			if diff.Differ.IssueNeedReport(issue) {
				pass.Report(*issue.Diagnostic)
			}
		}
	}
}
