package checkmain

import (
	"go/ast"
	"path/filepath"
	"strings"

	"golang.org/x/tools/go/analysis"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

const (
	MainName     = "main"
	MainFileName = "main.go"
)

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:  "checkmain",
		Name: "checkmain",
		Run:  run,
	}
}

func run(pass *analysis.Pass) (interface{}, error) {
	for _, file := range pass.Files {
		if ok := file.Pos().IsValid(); !ok {
			continue
		}

		fileName := pass.Fset.Position(file.Pos()).Filename
		if !strings.Contains(fileName, MainFileName) {
			continue
		}

		ok, err := isEasyMain(fileName)
		if err != nil {
			continue
		}
		if !ok {
			continue
		}

		doRule(pass, file)
	}
	return nil, nil
}

func isEasyMain(fileName string) (bool, error) {
	repoName, err := utils.GetProjectName()
	if err != nil {
		return false, err
	}
	mainFile := filepath.Join(repoName, "main.go")
	if strings.Contains(fileName, mainFile) {
		return true, nil
	}
	return false, nil
}

// 检查main.go中是否含有函数:func main() {}
func doRule(pass *analysis.Pass, file *ast.File) {
	for _, decl := range file.Decls {
		funcDecl, ok := decl.(*ast.FuncDecl)
		if !ok {
			continue
		}

		if funcDecl.Name.Name == MainName {
			issue := diff.NewIssue(pass.Fset, funcDecl.Pos(), "easy代码库中main.go不能含有main函数")
			if diff.Differ.IssueNeedReport(issue) {
				pass.Report(*issue.Diagnostic)
			}
			return
		}
	}
}
