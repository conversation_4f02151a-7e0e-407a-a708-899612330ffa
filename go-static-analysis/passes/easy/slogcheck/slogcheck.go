package slogcheck

import (
	"strings"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/buildssa"
	"golang.org/x/tools/go/ssa"
)

const (
	StructSLogType   = "icode.baidu.com/baidu/netdisk/easy-go-sdk/library/slog.StructLog"
	NormLogSLogPrint = "(icode.baidu.com/baidu/netdisk/easy-go-sdk/library/slog.NormLog).Print"
)

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:      "slogcheck",
		Name:     "slogcheck",
		Run:      run,
		Requires: []*analysis.Analyzer{buildssa.Analyzer},
	}
}

func run(pass *analysis.Pass) (interface{}, error) {
	bs := pass.ResultOf[buildssa.Analyzer].(*buildssa.SSA)
	for _, fn := range bs.SrcFuncs {
		DoSLogCheck(pass, fn)
	}
	return nil, nil
}

func DoSLogCheck(pass *analysis.Pass, fn *ssa.Function) {
	for _, blocks := range fn.DomPreorder() {
		for _, instr := range blocks.Instrs {
			switch instr := instr.(type) {
			case *ssa.Call:
				if ok := isFindPrintEntry(instr); ok {
					visitMap := make(map[ssa.Instruction]struct{})
					if !FindPrintFuncInRecursion(pass, instr, visitMap) {
						pass.Reportf(instr.Pos(), "easy项目中, SLog结构体需要调用Print函数")
					}
				}

			}
		}
	}
}

func isFindPrintEntry(instr *ssa.Call) bool {
	str := instr.String()
	if ok := strings.Contains(str, StructSLogType); !ok {
		return false
	}

	fn := instr.Call.StaticCallee()
	if fn == nil {
		return false
	}

	funcs := []string{
		"Info", "Warning", "Error", "Notice", "Debug", "Emergency",
	}

	for _, call := range funcs {
		if fn.Name() == call {
			return true
		}
	}
	return false
}

func FindPrintFuncInRecursion(pass *analysis.Pass, instr ssa.Instruction, visitMap map[ssa.Instruction]struct{}) bool {
	if isPrintFunc(pass, instr) {
		return true
	}

	visitMap[instr] = struct{}{}

	value, ok := instr.(ssa.Value)
	if !ok {
		return false
	}
	instrs := value.Referrers()
	for _, instr := range *instrs {
		if _, ok := visitMap[instr]; ok {
			continue
		}

		// 由于一个函数节点有限，不会出现无线递归情形。以StructLog结构体作为切入点，递归层数有限
		if FindPrintFuncInRecursion(pass, instr, visitMap) {
			return true
		}
	}
	return false
}

func isPrintFunc(pass *analysis.Pass, instr ssa.Instruction) bool {
	call, ok := instr.(*ssa.Call)
	if !ok {
		return false
	}
	if strings.Contains(call.String(), NormLogSLogPrint) {
		return true
	}
	return false
}
