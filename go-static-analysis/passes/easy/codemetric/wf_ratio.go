package codemetric

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/easyutil"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

type WfAPICode struct {
	APIPath   string
	CoreCode  easyutil.EasyCode // 入口代码，核心代码
	ExtCode   easyutil.EasyCode // 自定义打码
	ValidCode easyutil.EasyCode // 验证组件代码

	CoreCodeUserLineCnt  int // 核心代码，用户写的
	CoreCodeEasyLineCnt  int // 核心代码，easy 生成
	ValidCodeUserLineCnt int // 验证组件代码，用户写的 0
	ValidCodeEasyLineCnt int // 验证组件代码，wf 生成

	ExtCodeUserLineCnt int // 业务自己写的
	ExtCodeEasyLineCnt int // easy 生成的, 1行

	IsDiffAPI bool // 该 api 信息是否有变更
}

type SendStatData struct {
	ICodes   string `json:"icode"`
	CommitID string `json:"commit_id"`

	IsWF      bool   `json:"is_wf"`
	ICafeID   string `json:"icafe_id"`
	CommitURL string `json:"commit_url"`
	User      string `json:"user"`
}

func (r *Runner) initWfAPICodes() {
	for _, api := range r.RemoteWFEasyCode {
		r.initWfAPICodeByAPI(api)
	}
}

func (r *Runner) initWfAPICodeByAPI(api easyutil.EasyCode) {
	path := api.Path
	apiCodeStruct, ok := r.WfAPICodeMap[path]
	if !ok {
		apiCodeStruct = WfAPICode{
			APIPath:   path,
			IsDiffAPI: false,
		}
	}
	fileName := api.FileName
	if strings.HasSuffix(fileName, "_ext.go") {
		apiCodeStruct.ExtCode = api
	} else if strings.HasSuffix(fileName, "_valid.go") {
		apiCodeStruct.ValidCode = api
	} else {
		apiCodeStruct.CoreCode = api
	}

	r.WfAPICodeMap[path] = apiCodeStruct
}

func (r *Runner) getSendHiContent(onlyDiff bool) (string, error) {
	if len(r.WfAPICodeMap) == 0 {
		return "", errors.New("send wf stat data is empty")
	}
	var strContent string
	strContent += fmt.Sprintf("模块名称: %s\n", r.ProjectName)
	strContent += fmt.Sprintf("执行时间：%s\n", time.Now().Format("2006/1/2 15:04:05"))
	strContent += fmt.Sprintf("用户: %s\n\n", r.CommitUser)
	//
	wfAPIByte, err := json.Marshal(r.WfAPICodeMap)
	if err != nil {
		fmt.Println("marshal wf apicode map failed, err: ", err)
		return "", err
	}
	projectNameBase := filepath.Base(r.ProjectName)
	fileName := fmt.Sprintf("%s_result.json", projectNameBase)

	if err := os.WriteFile(fileName, wfAPIByte, 0755); err != nil {
		fmt.Println("write wf apicode to local failed, err: ", err)
		return "", err
	}

	formatStr := "WF层用户代码: %d \tWF层生成代码: %d\t 该接口代码生成比例为:%v %%\t 接口:%s\n"
	for apiPath, apiCode := range r.WfAPICodeMap {
		if onlyDiff && !apiCode.IsDiffAPI {
			continue
		}

		easyCodeCnt := apiCode.CoreCodeEasyLineCnt + apiCode.ValidCodeUserLineCnt
		userCodeCnt := apiCode.CoreCodeUserLineCnt + apiCode.ExtCodeEasyLineCnt

		ratio := float64(easyCodeCnt) / float64(userCodeCnt+easyCodeCnt) * 100
		strContent += fmt.Sprintf(formatStr, userCodeCnt, easyCodeCnt, ratio, apiPath)
	}
	// 打印到流水线执行产出中
	fmt.Println(strContent)
	return strContent, nil
}

func (r *Runner) SendWfStatToHi() error {
	onlyDiff := true
	content, err := r.getSendHiContent(onlyDiff)
	if err != nil {
		return err
	}
	utils.SendWFStatMessageToHi(content)

	onlyDiff = false

	return nil
}

// 给王鑫的数据
func (r *Runner) getSendWFDataBody() (*SendStatData, error) {
	sendStatData := &SendStatData{}
	for _, apiCode := range r.WfAPICodeMap {
		if apiCode.IsDiffAPI {
			sendStatData.IsWF = true
			break
		}
	}

	icafeID := r.getICafeID()

	sendStatData.ICafeID = icafeID
	sendStatData.CommitID = r.CommitID
	sendStatData.ICodes = r.ProjectName
	sendStatData.CommitURL = commitURLGlobal
	sendStatData.User = r.CommitUser

	return sendStatData, nil
}

func (r *Runner) getICafeID() string {
	commitMsg := r.CommitMsg

	for str := range r.IcafeMap {
		if !strings.Contains(commitMsg, str) {
			continue
		}

		lastIndex := strings.LastIndex(commitMsg, str)
		subStr := commitMsg[lastIndex:]
		subStrSlice := strings.Split(subStr, " ")
		if len(subStrSlice) != 0 && len(str) <= len(subStrSlice[0]) {
			tmp := subStrSlice[0][len(str):] // 提取数字
			num := ""
			for _, item := range tmp {
				if item < '0' || item > '9' {
					break
				}
				num += string(item)
			}

			return str + num
		}
	}

	msg := fmt.Sprintf("模块: %s\n信息：wf代码度量获取 icafe id 失败\n提交信息: %s\n", r.ProjectName, r.CommitMsg)
	utils.SendWarnMessageToHiForDailyMessage(msg)
	return "none"

}

func (r *Runner) SendICafeRequest() error {
	// httpclient.SendRequest("PUT")
	data, err := r.getSendWFDataBody()
	if err != nil {
		return err
	}

	body := &bytes.Buffer{}
	// Create a multipart writer for our buffer
	writer := multipart.NewWriter(body)

	dataByte, err := json.Marshal(data)
	if err != nil {
		return err
	}
	dataStr := string(dataByte)
	err = writer.WriteField("data", dataStr)
	if err != nil {
		return err
	}

	// Close the writer to finalize the form-data part of the body
	err = writer.Close()
	if err != nil {
		return err
	}

	url := "http://measure.bcc-gzbh.baidu.com:8080/api/saveWFInfo"
	// Create an HTTP request with our body, which is of type multipart/form-data

	// Perform the request
	client := &http.Client{
		Timeout: time.Second * 3,
	}

	var req *http.Request
	var resp *http.Response
	for i := 0; i < 3; i++ {
		req, err = http.NewRequest("POST", url, body)
		if err != nil {
			return err
		}

		// Set the correct content type, this is crucial
		req.Header.Set("Content-Type", writer.FormDataContentType())
		resp, err = client.Do(req)
		if err != nil {
			continue
		}

		break

	}

	content := fmt.Sprintf("时间： %s\n数据: %s", time.Now().Format("2006-01-02 15:04:05"), dataStr)
	utils.SendWarnMessageToHiForDailyMessage(content)

	if err != nil {
		golog.Error("[send icode stat data fail] [url: %s] [data: %s] [err: %v]", url, dataStr, err)
		return err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll((resp.Body))
	if err != nil {
		golog.Error("[io read all failed]")
		return err
	}

	if resp.StatusCode != http.StatusOK {
		golog.Error("[send code stat data failed, response not ok] [url: %s] [data: %v] [resp: %s]", url, dataStr, string(respBody))
		return fmt.Errorf("send code stat data failed, response not ok, url: %s, data: %v", url, dataStr)
	}

	golog.Info("[send code stat data success, response ok] [url: %s] [data: %v] [resp: %s]", url, dataStr, string(respBody))

	return nil
}
