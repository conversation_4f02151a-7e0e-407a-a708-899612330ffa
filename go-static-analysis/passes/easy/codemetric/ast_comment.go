package codemetric

import (
	"fmt"
	"go/ast"
	"go/token"
	"sort"
	"strings"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

type RPCCommentType string

const (
	RPCCommentTypeStart RPCCommentType = "start"
	RPCCommentTypeEnd   RPCCommentType = "end"
)

type LineNumMetrics struct {
	fileName    string
	fileInfo    *FileInfo
	baseFileSet *token.FileSet
	baseFile    *ast.File

	/*
		remoteFileSet *token.FileSet
		remoteFile    *ast.File
		remoteCode    string
	*/
	WfCommentInfos    map[string]map[*WorkflowCommentInfo]struct{}
	WfCommentContents map[string]*WorkflowCommentContent

	RPCPluginCommentInfos      map[int]*RPCPluginCommentInfo // 行号 —> 具体注释
	RPCPluginCommentContentMap map[*RPCPluginCommentContent]struct{}

	CodeMode CodeMode // 完全由easy托管

	WfTotalLine        int
	WfIncreLine        int
	RPCPluginTotalLine int
	RPCPluginIncreLine int
	Runner             *Runner
}

type WorkflowCommentInfo struct {
	LineNumber    int    // 所在行号
	WfCommentType int    // 0:not workflow comment, 1: start, 2: end
	WfCommentID   string // 注释 id, 文件粒度唯一
	FuncKey       string // 所在函数名

	// Func        *ast.FuncDecl // 所在函数具体信息
}

// 没有序号
type RPCPluginCommentInfo struct {
	LineNumber           int // 所在行号
	RPCPluginCommentType RPCCommentType
	FuncKey              string // 所在函数名

	// Func        *ast.FuncDecl // 所在函数具体信息
}

type RPCPluginCommentContent struct {
	FuncName  string
	StartLine int
	EndLine   int
}

type WorkflowCommentContent struct {
	FuncName  string
	StartLine int
	EndLine   int
}

func NewRPCPluginCommentContent(start *RPCPluginCommentInfo, end *RPCPluginCommentInfo) *RPCPluginCommentContent {
	return &RPCPluginCommentContent{
		FuncName:  start.FuncKey,
		StartLine: start.LineNumber,
		EndLine:   end.LineNumber,
	}
}

func NewWorkflowCommentContent(start *WorkflowCommentInfo, end *WorkflowCommentInfo) *WorkflowCommentContent {
	return &WorkflowCommentContent{
		FuncName:  start.FuncKey,
		StartLine: start.LineNumber,
		EndLine:   end.LineNumber,
	}

}

func NewLineNumMetrics(r *Runner, codeMode CodeMode, fileName string, fileInfo *FileInfo, baseFileSet *token.FileSet, baseFile *ast.File) *LineNumMetrics {
	return &LineNumMetrics{
		fileName:          fileName,
		fileInfo:          fileInfo,
		baseFileSet:       baseFileSet,
		baseFile:          baseFile,
		WfCommentInfos:    make(map[string]map[*WorkflowCommentInfo]struct{}),
		WfCommentContents: make(map[string]*WorkflowCommentContent),

		RPCPluginCommentInfos:      make(map[int]*RPCPluginCommentInfo),
		RPCPluginCommentContentMap: make(map[*RPCPluginCommentContent]struct{}),
		CodeMode:                   codeMode,
		Runner:                     r,
	}
}

func (f *LineNumMetrics) Compute() {
	wfTotalLine, wfIncreLine := f.ComputeWfComment()
	rpcTotalLine, rpcIncreLine := f.ComputeRPCComment()
	f.WfIncreLine = wfIncreLine
	f.WfTotalLine = wfTotalLine
	f.RPCPluginIncreLine = rpcIncreLine
	f.RPCPluginTotalLine = rpcTotalLine

	return
}

func (f *LineNumMetrics) genWfCommentInfos() {
	astFile := f.baseFile
	for _, commentGroup := range astFile.Comments {
		for _, comment := range commentGroup.List {
			if !f.isWorkflowComment(comment) {
				continue
			}

			// 获取注释
			f.genWorkflowCommentInfo(comment)
		}
	}

}

func (w *LineNumMetrics) genWorkflowCommentInfo(comment *ast.Comment) {
	fileSet := w.baseFileSet
	astFile := w.baseFile

	// 获取唯一标识
	text := comment.Text
	commentKey, ok := w.getWfCommentKey(text)
	if !ok {
		golog.Warn("[generate worflow code failed] [msg: 禁止修改唯一标识注释 或自己新增wf注释。] [comment: %v]\n", text)
		return
	}

	// 获取 start/end 标识
	commentType := 0 // 既不是 start 也不是 end
	hasStart := false
	hasEnd := false
	if strings.Contains(text, "begin") {
		hasStart = true
	}
	if strings.Contains(text, "end") {
		hasEnd = true
	}
	if hasStart && hasEnd {
		golog.Warn("[generate worflow code failed] [禁止修改唯一标识注释，此时不会提取任何信息更新到新文件。] [comment: %v]\n", text)
		return
	}
	if hasStart {
		commentType = 1
	}
	if hasEnd {
		commentType = 2
	}
	if commentType == 0 {
		golog.Warn("[generate worflow code failed] [禁止修改唯一标识注释，此时不会提取任何信息更新到新文件。注释内容为 %s]\n", text)
		return
	}

	// 获取行号
	lineNumber := fileSet.Position(comment.Pos()).Line

	// 获取所在函数名
	funcKey := ""
	for _, decl := range astFile.Decls {
		if funcDecl, ok := decl.(*ast.FuncDecl); ok {
			if comment.Pos() > funcDecl.Pos() && comment.End() < funcDecl.End() {
				funcKey = genFuncKey(funcDecl)
				break
			}
		}
	}

	info := &WorkflowCommentInfo{
		WfCommentType: commentType,
		WfCommentID:   commentKey,
		LineNumber:    lineNumber,
		FuncKey:       funcKey,
	}

	// 存储本地 ast 注释信息
	commentMap, ok := w.WfCommentInfos[commentKey]
	if !ok {
		commentMap = make(map[*WorkflowCommentInfo]struct{})
	}
	commentMap[info] = struct{}{}

	w.WfCommentInfos[commentKey] = commentMap

	return
}

func genFuncKey(funcDecl *ast.FuncDecl) string {
	recv := funcDecl.Recv //
	if recv == nil {
		// 函数的构成
		return fmt.Sprintf("%s:0", funcDecl.Name.Name)
	}

	key := funcDecl.Name.Name
	list := recv.List
	for _, item := range list {
		names := item.Names
		if len(names) == 0 {
			continue
		}
		itemKey := ""
		for _, name := range names {
			itemKey += name.Name
		}
		key += fmt.Sprintf(":%s", itemKey)
	}
	return fmt.Sprintf("%s:1", key)
}

func (w *LineNumMetrics) getLocalCommentContents() {
	// 每个 commentKey 下只有两个注释，一个 start 一个 end
	for commentKey, commentMap := range w.WfCommentInfos {
		if err := w.getLocalCommentContent(commentKey, commentMap); err != nil {
			golog.Warn("[generate worflow code failed] [file name: %s] [key: %s] [err: %v]\n", w.fileName, commentKey, err)
		}
	}
}

func (w *LineNumMetrics) getLocalCommentContent(commentKey string, commentMap map[*WorkflowCommentInfo]struct{}) error {
	commentMapLen := len(commentMap)
	if commentMapLen != 2 {
		return fmt.Errorf("本地 comment key 为 %s 的 workflow 注释数量不对, 数量为：%d, 请检查", commentKey, commentMapLen)
		//return fmt.Errorf("本地 workflow 注释 %s 数量不对, 数量为: %d", commentKey, commentMapLen)
	}

	// 校验是否重复
	isStart := false
	isEnd := false

	var startInfo *WorkflowCommentInfo
	var endInfo *WorkflowCommentInfo
	for info := range commentMap {
		info := info
		if info.WfCommentType == 1 {
			if isStart {
				return fmt.Errorf("本地 workflow 注释 %s 重复 start\n", commentKey)
			}
			isStart = true
			startInfo = info
		}

		if info.WfCommentType == 2 {
			if isEnd {
				return fmt.Errorf("本地 workflow 注释 %s 重复 end\n", commentKey)
			}
			isEnd = true
			endInfo = info
		}
	}

	if !isStart || !isEnd {
		return fmt.Errorf("本地 workflow 注释 %s 没有 start 或 end", commentKey)
	}
	if startInfo.LineNumber >= endInfo.LineNumber {
		return fmt.Errorf("本地 workflow 注释 %s start 位置大于 end", commentKey)
	}

	/*
		fileContentByte, err := os.ReadFile(w.fileName)
		if err != nil {
			return fmt.Errorf("读取本地文件 %s 失败, err: %v", w.fileName, err)
		}
	*/
	//fileContent := string(fileContentByte)
	//fileContentSlice := strings.Split(fileContent, "\n")

	// 只提取注释内容，不提取注释, 注释用 workflow 生成的注释
	//if (startLine + 1) <= endLine {
	//commentContentSlice := fileContentSlice[startLine : endLine-1]
	//	commentContent := strings.Join(commentContentSlice, "\n")

	w.WfCommentContents[commentKey] = NewWorkflowCommentContent(startInfo, endInfo)

	return nil
}

func (w *LineNumMetrics) isWorkflowComment(comment *ast.Comment) bool {
	return strings.Contains(comment.Text, "custom") && (strings.Contains(comment.Text, "begin") || strings.Contains(comment.Text, "end"))
}

// 全托管中的注释的内容，是 rd 写的
func (f *LineNumMetrics) ComputeWfComment() (int, int) {
	// 在 baseFile
	if f.CodeMode != CodeModeFull {
		// 只有全托管中才需要获取 wf 中的内容
		return 0, 0
	}

	f.genWfCommentInfos()
	f.getLocalCommentContents()

	// 注释中的内容

	totalLine := 0
	increLine := 0

	for _, info := range f.WfCommentContents {
		total, incre := f.getLineNum(info.StartLine, info.EndLine, true)
		totalLine += total
		increLine += incre
	}

	return totalLine, increLine
}

func (f *LineNumMetrics) ComputeRPCComment() (int, int) {
	f.genRPCPluginCommentInfos()
	var commentInfos []*RPCPluginCommentInfo
	for _, info := range f.RPCPluginCommentInfos {
		commentInfos = append(commentInfos, info)
	}
	sort.Slice(commentInfos, func(i, j int) bool {
		return commentInfos[i].LineNumber < commentInfos[j].LineNumber
	})

	for i := 0; i < len(commentInfos); i++ {
		if commentInfos[i].RPCPluginCommentType != RPCCommentTypeStart {
			continue
		}

		startInfo := commentInfos[i]
		if i+1 >= len(commentInfos) {
			continue
		}
		endInfo := commentInfos[i+1]
		if endInfo.RPCPluginCommentType != RPCCommentTypeEnd {
			continue
		}

		if startInfo.FuncKey != endInfo.FuncKey {
			// rpc 注释中的内容要在一个函数
			golog.Warn("rpc 注释中的内容要在一个函数, fileName: %s", f.fileName)
			continue
		}
		comment := NewRPCPluginCommentContent(startInfo, endInfo)
		f.RPCPluginCommentContentMap[comment] = struct{}{}
	}

	totalLine := 0
	increLine := 0
	for comment := range f.RPCPluginCommentContentMap {
		total, incre := f.getLineNum(comment.StartLine, comment.EndLine, false)
		totalLine += total
		increLine += incre
	}

	return totalLine, increLine
}

func (w *LineNumMetrics) getWfCommentKey(comment string) (string, bool) {
	// comment_id: 10
	beginKey := "begin-"
	endKey := "end-"

	beginIndex := strings.LastIndex(comment, beginKey)
	endIndex := strings.LastIndex(comment, endKey)
	if beginIndex == -1 && endIndex == -1 {
		return "", false
	}

	if beginIndex > 0 && endIndex > 0 {
		return "", false
	}

	tmp := ""
	if beginIndex > 0 {
		tmp = comment[beginIndex+len(beginKey):]
	}
	if endIndex > 0 {
		tmp = comment[endIndex+len(endKey):]
	}
	if tmp == "" {
		return "", false
	}

	tmpSlice := strings.Split(tmp, " ")
	if len(tmpSlice) == 0 {
		return "", false
	}
	res := strings.TrimSpace(tmpSlice[0])

	return res, true
}
func (f *LineNumMetrics) genRPCPluginCommentInfos() {
	astFile := f.baseFile
	for _, commentGroup := range astFile.Comments {
		for _, comment := range commentGroup.List {
			typ, ok := f.isRPCPluginComment(comment)
			if !ok {
				continue
			}

			// 获取所在函数名
			funcKey := ""
			for _, decl := range astFile.Decls {
				if funcDecl, ok := decl.(*ast.FuncDecl); ok {
					if comment.Pos() > funcDecl.Pos() && comment.End() < funcDecl.End() {
						funcKey = genFuncKey(funcDecl)
						break
					}
				}
			}

			line := f.baseFileSet.Position(comment.Pos()).Line
			commentInfo := &RPCPluginCommentInfo{
				LineNumber:           line,
				RPCPluginCommentType: typ,
				FuncKey:              funcKey,
			}

			if _, ok := f.RPCPluginCommentInfos[line]; ok {
				msg := fmt.Sprintf("module: %s, fileName: %s line %d has already exist", "", f.fileName, line)
				utils.SendWarnMessageToHi(msg)
				continue
			}
			f.RPCPluginCommentInfos[line] = commentInfo
		}
	}
}

func (f *LineNumMetrics) isRPCPluginComment(comment *ast.Comment) (RPCCommentType, bool) {
	text := comment.Text
	if !strings.Contains(text, "Comate-Easy") {
		return "", false
	}

	if strings.Contains(text, "Start") {
		return RPCCommentTypeStart, true
	}

	if strings.Contains(text, "End") {
		return RPCCommentTypeEnd, true
	}

	return "", false
}

func (f *LineNumMetrics) getLineNum(start int, end int, isWorkflow bool) (int, int) {

	realStart := start
	realEnd := end
	if isWorkflow {
		realStart = realStart + 1
		realEnd = realEnd - 1
	}

	totalLine := realEnd - realStart + 1
	increLine := 0

	increLineMap, isAllIncre, hasIncre := f.Runner.Differ.Checker.GetDiffLine(f.fileName)
	if !hasIncre {
		return totalLine, increLine
	}

	if isAllIncre {
		increLine = totalLine
		return totalLine, increLine
	}

	for i := realStart; i <= realEnd; i++ {
		_, ok := increLineMap[i]
		if ok {
			increLine++
		}
	}

	return totalLine, increLine
}
