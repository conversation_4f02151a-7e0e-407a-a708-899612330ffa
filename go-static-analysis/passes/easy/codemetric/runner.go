package codemetric

import (
	"encoding/json"
	"errors"
	"fmt"
	"go/format"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/bitly/go-simplejson"
	"github.com/cloudflare/ahocorasick"
	"github.com/sergi/go-diff/diffmatchpatch"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/easyutil"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

type CodeMode int

const (
	RetryCnt                  = 3
	ConnectTimeout            = 1000
	ReadWriteTimeout          = 3000
	EasyCodeRuleAddrForOnline = "10.11.97.54:80"
	EsayCodeRuleAddrForTest   = "10.27.35.83:8057"
)

const (
	CodeModeFull CodeMode = iota // 完全由easy托管
	CodeModeHalf                 // 由easy半托管（和用户代码有交集）
	CodeModeZero                 // 由easy零托管（全为用户代码，和easy代码无交集）
)

var HalfFileNameMap = make(map[string]struct{})

type Runner struct {
	ProjectName       string
	StatServerAddr    string
	AddCodeMetricURL  string
	CheckFileExistURL string
	AddFileExistURL   string

	CommitUser   string
	CommitID     string
	CommitMsg    string
	BaseCommitID string

	// 最终结果，按目录生成
	fileEasyCodeResult      map[string]int
	fileUserCodeResult      map[string]int
	increFileEasyCodeResult map[string]int
	increFileUserCodeResult map[string]int

	fileEasyUTCodeResult      map[string]int
	fileUserUTCodeResult      map[string]int
	increFileEasyUTCodeResult map[string]int
	increFileUserUTCodeResult map[string]int

	dirEasyCodeResult      map[string]int
	dirUserCodeResult      map[string]int
	increDirEasyCodeResult map[string]int
	increDirUserCodeResult map[string]int

	AllEasyCodeCnt      int
	AllUserCodeCnt      int
	increAllEasyCodeCnt int
	increAllUserCodeCnt int

	AllEasyUTCodeCnt      int
	AllUserUTCodeCnt      int
	increAllEasyUTCodeCnt int
	increAllUserUTCodeCnt int

	allDir map[string]struct{}

	FilesInfos map[string]*FileInfo

	// 远程代码
	RemoteEasyCode     map[string]easyutil.EasyCode
	WFIdeCodeInfoMap   easyutil.IdeWFCodeInfoMap
	RemoteWFEasyCode   []easyutil.EasyCode
	WfFileToAPIPathMap map[string]string // fileName 映射所属api
	WfAPICodeMap       map[string]WfAPICode

	IcafeMap map[string]string `json:"icafe_map"`

	Differ *diff.Diff

	CurMetrics *LineNumMetrics
}

type RespBodyCodeMetric struct {
	RepoName string `json:"repo_name"`
	CreateAt int64  `json:"create_at"`

	BaseCommitID string `json:"base_commit_id"`
	CommitID     string `json:"commit_id"`
	CommitUser   string `json:"commit_user"`

	EasyTotalCnt      int32 `json:"easy_total_cnt"`
	IncreEasyTotalCnt int32 `json:"incre_easy_total_cnt"`

	UserTotalCnt      int32 `json:"user_total_cnt"`
	IncreUserTotalCnt int32 `json:"incre_user_total_cnt"`

	EasyUtTotalCnt      int32 `json:"easy_ut_total_cnt"`
	UserUtTotalCnt      int32 `json:"user_ut_total_cnt"`
	IncreEasyUtTotalCnt int32 `json:"incre_easy_ut_total_cnt"`
	IncreUserUtTotalCnt int32 `json:"incre_user_ut_total_cnt"`

	FileData  []*FileData `json:"file_data"`
	CommitURL string      `json:"commit_url"`
}

type FileExistAddBody struct {
	RepoName  string   `json:"repo_name"`
	FileNames []string `json:"file_name"`
}

type DirData struct {
	DirName string `json:"dir_name"`

	UserCnt      int32 `json:"user_cnt"`
	IncreUserCnt int32 `json:"incre_user_cnt"`

	EasyCnt      int32 `json:"easy_cnt"`
	IncreEasyCnt int32 `json:"incre_easy_cnt"`
}

type FileData struct {
	FileName string `json:"file_name"`
}

// diff新增代码
type AddCode struct {
	Code string
}

func NewRunner(lastCommitID string) *Runner {
	r := &Runner{
		RemoteEasyCode:          make(map[string]easyutil.EasyCode),
		allDir:                  make(map[string]struct{}),
		increDirEasyCodeResult:  make(map[string]int),
		increDirUserCodeResult:  make(map[string]int),
		dirEasyCodeResult:       make(map[string]int),
		dirUserCodeResult:       make(map[string]int),
		increFileEasyCodeResult: make(map[string]int),
		increFileUserCodeResult: make(map[string]int),
		fileEasyCodeResult:      make(map[string]int),
		fileUserCodeResult:      make(map[string]int),
		FilesInfos:              make(map[string]*FileInfo),

		fileEasyUTCodeResult:      make(map[string]int),
		fileUserUTCodeResult:      make(map[string]int),
		increFileEasyUTCodeResult: make(map[string]int),
		increFileUserUTCodeResult: make(map[string]int),
		WfFileToAPIPathMap:        make(map[string]string),
		WfAPICodeMap:              make(map[string]WfAPICode),

		IcafeMap: map[string]string{
			"netdisk-commercialization-": "商业化",
			"netdisk-tera-":              "Terabox",
			"netdisk-tob-":               "toB生态(企业、开放平台)",
			"netdisk-basic-":             "基础体验",
			"netdisk-DI-":                "数据智能研发团队",
			"netdisk-operation-":         "运营(电商)",
			"netdisk-yike-":              "一刻",
			"netdisk-infrastructure-":    "工程效率/质量",
			"netdisk-secure-":            "数据安全",
			"netdisk-risk-":              "数据安全",
			"netdisk-onlines-":           "在线化",
			"wangpanshujuxuqiu-":         "用商策略",
			"netdisk-innovate":           "创新业务",
		},

		Differ: diff.NewDiff(lastCommitID, false),
	}
	return r
}

type FileInfo struct {
	LineCnt     int
	Mode        CodeMode
	IsWfAPIFile bool
	WfAPIPath   string

	RelFileName string
	RelFilePath string
}

func (r *Runner) initAll() error {
	if err := r.initProjectName(); err != nil {
		golog.Error("[msg: init project name failed][err: %s]", err.Error())
		return err
	}

	if err := r.initRemoteEasyCode(); err != nil {
		golog.Error("[msg: init remote easy code failed][err: %s]", err.Error())
		return err
	}

	if err := r.initCommitIDsAndCommitUser(); err != nil {
		golog.Error("[msg: init commit id or commit user failed][err: %s]", err.Error())
		return err
	}
	if err := r.initWFIdeCode(); err != nil {
		golog.Error("[msg: init wf ide code failed][err: %s]", err.Error())
		return err
	}

	// v1 wf 代码生成，先保留
	r.initWfAPICodes()

	if err := r.initFileInfos(); err != nil {
		golog.Error("[msg: init fileInfos failed.] err: %s", err.Error())
		return err
	}

	if err := r.initStatServerAddrAndURL(); err != nil {
		golog.Error("[msg: init statServer addr failed][err: %s]", err.Error())
		return err
	}

	if err := r.initDiffer(); err != nil {
		golog.Error("[msg: init differ failed][err: %s]", err.Error())
		return err
	}

	return nil
}

func (r *Runner) initDiffer() error {
	if r.Differ == nil {
		r.Differ = diff.NewDiff(r.BaseCommitID, false)
	}

	if err := r.Differ.Checker.Prepare(); err != nil {
		golog.Error("[msg: init differ failed][err: %s]", err.Error())
		return err
	}

	return nil
}

func (r *Runner) initProjectName() error {
	projectName, err := easyutil.GetProjectName()
	if err != nil {
		return err
	}
	r.ProjectName = projectName
	return nil
}

func (r *Runner) initRemoteEasyCode() error {
	res, wfCodes, err := easyutil.GetRemoteMsg(r.ProjectName)
	if err != nil {
		golog.Warn("[msg: get remote msg failed][project name: %s]", r.ProjectName)
		return err
	}
	//  通过原始接口获取到的
	r.RemoteEasyCode = res
	r.RemoteWFEasyCode = wfCodes

	remoteEasyCodeStr, err := json.Marshal(r.RemoteWFEasyCode)
	if err != nil {
		golog.Error("[msg: marshal remote easy code failed][err: %s]", err.Error())
		return err
	}

	fileName := filepath.Base(r.ProjectName) + ".json"
	if err := os.WriteFile(fileName, []byte(remoteEasyCodeStr), 0755); err != nil {
		return err
	}

	return nil
}

func (r *Runner) initWFIdeCode() error {
	remoteWFIdeCodeMap, err := easyutil.GetRemoteWFIdeCodeMap(r.ProjectName, r.CommitUser)
	if err != nil {
		return err
	}
	r.WFIdeCodeInfoMap = remoteWFIdeCodeMap
	return nil
}

func (r *Runner) initStatServerAddrAndURL() error {
	var addr string
	if DebugMode {
		addr = EsayCodeRuleAddrForTest // 沙盒中的easy-coderule-server地址，避免弄脏线上数据库
	} else {
		addr = EasyCodeRuleAddrForOnline
	}

	r.StatServerAddr = addr
	r.AddCodeMetricURL = fmt.Sprintf("http://%s/codemetric?method=add", addr)
	r.CheckFileExistURL = fmt.Sprintf("http://%s/codemetric?method=fileexist", addr)
	r.AddFileExistURL = fmt.Sprintf("http://%s/codemetric?method=addhalffile", addr)

	return nil
}

func (r *Runner) initFileInfos() error {
	wd, err := os.Getwd()
	if err != nil {
		return err
	}

	err = filepath.Walk(wd, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 如果path是带有ignored后缀把该后缀去除
		if !info.IsDir() && strings.HasSuffix(path, ".ignored") {
			newName := strings.TrimSuffix(path, ".ignored")
			if err := os.Rename(path, newName); err != nil {
				golog.Error("rename failed, error: %s", err.Error())
				return nil
			}
			path = newName
		}

		// 遍历当前目录下所有的结果
		if !info.IsDir() && filepath.Ext(path) == ".go" {
			fileContent, err := os.ReadFile(path)
			if err != nil {
				golog.Error("[err: filepath.Walk failed][path: %s][err: %s]", path, err.Error())
				// 递归中出现的错误会导致退出递归, 这里不应该抛出错误, 下同
				return nil
			}

			lineCnt := getLineCnt(string(fileContent))

			relFileName, err := filepath.Rel(wd, path)
			if err != nil {
				golog.Error("[err: filepath.Walk failed][path: %s][err: %s]", path, err.Error())
				// 递归中出现的错误会导致退出递归, 这里不应该抛出错误
				return nil
			}

			// set file info
			fileInfo := &FileInfo{}
			code, ok := r.RemoteEasyCode[relFileName]
			if !ok {
				fileInfo.Mode = CodeModeZero // 在接口下发里面找不到，此时为easy不托管
			} else {
				if !code.Rewrite {
					fileInfo.Mode = CodeModeHalf // easy半托管
				} else {
					fileInfo.Mode = CodeModeFull // easy全托管
				}

			}

			relFilePath := filepath.Dir(relFileName)
			_, ok = r.WfAPICodeMap[relFilePath]
			if ok {
				fileInfo.IsWfAPIFile = true // 目录下有easy代码，则认为该目录为easy托管
				fileInfo.WfAPIPath = relFilePath
			} else {
				fileInfo.IsWfAPIFile = false // 目录下有easy代码，则认为该目录为easy托管
				fileInfo.WfAPIPath = ""
			}
			fileInfo.RelFileName = relFileName
			fileInfo.RelFilePath = relFilePath

			fileInfo.LineCnt = lineCnt
			r.FilesInfos[relFileName] = fileInfo
		}

		return nil
	})

	// 获取在easy生成的，但是在rd提交的代码中已经删掉了的代码
	for fileName := range r.RemoteEasyCode {
		_, ok := r.FilesInfos[fileName]
		if ok {
			continue
		}
		golog.Error("the file is in remote, bug not in local. file name: %s", fileName)

		// 不处理这种情形
		/*
			tmpFileInfo := &FileInfo{}
			tmpFileInfo.LineCnt = 0
			if ec.Rewrite {
				tmpFileInfo.Mode = CodeModeFull
			} else {
				tmpFileInfo.Mode = CodeModeHalf
			}

			r.FilesInfos[fileName] = tmpFileInfo
		*/
	}

	return nil
}

func (r *Runner) initCommitIDsAndCommitUser() error {
	// get last long commit id for git command
	getCommitIDCmd := `git rev-parse HEAD`
	commitID, err := easyutil.ExecScript(getCommitIDCmd)
	if err != nil {
		return fmt.Errorf("get commit id failed, err is %w, output is  %s", err, commitID)
	}

	getCommitUserCmd := `git log -1 --format="%an"`
	commitUser, err := easyutil.ExecScript(getCommitUserCmd)
	if err != nil {
		return fmt.Errorf("get last commit user failed, err is %w, output is %s", err, commitUser)
	}

	getCommitMsgCmd := `git log -1 --pretty=format:'%s'`
	commitMsg, err := easyutil.ExecScript(getCommitMsgCmd)
	if err != nil {
		return fmt.Errorf("get last commit message failed, err is %w, output is %s", err, commitMsg)
	}

	r.BaseCommitID = lastCommitID
	r.CommitID = commitID
	r.CommitUser = commitUser
	r.CommitMsg = commitMsg
	return nil
}

func (r *Runner) setEasyCodeResult() error {
	for fileName, ec := range r.RemoteEasyCode { // 由 go文件以及go.mod组成
		var remoteCode string
		if strings.HasSuffix(fileName, ".go") {
			remoteTmpCode, err := format.Source([]byte(ec.Code)) // 对远程代码
			if err != nil {
				golog.Error("[msg: format.Source failed][filename: %s][err: %s]", fileName, err.Error())
				continue
			}
			remoteCode = string(remoteTmpCode)
		} else {
			remoteCode = ec.Code
		}
		lineCnt := getLineCnt(string(remoteCode))
		r.fileEasyCodeResult[fileName] = lineCnt
	}
	return nil
}

// 计算easy生成行数与easy生成行数
func (r *Runner) computeCodeLineResult(lastCommitID string) {
	for fileName, fileInfo := range r.FilesInfos {
		if fileInfo == nil {
			continue
		}
		fileSet, astFile, err := ParseFile(fileName)
		if err != nil {
			golog.Error("parse file failed, err is %s", err)
			continue
		}

		metrics := NewLineNumMetrics(r, fileInfo.Mode, fileName, fileInfo, fileSet, astFile)
		metrics.Compute()

		r.CurMetrics = metrics

		if DebugMode {
			fmt.Printf("[file name: %s] [wf total: %d] [wf incre: %d] [rpc total: %d] [rpc incre: %d]\n",
				fileName, metrics.WfTotalLine, metrics.WfIncreLine, metrics.RPCPluginTotalLine, metrics.RPCPluginIncreLine)
		}

		switch fileInfo.Mode {
		case CodeModeFull:
			r.computeLineCntOnCodeModeFull(fileName)                       // easy全托管全量 // wf v1代码在这里做统计(之前的wf代码)
			r.computeLineCntOnCodeModeFullIncrease(lastCommitID, fileName) // easy全托管增量 // wf v1代码在这里做统计（之前的wf代码）
		case CodeModeHalf:
			wfCodeMetas := r.computeLinetCntOnCodeModeHalf(fileName)                    // easy半托管全量  // rpc, wf v2(编辑器)代码在这里做统计
			r.computeLineCntOnCodeModeHalfIncrease(lastCommitID, fileName, wfCodeMetas) // easy半托管增量  // rpc, wf v2(编辑器)代码在这里做统计,
		case CodeModeZero:
			wfCodeMetas := r.computeLineCntOnCodeModeZero(fileName)                     // easy不托管全量  // rpc, wf v2(编辑器)代码在这里做统计
			r.computeLineCntOnCodeModeZeroIncrease(lastCommitID, fileName, wfCodeMetas) // easy不托管增量  // rpc, wf v2(编辑器)代码在这里做统计
		default:
			// do nothing
		}
	}
}

func (r *Runner) computeLineCntOnCodeModeFull(fileName string) {
	// 全托管: 用户行数
	easyCode, ok := r.RemoteEasyCode[fileName]
	if !ok {
		return
	}

	easyCnt := 0
	res, err := format.Source([]byte(easyCode.Code))
	if err != nil {
		easyCnt = getLineCnt(string(easyCode.Code))
	} else {
		easyCnt = getLineCnt(string(res))
	}

	wfTotalLine := 0
	if r.CurMetrics != nil && r.CurMetrics.fileName == fileName {
		wfTotalLine = r.CurMetrics.WfTotalLine
	} else {
		golog.Warn("[wf stat] fileName not match, file name: %s", fileName)
	}

	// 全托管下只统计 wf 即可
	if isTestFile(fileName) {
		r.fileUserUTCodeResult[fileName] = wfTotalLine
		r.fileEasyUTCodeResult[fileName] = easyCnt
	}

	r.fileUserCodeResult[fileName] = wfTotalLine
	r.fileEasyCodeResult[fileName] = easyCnt

	// WF占比
	relFilePath := r.CurMetrics.fileInfo.RelFilePath
	apiCode, ok := r.WfAPICodeMap[relFilePath]
	if ok {
		if strings.HasSuffix(fileName, "_ext.go") { // 半托管
			apiCode.ExtCodeEasyLineCnt = easyCnt
			apiCode.ExtCodeUserLineCnt = wfTotalLine
		} else if strings.HasSuffix(fileName, "_valid.go") { // 全托管
			apiCode.ValidCodeEasyLineCnt = easyCnt
			apiCode.ValidCodeUserLineCnt = wfTotalLine
		} else { // 全托管
			apiCode.CoreCodeEasyLineCnt = easyCnt
			apiCode.CoreCodeUserLineCnt = wfTotalLine
		}
		r.WfAPICodeMap[relFilePath] = apiCode
	}
}

// 全托管下的增量行信息
func (r *Runner) computeLineCntOnCodeModeFullIncrease(lastCommitID string, fileName string) {
	var command string
	if nowCommitIDGlobal == "" {
		command = fmt.Sprintf("git diff %s -- %s", lastCommitID, fileName)
	} else {
		command = fmt.Sprintf("git diff %s %s -- %s", lastCommitID, nowCommitIDGlobal, fileName)
	}

	output, err := easyutil.ExecScript(command)
	if err != nil {
		golog.Error("command exec failed. command is %s, err is %s", command, err.Error())
		return
	}

	if strings.TrimSpace(output) == "" {
		// 如果没有diff信息直接返回避免多写数据到mysql中
		return
	}

	wfIncreLine := 0
	if r.CurMetrics != nil && r.CurMetrics.fileName == fileName {
		wfIncreLine = r.CurMetrics.WfIncreLine
	}

	// 全托管下的增量行信息都算是easy的
	addCnt, _ := processDiffContent(output)
	increaseCnt := addCnt
	r.increFileEasyCodeResult[fileName] = increaseCnt - wfIncreLine
	r.increFileUserCodeResult[fileName] = wfIncreLine

	if isTestFile(fileName) {
		r.increFileUserUTCodeResult[fileName] = wfIncreLine
		r.increFileEasyUTCodeResult[fileName] = increaseCnt - wfIncreLine
	}

	// 占比
	relFilePath := r.CurMetrics.fileInfo.RelFilePath
	apiCode, ok := r.WfAPICodeMap[relFilePath]
	if ok && (addCnt != 0) {
		apiCode.IsDiffAPI = true
		r.WfAPICodeMap[relFilePath] = apiCode
	}

	if DebugMode {
		newFileName := fileName + "-full-incre"
		fd, err := os.OpenFile(newFileName, os.O_TRUNC|os.O_CREATE|os.O_RDWR, 0666)
		if err != nil {
			golog.Error("open file failed, filename is %s", newFileName)
			return
		}
		defer fd.Close()
		fmt.Fprintf(fd, output)
	}
}

/*
// 半托管文件中增量
func (r *Runner) computeLineCntOnCodeModeHalfIncreaseV2(lastCommitID string, fileName string) {
	command := fmt.Sprintf("git diff %s -- %s", lastCommitID, fileName)
	output, err := easyutil.ExecScript(command)
	if err != nil {
		golog.Error("command exec failed. command is %s, err is %s", command, err.Error())
		return
	}

	if strings.TrimSpace(output) == "" {
		// 如果没有diff信息直接返回避免多写数据到mysql中
		golog.Error("no diff info, fileName is %s", fileName)
		return
	}

	increContents := parseDiffContent(output)
	easyCode, ok := r.RemoteEasyCode[fileName]
	if !ok {
		golog.Error("get remote easyCode failed, file name is %s", fileName)
		return
	}

	easyCodeByte, err := format.Source([]byte(easyCode.Code))
	if err != nil {
		golog.Error("go format easycode code failed, err is %s", err.Error())
		return
	}

	easyIncreContent := getEasyIncreCntV2(string(easyCodeByte), increContents)
	r.increFileEasyCodeResult[fileName] = easyIncreContent
	userIncreCnt := len(increContents) - easyIncreContent
	if userIncreCnt < 0 {
		// 由于 空行和 diff 的原因，这个值可能为 -1， 这里重置为 0
		userIncreCnt = 0
	}
	userIncreContent := userIncreCnt

	if userIncreContent < 0 {
		golog.Error(" userIncreContent < 0, file name is %s", fileName)
		return
	}
	r.increFileUserCodeResult[fileName] = userIncreContent
	r.increFileEasyCodeResult[fileName] = easyIncreContent

	if isTestFile(fileName) {
		r.increFileUserUTCodeResult[fileName] = userIncreContent
		r.increFileEasyUTCodeResult[fileName] = easyIncreContent
	}

	return
}
*/

func getEasyIncreCnt(remoteFile string, diffContent []string) int {
	ac := ahocorasick.NewStringMatcher(diffContent)

	//matchs := ac.Match([]byte(remoteFile))
	matchs := ac.Match([]byte(remoteFile))

	return len(matchs)
}

func getEasyIncreCntV2(remoteFile string, diffContent []string) int {
	var increStr string
	for _, item := range diffContent {
		increStr += item + "\n"
	}
	increStr = strings.TrimSuffix(increStr, "\n")

	needReturn := true
	script := `diff -u <(echo "$1") <(echo "$2")`
	newDiffContent, err := utils.ExecScript(script, remoteFile, increStr)
	if err != nil {
		if e, ok := err.(*exec.ExitError); ok {
			// diff 命令当有diff时退出码为1
			switch e.ExitCode() {
			case 0:
				// 当文件不存在 diff 时，退出码为0，函数头已进行了处理， 这里预防性再处理下，理论上走不到
				return getLineCnt(remoteFile)
			case 1:
				// 当文件存在diff时，退出码为1
				needReturn = false
			default:
				errMsg := fmt.Sprintf("codemetric exit code: %d", e.ExitCode())
				utils.SendWarnMessageToHiForDailyMessage(errMsg)
			}
		}
	}

	codeLineCnt := getLineCnt(increStr)
	if needReturn {
		return codeLineCnt // getLineCnt(remoteFile)
	}

	addCnt, _ := processDiffContent(newDiffContent)
	return codeLineCnt - addCnt
}

func parseDiffContent(diffOutput string) []string {
	originalLines := []string{}
	lines := strings.Split(diffOutput, "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "+") && !strings.HasPrefix(line, "+++") {
			originalLine := line[1:]
			originalLines = append(originalLines, originalLine)
		}
	}
	return originalLines
}

// 半托管模式下的增量行信息
func (r *Runner) computeLineCntOnCodeModeHalfIncrease(lastCommitID string, fileName string, wfCodeMetas []*WFCodeMeta) {
	var command string
	if nowCommitIDGlobal == "" {
		command = fmt.Sprintf("git diff %s -- %s", lastCommitID, fileName)
	} else {
		command = fmt.Sprintf("git diff %s %s -- %s", lastCommitID, nowCommitIDGlobal, fileName)
	}
	output, err := easyutil.ExecScript(command)
	if err != nil {
		golog.Error("command exec failed. command is %s, err is %s", command, err.Error())
		return
	}

	if strings.TrimSpace(output) == "" {
		// 如果没有diff信息直接返回避免多写数据到mysql中
		return
	}

	rpcIncreLine := 0
	if r.CurMetrics != nil && r.CurMetrics.fileName == fileName {
		rpcIncreLine = r.CurMetrics.RPCPluginIncreLine
	}

	// git diff 算

	// WF增量代码度量
	addCodes := getDiffContent(output) // 新增的代码行，需要判断里面有多少个 wf 代码生成的
	easyCnt, userCnt := r.computeWFIncreRatioCode(wfCodeMetas, addCodes)

	if r.checkFileIsExistInRemote(fileName) {
		// 文件已存在，此时增量代码算rd的
		r.increFileUserCodeResult[fileName] = int(userCnt) - rpcIncreLine
		r.increFileEasyCodeResult[fileName] = rpcIncreLine + int(easyCnt)
	} else {
		//文件尚未创建，增量信息按照该文件全量的信息来算
		HalfFileNameMap[fileName] = struct{}{}
		r.increFileUserCodeResult[fileName] = r.fileUserCodeResult[fileName]
		r.increFileEasyCodeResult[fileName] = r.fileEasyCodeResult[fileName]
		//

	}

	if DebugMode {
		newFileName := fileName + "-half-incre"
		fd, err := os.OpenFile(newFileName, os.O_TRUNC|os.O_CREATE|os.O_RDWR, 0666)
		if err != nil {
			golog.Error("open file failed, filename is %s", newFileName)
			return
		}
		defer fd.Close()
		fmt.Fprintf(fd, output)
	}
}

type CheckFileExistReqBody struct {
	RepoName string `json:"repo_name"`
	FileName string `json:"file_name"`
}

type CheckFileExistResBody struct {
	RepoName string `json:"repo_name"`
	FileName string `json:"file_name"`
	Exist    bool   `json:"exist"`
}

func (r *Runner) checkFileIsExistInRemote(fileName string) bool {
	// 当该函数出错时，默认该文件是rd写的
	body := CheckFileExistReqBody{
		RepoName: r.ProjectName,
		FileName: fileName,
	}

	bodyJSON, err := json.Marshal(body)
	if err != nil {
		golog.Error("json marshal failed, %v", err)
		msg := fmt.Sprintf("checkFileIsExistInRemote failed, json marshal failed, repoName is %s, fileName is %s", body.RepoName, body.RepoName)
		utils.SendWarnMessageToHiForDailyMessage(msg)
		return true
	}

	var resp httpclient.HttpResponse
	var postErr error
	for i := 0; i < RetryCnt; i++ {
		resp, postErr = httpclient.Post(r.CheckFileExistURL, map[string]string{}, ConnectTimeout, ReadWriteTimeout, string(bodyJSON))
		if postErr != nil {
			golog.Error("[msg: http post failed][i: %v][filename: %s][url: %s][err: %v]", i, r.CheckFileExistURL, fileName, postErr)
			continue
		} else {
			break
		}
	}

	if postErr != nil {
		msg := fmt.Sprintf("[msg: http post failed][filename: %s][url: %s][err: %v]", fileName, r.CheckFileExistURL, postErr)
		golog.Error(msg)
		utils.SendWarnMessageToHiForDailyMessage(msg)
		return true
	}
	if resp.StatusCode != http.StatusOK {
		msg := fmt.Sprintf("[msg: http post failed, status is not ok][filename: %s][url: %s][status: %d]", fileName, r.CheckFileExistURL, resp.StatusCode)
		golog.Error(msg)
		utils.SendWarnMessageToHiForDailyMessage(msg)
		return true
	}
	js, err := simplejson.NewJson(resp.Body)
	if err != nil {
		msg := fmt.Sprintf("[msg: checkFileIsExistInRemote failed, new json failed][err: %v]", err)
		golog.Error(msg)
		utils.SendWarnMessageToHiForDailyMessage(msg)
		return true
	}

	isExist, err := js.Get("data").Get("exist").Bool()
	if err != nil {
		msg := fmt.Sprintf("[msg: get the exist failed form url %s]", r.CheckFileExistURL)
		golog.Error(msg)
		utils.SendWarnMessageToHiForDailyMessage(msg)
		return true
	}

	return isExist
}

// 不托管下, 增量都算rd的
func (r *Runner) computeLineCntOnCodeModeZeroIncrease(lastCommitID string, fileName string, wfCodeMetas []*WFCodeMeta) {
	var command string
	if nowCommitIDGlobal == "" {
		command = fmt.Sprintf("git diff %s -- %s", lastCommitID, fileName)
	} else {
		command = fmt.Sprintf("git diff %s %s -- %s", lastCommitID, nowCommitIDGlobal, fileName)
	}

	output, err := easyutil.ExecScript(command)
	if err != nil {
		golog.Error("command exec failed. command is %s, err is %s", command, err.Error())
		return
	}

	if strings.TrimSpace(output) == "" {
		// 如果没有diff信息直接返回避免写不必要的数据到mysql中
		return
	}

	// WF增量代码度量
	addCodes := getDiffContent(output) // 新增的代码行，需要判断里面有多少个 wf 代码生成的
	easyCnt, userCnt := r.computeWFIncreRatioCode(wfCodeMetas, addCodes)

	// 强强侧插件 RPC 代码度量
	rpcIncreLine := 0
	if r.CurMetrics != nil && r.CurMetrics.fileName == fileName {
		rpcIncreLine = r.CurMetrics.RPCPluginIncreLine
	}

	if isTestFile(fileName) {
		r.increFileUserUTCodeResult[fileName] = int(userCnt) - rpcIncreLine
		r.increFileEasyUTCodeResult[fileName] = rpcIncreLine + int(easyCnt)
	}

	r.increFileUserCodeResult[fileName] = int(userCnt) - rpcIncreLine
	r.increFileEasyCodeResult[fileName] = rpcIncreLine + int(easyCnt)

	if DebugMode {
		newFileName := fileName + "-zero-incre"
		fd, err := os.OpenFile(newFileName, os.O_TRUNC|os.O_CREATE|os.O_RDWR, 0666)
		if err != nil {
			golog.Error("open file failed, filename is %s", newFileName)
			return
		}
		defer fd.Close()
		fmt.Fprintf(fd, output)
	}
}

func (r *Runner) computeWFIncreRatioCode(wfCodeMetas []*WFCodeMeta, addCodes []*AddCode) (easyCnt float64, userCnt float64) {
	var tmpAddCodes []string
	for _, item := range addCodes {
		tmpAddCodes = append(tmpAddCodes, item.Code)
	}
	addCodesStr := strings.Join(tmpAddCodes, "\n") // 新增的代码

	var tmpWfCodeMetas []string
	for _, item := range wfCodeMetas {
		tmpWfCodeMetas = append(tmpWfCodeMetas, item.Code)
	}
	wfCodeMetasStr := strings.Join(tmpWfCodeMetas, "\n") // wf代码

	dmp := diffmatchpatch.New()
	diffs := dmp.DiffMain(wfCodeMetasStr, addCodesStr, false)

	lastEqualLine := ""
	lastInsertLine := ""

	var lastInsertEqualStatus = 0
	for _, diff := range diffs {
		diffLines := strings.Split(diff.Text, "\n")
		switch diff.Type {
		case diffmatchpatch.DiffEqual:
			if len(diffLines) > 0 {
				easyCnt += float64(len(diffLines) - 1)
			}
			if lastInsertLine != "" {
				line := lastInsertLine + diffLines[0]
				userRatio := float64(len(lastInsertLine)) / float64(len(line))
				easyRatio := 1 - userRatio

				easyCnt += easyRatio
				userCnt += userRatio

				lastInsertLine = ""
			}
		case diffmatchpatch.DiffInsert:
			if len(diffLines) > 0 {
				userCnt += float64(len(diffLines) - 1) // 先加上基础行
			}

			if lastEqualLine != "" {
				// 此时表明变更的地方是一行的最后一处
				line := lastEqualLine + diffLines[0]
				easyRatio := float64(len(lastEqualLine)) / float64(len(line))
				userRatio := 1 - easyRatio
				fmt.Println("line: ", line, ", easyRatio: ", easyRatio, ", userRatio: ", userRatio)
				easyCnt += easyRatio
				userCnt += userRatio

				lastEqualLine = ""
			}

			lastInsertLine = diffLines[len(diffLines)-1]
			lastInsertEqualStatus = StatusInsert
		}
	}

	if lastInsertEqualStatus == StatusEqual {
		easyCnt += 1
	}
	if lastInsertEqualStatus == StatusInsert {
		userCnt += 1
	}
	return
}

func (r *Runner) computeLinetCntOnCodeModeHalf(fileName string) []*WFCodeMeta {
	localFileByte, err := os.ReadFile(fileName)
	if err != nil {
		errMsg := fmt.Sprintf("[msg: os.ReadFile failed][file name: %s][err: %s]", fileName, err.Error())
		golog.Error(errMsg)
		utils.SendWarnMessageToHiForDailyMessage(errMsg)
		return nil
	}

	ec, ok := r.RemoteEasyCode[fileName]
	if !ok {
		golog.Error("r.RemoteEasyCode[%s] is not ok", fileName)
		return nil
	}

	remoteFile := ec.Code
	remoteFileByte := []byte(remoteFile)

	localFileByte, err = format.Source(localFileByte)
	if err != nil {
		golog.Error(err.Error())
		return nil
	}

	remoteFileByte, err = format.Source(remoteFileByte)
	if err != nil {
		golog.Error(err.Error())
		return nil
	}

	// format以后，先把 localCode 的代码提取出来，然后和 remoteFile
	localFile := string(localFileByte)
	remoteFile = string(remoteFileByte)

	localCodeAfterDeleting, wfCodeMetaSlice := SplitLocalCode(localFile)
	r.processDiffContent(fileName, remoteFile, localCodeAfterDeleting)

	var fileNameCodeInfo []easyutil.EasyIdeWFCodeInfo
	// 存储的时候相对文件名不易获取，存的绝对文件名，这里遍历提取一下
	for absFileName, remoteWFCodeItem := range r.WFIdeCodeInfoMap {
		if !strings.HasSuffix(absFileName, fileName) {
			continue
		}
		fileNameCodeInfo = append(fileNameCodeInfo, remoteWFCodeItem)
	}

	// 在以上的计数上计算 wfcode 代码
	r.computeWFV2Code(fileName, fileNameCodeInfo, wfCodeMetaSlice)
	return wfCodeMetaSlice

}

func (r *Runner) computeWFV2Code(fileName string, fileNameCodeInfos []easyutil.EasyIdeWFCodeInfo, wfCodeMeta []*WFCodeMeta) {
	tmpMap := make(map[string]int)
	// local code
	sort.Slice(wfCodeMeta, func(i, j int) bool {
		return wfCodeMeta[i].Index < wfCodeMeta[j].Index
	})

	for _, item := range wfCodeMeta {
		tmpMap[item.ID] = item.Index
	}

	// remote code
	remoteWFIdeCodeMap := make(map[string]easyutil.EasyIdeWFCodeInfo)
	for _, item := range fileNameCodeInfos {
		remoteWFIdeCodeMap[item.CodeID] = item
	}

	var wfAllEasyCnt float64 = 0
	var wfAllUserCnt float64 = 0
	for _, localCodeMeta := range wfCodeMeta {
		wfCodeInfo, ok := remoteWFIdeCodeMap[localCodeMeta.ID]
		if !ok {
			golog.Error("remoteWFIdeCodeMap[%s] is not ok", localCodeMeta.ID)
			fmt.Printf("remoteWFIdeCodeMap[%s] is not ok\n", localCodeMeta.ID)
			// 前几天
			continue
		}

		easyCnt, userCnt := CompareWFDiff(wfCodeInfo.Code, localCodeMeta.Code)
		wfAllEasyCnt += easyCnt
		wfAllUserCnt += userCnt

		// r.fileEasyCodeResult[fileName] += easyCnt
		// r.fileUserCodeResult[fileName] += userCnt - rpcTotalLine
	}
	r.fileEasyCodeResult[fileName] += int(wfAllEasyCnt)
	r.fileUserCodeResult[fileName] += int(wfAllUserCnt)
	/*
		localWFCode := strings.Join(localWFCodeSlice, "\n\n")
		remoteWFCode := strings.Join(remoteWFCodeSlice, "\n\n")
	*/

}

func (r *Runner) processDiffContent(fileName, baseFileContent string, newFileContent string) {
	rpcTotalLine := 0
	if r.CurMetrics != nil && r.CurMetrics.fileName == fileName {
		rpcTotalLine = r.CurMetrics.RPCPluginTotalLine
	} else {
		golog.Warn("debug not match, fileName: %s", fileName)
	}

	if baseFileContent == newFileContent {
		easyCodeResultCnt := getLineCnt(baseFileContent)
		r.fileEasyCodeResult[fileName] = easyCodeResultCnt
		r.fileUserCodeResult[fileName] = 0

		if isTestFile(fileName) {
			r.fileEasyUTCodeResult[fileName] = easyCodeResultCnt
			r.fileUserUTCodeResult[fileName] = 0
		}

		return
	}

	needReturn := true
	script := `diff -u <(echo "$1") <(echo "$2")`
	diffContent, err := utils.ExecScript(script, baseFileContent, newFileContent)
	if err != nil {
		if e, ok := err.(*exec.ExitError); ok {
			// diff 命令当有diff时退出码为1
			switch e.ExitCode() {
			case 0:
				// 当文件不存在 diff 时，退出码为0，函数头已进行了处理， 这里预防性再处理下，理论上走不到
				easyLineCnt := getLineCnt(baseFileContent)
				r.fileEasyCodeResult[fileName] = easyLineCnt
				r.fileUserCodeResult[fileName] = 0

				if isTestFile(fileName) {
					r.fileEasyUTCodeResult[fileName] = easyLineCnt
					r.fileUserUTCodeResult[fileName] = 0
				}

			case 1:
				// 当文件存在diff时，退出码为1
				needReturn = false
			default:
				errMsg := fmt.Sprintf("codemetric exit code: %d", e.ExitCode())
				utils.SendWarnMessageToHiForDailyMessage(errMsg)
			}
		}
	}

	if needReturn {
		return
	}
	// 只有上述diff命令退出码为1时, 即存在diff时才能走到这里

	// 用户代码行数 = insert的代码行数
	// easy代码行数 = base代码行数 - （delete的代码行数）
	userInsertLineCnt, _ := processDiffContent(diffContent)
	//easyLineCnt := getLineCnt(baseFileContent) - easyDeleteLineCnt
	easyLineCnt := getLineCnt(newFileContent) - userInsertLineCnt
	r.fileEasyCodeResult[fileName] = easyLineCnt + rpcTotalLine
	r.fileUserCodeResult[fileName] = userInsertLineCnt - rpcTotalLine

	if isTestFile(fileName) {
		r.fileUserUTCodeResult[fileName] = userInsertLineCnt - rpcTotalLine
		r.fileEasyUTCodeResult[fileName] = easyLineCnt + rpcTotalLine
	}

	// WF占比
	relFilePath := r.CurMetrics.fileInfo.RelFilePath
	apiCode, ok := r.WfAPICodeMap[relFilePath]
	if ok {
		if strings.HasSuffix(fileName, "_ext.go") { // 半托管
			apiCode.ExtCodeEasyLineCnt = easyLineCnt + rpcTotalLine
			apiCode.ExtCodeUserLineCnt = userInsertLineCnt - rpcTotalLine
		}
	}

	if DebugMode {
		baseFileName := fileName + "-half-base"
		baseFile, err := os.OpenFile(baseFileName, os.O_TRUNC|os.O_CREATE|os.O_RDWR, 0666)
		if err != nil {
			return
		}
		defer baseFile.Close()
		fmt.Fprintf(baseFile, baseFileContent)

		newFileName := fileName + "-half-new"
		newFile, err := os.OpenFile(newFileName, os.O_TRUNC|os.O_CREATE|os.O_RDWR, 0666)
		if err != nil {
			return
		}
		defer newFile.Close()
		fmt.Fprintf(newFile, newFileContent)

		diffFileName := fileName + "-half-diff"
		diffFile, err := os.OpenFile(diffFileName, os.O_TRUNC|os.O_CREATE|os.O_RDWR, 0666)
		if err != nil {
			return
		}
		defer diffFile.Close()
		fmt.Fprintf(diffFile, diffContent)
	}
}

// 针对半托管文件，不托管文件， 做 wf ide 代码生成计算
// 全量计算
func (r *Runner) processDiffIdeCodeContent(filePath string, localCode string) {

	return
}

func getDiffContent(content string) []*AddCode {
	var addCodes []*AddCode

	slice := strings.Split(content, "\n")

	var curAddCode *AddCode

	for _, line := range slice {
		if strings.HasPrefix(line, "+++") || strings.HasPrefix(line, "---") {
			continue
		}

		if strings.HasPrefix(line, "+") {
			line = strings.TrimLeft(line, "+")
			line = strings.TrimSpace(line)
			if curAddCode == nil {
				curAddCode = &AddCode{
					Code: line,
				}
			} else {
				curAddCode.Code += line + "\n"
			}
		} else {
			if curAddCode != nil {
				addCodes = append(addCodes, curAddCode)
				curAddCode = nil
			}

		}
	}

	return addCodes

}

func processDiffContent(content string) (int, int) {
	addCnt := 0 // 增加的行数
	subCnt := 0 // 减少的行数

	slice := strings.Split(content, "\n")
	for _, line := range slice {
		if strings.HasPrefix(line, "+++") || strings.HasPrefix(line, "---") {
			continue
		}
		if strings.HasPrefix(line, "+") {
			addCnt++
		}
		if strings.HasPrefix(line, "-") {
			subCnt++
		}
	}
	return addCnt, subCnt
}

func (r *Runner) computeLineCntOnCodeModeZero(fileName string) []*WFCodeMeta {
	localFileByte, err := os.ReadFile(fileName)
	if err != nil {
		errMsg := fmt.Sprintf("[msg: os.ReadFile failed][file name: %s][err: %s]", fileName, err.Error())
		golog.Error(errMsg)
		utils.SendWarnMessageToHiForDailyMessage(errMsg)
		return nil
	}

	localFileByte, err = format.Source(localFileByte)
	if err != nil {
		golog.Error(err.Error())
		return nil
	}

	localFile := string(localFileByte)

	localCodeAfterDeleting, wfCodeMetaSlice := SplitLocalCode(localFile)

	fileInfo := r.FilesInfos[fileName]
	if fileInfo == nil {
		// 正常逻辑走不到这里, 预防代码可能的bug导致的panic
		errMsg := fmt.Sprintf("file info is nil, file name: %s", fileName)
		utils.SendWarnMessageToHiForDailyMessage(errMsg)
		return nil
	}

	// 重置 LineCnt
	fileInfo.LineCnt = getLineCnt(localCodeAfterDeleting)

	rpcTotalLine := 0
	if r.CurMetrics != nil && r.CurMetrics.fileName == fileName {
		rpcTotalLine = r.CurMetrics.RPCPluginTotalLine
	}

	if isTestFile(fileName) {
		r.fileUserUTCodeResult[fileName] = fileInfo.LineCnt - rpcTotalLine
		r.fileEasyUTCodeResult[fileName] = rpcTotalLine
	}

	r.fileUserCodeResult[fileName] = fileInfo.LineCnt - rpcTotalLine
	r.fileEasyCodeResult[fileName] = rpcTotalLine

	var fileNameCodeInfo []easyutil.EasyIdeWFCodeInfo
	// 存储的时候相对文件名不易获取，存的绝对文件名，这里遍历提取一下
	for absFileName, remoteWFCodeItem := range r.WFIdeCodeInfoMap {
		if !strings.HasSuffix(absFileName, fileName) {
			continue
		}
		fileNameCodeInfo = append(fileNameCodeInfo, remoteWFCodeItem)
	}

	// 在以上的计数上计算 wfcode 代码
	r.computeWFV2Code(fileName, fileNameCodeInfo, wfCodeMetaSlice)
	return wfCodeMetaSlice

}

func (r *Runner) sendHalfFiles() error {
	addBody := &FileExistAddBody{
		FileNames: make([]string, 0),
	}
	for fileName := range HalfFileNameMap {
		addBody.FileNames = append(addBody.FileNames, fileName)
	}
	addBody.RepoName = r.ProjectName

	bodyJSON, err := json.Marshal(addBody)
	if err != nil {
		golog.Error("[msg json marshal failed][err: %v]", err)
		return err
	}

	var postErr error
	var resp httpclient.HttpResponse

	body := string(bodyJSON)
	golog.Info("[msg: http post half files][url: %s] [body: %s]", r.AddFileExistURL, body)
	for i := 0; i < 3; i++ {
		// 写多个表耗时较长
		resp, postErr = httpclient.Post(r.AddFileExistURL, map[string]string{}, 2000, 10000, body)
		if postErr != nil {
			golog.Error("[msg: http post failed, add half file failed][i: %v][url: %s][err: %v][resp: %v]", i, r.AddCodeMetricURL, postErr, resp)
			continue
		}

		golog.Info("[msg: http post success][url: %s] [body: %s]", r.AddCodeMetricURL, body)
		break
	}
	if postErr != nil {
		golog.Error("[msg: http post failed, send data to interface add failed][url: %s][err: %v][resp: %v]", r.AddCodeMetricURL, postErr, resp)
		return postErr
	}

	if resp.StatusCode != http.StatusOK {
		msg := fmt.Sprintf("[msg: http post is not ok, http status is %d, http body is %s", resp.StatusCode, string(resp.Body))
		golog.Error(msg)
		return errors.New(msg)
	}

	var respMap map[string]interface{}
	golog.Info("add file body: %s", string(resp.Body))
	if err := json.Unmarshal(resp.Body, &respMap); err != nil {
		golog.Error("[msg: json unmarshal failed][err: %v]", err)
		return err
	}

	if respMap["errno"] != "200" {
		golog.Error("add file failed, resp: %v", string(resp.Body))
		return errors.New("add file failed")
	}

	return nil
}

func (r *Runner) printAndSendCodeMetrics() error {
	codeMetricBody := &RespBodyCodeMetric{}
	// 从全量文件结果计算得到全量的目录结果, 总结果

	// 非单测
	for fileName, cnt := range r.fileUserCodeResult {
		dirName := filepath.Dir(fileName)
		r.allDir[dirName] = struct{}{}

		r.dirUserCodeResult[dirName] += cnt
		r.AllUserCodeCnt += cnt
	}

	// 单测
	for _, cnt := range r.fileUserUTCodeResult {
		r.AllUserUTCodeCnt += cnt
	}

	for fileName, cnt := range r.fileEasyCodeResult {
		dirName := filepath.Dir(fileName)
		r.allDir[dirName] = struct{}{}

		r.dirEasyCodeResult[dirName] += cnt
		r.AllEasyCodeCnt += cnt
	}

	// 单测
	for _, cnt := range r.fileEasyUTCodeResult {
		r.AllEasyUTCodeCnt += cnt
	}

	// 从增量文件结果计算得到增量的目录结果，总结果
	for fileName, cnt := range r.increFileUserCodeResult {
		dirName := filepath.Dir(fileName)

		r.increDirUserCodeResult[dirName] += cnt
		r.increAllUserCodeCnt += cnt
	}
	for fileName, cnt := range r.increFileEasyCodeResult {
		dirName := filepath.Dir(fileName)

		r.increDirEasyCodeResult[dirName] += cnt
		r.increAllEasyCodeCnt += cnt
	}

	// 按文件粒度
	//fmt.Println("easy代码行数\t用户代码行数\t文件名称\t")
	for fileName := range r.FilesInfos {
		easyCnt, ok := r.fileEasyCodeResult[fileName]
		if !ok {
			easyCnt = 0
		}
		userCnt, ok := r.fileUserCodeResult[fileName]
		if !ok {
			userCnt = 0
		}

		increEasyCnt, ok := r.increFileEasyCodeResult[fileName]
		if !ok {
			increEasyCnt = 0
		}
		increUserCnt, ok := r.increFileUserCodeResult[fileName]
		if !ok {
			increUserCnt = 0
		}

		utIncreEasyCnt, ok := r.increFileEasyUTCodeResult[fileName]
		if !ok {
			utIncreEasyCnt = 0
		}
		utIncreUserCnt, ok := r.increFileUserUTCodeResult[fileName]
		if !ok {
			utIncreEasyCnt = 0
		}

		utEasyCnt, ok := r.fileEasyUTCodeResult[fileName]
		if !ok {
			utIncreEasyCnt = 0
		}
		utUserCnt, ok := r.fileUserUTCodeResult[fileName]
		if !ok {
			utIncreEasyCnt = 0
		}

		if DebugMode {
			fmt.Println("ut incre easy: ", utIncreEasyCnt,
				"ut incre user: ", utIncreUserCnt,
				"ut easy: ", utEasyCnt,
				"ut user: ", utUserCnt,
				"easyCnt: ", easyCnt, "\tincreEasyCnt: ", increEasyCnt, "\tuserCnt: ", userCnt, "increUserCnt: ", increUserCnt, "\tfileName: ", fileName)
		}
	}

	// 按目录粒度产出结果
	//fmt.Println("easy代码行数\t用户代码行数\t目录名称\t")
	for dirName := range r.allDir {
		easyCnt, ok := r.dirEasyCodeResult[dirName]
		if !ok {
			easyCnt = 0
		}
		userCnt, ok := r.dirUserCodeResult[dirName]
		if !ok {
			userCnt = 0
		}
		increEasyCnt, ok := r.increDirEasyCodeResult[dirName]
		if !ok {
			increEasyCnt = 0
		}
		increUserCnt, ok := r.increDirUserCodeResult[dirName]
		if !ok {
			increUserCnt = 0
		}

		if DebugMode {
			fmt.Println("easyCnt: ", easyCnt, "increEasyCnt: ", increEasyCnt, "\tuserCnt: ", userCnt, "increUserCnt: ", increUserCnt, "\tdirName: ", dirName)
		}
	}

	if DebugMode {
		fmt.Println("easy代码总行数: ", r.AllEasyCodeCnt)
		fmt.Println("easy代码总增量行数", r.increAllEasyCodeCnt)
		fmt.Println("user代码总行数: ", r.AllUserCodeCnt)
		fmt.Println("user代码总增量行数: ", r.increAllUserCodeCnt)

		fmt.Println("easy代码总行数(不包含单测): ", r.AllEasyCodeCnt-r.AllEasyUTCodeCnt)
		fmt.Println("easy代码总增量行数(不包含单测):", r.increAllEasyCodeCnt-r.increAllEasyUTCodeCnt)
		fmt.Println("user代码总行数(不包含单测): ", r.AllUserCodeCnt-r.AllUserUTCodeCnt)
		fmt.Println("user代码总增量行数(不包含单测): ", r.increAllUserCodeCnt-r.increAllUserUTCodeCnt)

	}

	timeStamp := time.Now().Unix()
	newTime, err := getCreateAt()
	if err == nil {
		timeStamp = newTime
	}
	codeMetricBody.FileData = make([]*FileData, 0)
	codeMetricBody.RepoName = r.ProjectName
	codeMetricBody.CreateAt = timeStamp
	codeMetricBody.IncreEasyTotalCnt = int32(r.increAllEasyCodeCnt)
	codeMetricBody.IncreUserTotalCnt = int32(r.increAllUserCodeCnt)
	codeMetricBody.EasyTotalCnt = int32(r.AllEasyCodeCnt)
	codeMetricBody.UserTotalCnt = int32(r.AllUserCodeCnt)

	codeMetricBody.CommitID = r.CommitID
	codeMetricBody.BaseCommitID = r.BaseCommitID
	codeMetricBody.CommitUser = r.CommitUser
	codeMetricBody.CommitURL = commitURLGlobal

	codeMetricBody.EasyUtTotalCnt = int32(r.AllEasyUTCodeCnt)
	codeMetricBody.UserUtTotalCnt = int32(r.AllUserUTCodeCnt)
	codeMetricBody.IncreEasyUtTotalCnt = int32(r.increAllEasyUTCodeCnt)
	codeMetricBody.IncreUserUtTotalCnt = int32(r.increAllUserUTCodeCnt)

	bodyJSON, err := json.Marshal(codeMetricBody)
	if err != nil {
		golog.Error("[msg json marshal failed][err: %v]", err)
		return err
	}

	var postErr error
	var resp httpclient.HttpResponse

	body := string(bodyJSON)
	golog.Info("[body info] [url: %s] [body: %v]", r.AddCodeMetricURL, body)
	for i := 0; i < 3; i++ {
		// 写多个表耗时较长
		resp, postErr = httpclient.Post(r.AddCodeMetricURL, map[string]string{}, 2000, 10000, body)
		if postErr != nil {
			golog.Error("[msg: http post failed, send data to interface add failed][i: %v][url: %s][err: %v][resp: %v]", i, r.AddCodeMetricURL, postErr, resp)
			continue
		}

		golog.Info("[msg: http post success][url: %s] [body: %s]", r.AddCodeMetricURL, body)
		break
	}
	if postErr != nil {
		golog.Error("[msg: http post failed, send data to interface add failed][url: %s][err: %v][resp: %v]", r.AddCodeMetricURL, postErr, resp)
		return postErr
	}
	if resp.StatusCode != http.StatusOK {
		msg := fmt.Sprintf("[msg: http post is not ok, http status is %d, http body is %s", resp.StatusCode, string(resp.Body))
		golog.Error(msg)
		return errors.New(msg)
	}

	return nil
}

func getCreateAt() (int64, error) {
	cmd := "git log -n 1 --format=\"%ct\""
	output, err := utils.ExecScript(cmd)
	if err != nil {
		golog.Error("[msg: get create at failed][err: %v]", err)
		return 0, err
	}

	outputNum := strings.TrimSpace(output)
	unixStamp, err := strconv.Atoi(outputNum)
	if err != nil {
		golog.Error("[msg: atoi failed][err: %v]", err)
		return 0, err
	}
	return int64(unixStamp + 300), nil
}

// 按目录维度来产出结果
func getLineCnt(fileContent string) int {
	lineSlice := strings.Split(fileContent, "\n")
	return len(lineSlice)
}

func isTestFile(fileName string) bool {
	return strings.HasSuffix(fileName, "_test.go")
}
