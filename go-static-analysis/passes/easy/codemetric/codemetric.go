package codemetric

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

var (
	DebugMode         bool
	debugModeFlag     = flag.Bool("debug", false, "debug mode")
	nowCommitIDFlag   = flag.String("new_commit_id", "", "new commit id")
	nowCommitIDGlobal string
	lastCommitIDFlag  = flag.String("last_commit_id", "", "last commit id")
	lastCommitID      string
	commitURLFlag     = flag.String("commit_url", "", "git commit url")
	commitURLGlobal   string
)

func Main() {
	pwd, err := os.Getwd()
	if err != nil {
		fmt.Println("get current working directory error: ", err)
		return
	}

	isMerge, output, err := isMergeCommit()
	if err != nil {
		sendHiMsg := fmt.Sprintf("[check merge commit failed] [err: %v] [pwd: %s] [output: %s]", err, pwd, output)
		fmt.Println(sendHiMsg)
		utils.SendWarnMessageToHiForDailyMessage(sendHiMsg)
		return
	}

	// 如果是merge请求则过滤掉
	if isMerge {
		// 先 hi 报警观察，后续没有问题可删去该hi报警
		sendHiMsg := fmt.Sprintf("[is merge commit] [pwd: %s] [output: %s]", pwd, output)
		golog.Warn(sendHiMsg)
		fmt.Println(sendHiMsg)
		utils.SendWarnMessageToHiForDailyMessage(sendHiMsg)
		return
	}

	flag.Parse()
	DebugMode = *debugModeFlag
	if err := initLog(); err != nil {
		fmt.Printf("[msg: init log failed][err: %s]\n", err.Error())
		os.Exit(-1)
	}

	if lastCommitIDFlag == nil || *lastCommitIDFlag == "" {
		lastCommitID = "HEAD~"
	} else {
		lastCommitID = *lastCommitIDFlag
	}

	if lastCommitID == "0000000000000000000000000000000000000000" {
		// 新建分支的时候会命中该分支，此时不该统计
		golog.Info("new branch, skip")
		return
	}
	nowCommitIDGlobal = *nowCommitIDFlag

	if commitURLFlag == nil || *commitURLFlag == "" {
		fmt.Println("commitURL is empty")
	}
	commitURLGlobal = *commitURLFlag
	fmt.Printf("commitURL: %s \n", commitURLGlobal)
	golog.Info("[commit url] [url: %s]", commitURLGlobal)

	runner := NewRunner(lastCommitID)
	if err := runner.initAll(); err != nil {
		golog.Error(err.Error())
		os.Exit(-1)
	}

	// 计算结果
	runner.computeCodeLineResult(lastCommitID)
	if err := runner.printAndSendCodeMetrics(); err != nil {
		golog.Error(err.Error())
		utils.SendWarnMessageToHiForDailyMessage(err.Error())

	}

	if err := runner.sendHalfFiles(); err != nil {
		golog.Error(err.Error())
		utils.SendWarnMessageToHiForDailyMessage(err.Error())
	}

	if err := runner.SendICafeRequest(); err != nil {
		golog.Error(err.Error())
		utils.SendWarnMessageToHiForDailyMessage(err.Error())
	}

	if err := runner.SendWfStatToHi(); err != nil {
		golog.Error(err.Error())
		//utils.SendWarnMessageToHiForDailyMessage(err.Error())
	}
}

func initLog() error {
	// 和正常服务不同，这里不需要做日志切割（跑完即停止），仅记录工具上一次运行产出的结果即可
	logName := "code-metric.log"
	logNameWf := logName + ".wf"

	// 为了避免 append 在上次运行产出的日志，每次运行先remove
	if ok := isFileExist(logName); ok {
		if err := os.Remove(logName); err != nil {
			return err
		}
	}

	if ok := isFileExist(logNameWf); ok {
		if err := os.Remove(logNameWf); err != nil {
			return err
		}
	}

	if err := golog.SetFile(logName); err != nil {
		return err
	}

	golog.SetLevel(8)
	return nil
}

func isFileExist(fileName string) bool {
	info, err := os.Stat(fileName)
	if err != nil {
		return false
	}

	if info.IsDir() {
		return false
	}

	return true
}

func isMergeCommit() (bool, string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "git", "rev-list", "--parents", "-n", "1", "HEAD")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return false, string(output), err
	}

	// 解析 git rev-list 的输出
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")

	if len(lines) >= 1 {
		parents := strings.Fields(lines[0])
		if len(parents) > 2 {
			return true, "", nil
		}
		return false, "", nil

	}

	return false, "", errors.New("no parent commit")
}
