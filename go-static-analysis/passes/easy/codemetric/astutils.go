package codemetric

import (
	"go/ast"
	"go/parser"
	"go/token"
	"os"
)

func ParseFile(filename string) (*token.FileSet, *ast.File, error) {
	src, err := os.ReadFile(filename)
	if err != nil {
		return nil, nil, err
	}

	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, filename, src, parser.ParseComments)
	return fset, file, err
}

func ParseCode(code string) (*token.FileSet, *ast.File, error) {
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, "", code, parser.ParseComments)
	return fset, file, err
}
