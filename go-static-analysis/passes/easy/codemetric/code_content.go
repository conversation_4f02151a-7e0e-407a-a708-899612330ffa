package codemetric

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/sergi/go-diff/diffmatchpatch"
)

const (
	StatusDelete = -1
	StatusEqual  = 0
	StatusInsert = 1
)

type WFCodeMeta struct {
	Index int
	ID    string
	Code  string
}

// 将代码拆成多个BEGIN-END片段，以及拆外后的代码
// 返回类型： WF代码， 剔除BEGIN-END后的代码，BEGIN-END这两行没有提取，从总行数里面先剔除
func SplitLocalCode(localCode string) (string, []*WFCodeMeta) {
	localCodeSlice := strings.Split(localCode, "\n")

	var localCodeSliceAfter []string
	wfCodeMetaSlice := make([]*WFCodeMeta, 0)
	var curBeginEndInnerCode []string
	isSliceMode := false
	codeMetaIndex := 0
	curCodeID := ""
	for _, item := range localCodeSlice {
		if !(isSliceMode || (strings.Contains(item, "//") && strings.Contains(item, "@WF:"))) {
			localCodeSliceAfter = append(localCodeSliceAfter, item)
			continue
		}

		isBeginCode := false
		isEndCode := false
		if strings.Contains(item, "BEGIN") {
			isBeginCode = true
			isSliceMode = true
		}
		if strings.Contains(item, "END") {
			isEndCode = true
			isSliceMode = false
		}

		if isBeginCode {
			curCodeID = getCodeIDByBeginTag(item)
			continue
		}

		// 如果当前是BEGIN-END代码，则直接strings.Join, 并 append 代码到 codeSlice 中
		if isEndCode {
			innerCodeStr := strings.Join(curBeginEndInnerCode, "\n")
			// codeSlice = append(codeSlice, innerCodeStr)
			curBeginEndInnerCode = []string{} // 清空
			codeMetaIndex++

			wfCodeMeta := &WFCodeMeta{}
			wfCodeMeta.Code = innerCodeStr
			wfCodeMeta.ID = curCodeID
			wfCodeMeta.Index = codeMetaIndex
			wfCodeMetaSlice = append(wfCodeMetaSlice, wfCodeMeta)

			continue
		}
		curBeginEndInnerCode = append(curBeginEndInnerCode, item)
	}
	// 把剔除 begin-end 后的代码重新拼接
	localCodeAfterDeleting := strings.Join(localCodeSliceAfter, "\n")
	return localCodeAfterDeleting, wfCodeMetaSlice
}

// 格式化WF代码，去除多余的换行和前面的空字符
func formatWFCode(wfCode string) string {
	wfCode = strings.Replace(wfCode, "\t", "", -1)
	re := regexp.MustCompile(`\n{2,}`)
	wfCode = re.ReplaceAllString(wfCode, "\n\n")
	return wfCode
}

func getCodeIDByBeginTag(beginTag string) string {
	// init:662137-begin
	if beginTag == "" {
		return ""
	}

	regex := regexp.MustCompile(`init:(\d+)-begin`)
	match := regex.FindStringSubmatch(beginTag)

	if len(match) > 1 {
		return match[1]
	}

	return ""
}

func CompareWFDiff(remoteCode string, localCode string) (easyCnt float64, userCnt float64) {
	dmp := diffmatchpatch.New()
	// 计算整个文本的差异

	diffs := dmp.DiffMain(remoteCode, localCode, false)
	lastEqualLine := ""
	lastInsertLine := ""

	lastInsertEqualStatus := StatusDelete
	for _, diff := range diffs {
		diffLines := strings.Split(diff.Text, "\n")
		switch diff.Type {
		case diffmatchpatch.DiffEqual:
			if len(diffLines) > 0 {
				easyCnt += float64(len(diffLines) - 1)
			}

			if lastInsertLine != "" {
				line := lastInsertLine + diffLines[0]
				userRatio := float64(len(lastInsertLine)) / float64(len(line))
				easyRatio := 1 - userRatio

				easyCnt += easyRatio
				userCnt += userRatio

				lastInsertLine = ""
			}
			lastEqualLine = diffLines[len(diffLines)-1]
			lastInsertEqualStatus = StatusEqual
		case diffmatchpatch.DiffDelete:
		case diffmatchpatch.DiffInsert:
			if len(diffLines) > 0 {
				userCnt += float64(len(diffLines) - 1) // 先加上基础行
			}

			if lastEqualLine != "" {
				// 此时表明变更的地方是一行的最后一处
				line := lastEqualLine + diffLines[0]
				easyRatio := float64(len(lastEqualLine)) / float64(len(line))
				userRatio := 1 - easyRatio
				fmt.Println("line: ", line, ", easyRatio: ", easyRatio, ", userRatio: ", userRatio)
				easyCnt += easyRatio
				userCnt += userRatio

				lastEqualLine = ""
			}

			lastInsertLine = diffLines[len(diffLines)-1]
			lastInsertEqualStatus = StatusInsert
		default:
		}
	}

	if lastInsertEqualStatus == StatusInsert {
		userCnt += 1.0 // 最后一行
	} else if lastInsertEqualStatus == StatusEqual {
		easyCnt += 1.0 // 最后一行
	}

	return
}
