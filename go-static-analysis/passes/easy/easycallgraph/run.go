package easycallgraph

import (
	"flag"
	"fmt"
	"log"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/callchains"
)

var depth = flag.Int("depth", 36, "the depth of the callgraph")

func Run() {
	flag.Parse()
	args := flag.Args()

	ccr := callchains.NewEasyCallChainsRunner(*depth)
	if err := ccr.InitAll(false, "", args); err != nil {
		log.Fatal(err)
	}

	ccr.Run()
	if err := ccr.SendAPICallgraphMsg(); err != nil {
		fmt.Println("send err: ", err)
		return
	}
}
