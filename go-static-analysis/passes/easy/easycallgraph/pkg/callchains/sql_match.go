package callchains

import (
	"errors"
	"fmt"
	"reflect"
	"sort"
	"strings"

	"golang.org/x/tools/go/ssa"
)

// update 函数对应一个 string 语句
func (b *SSAModelStruct) MatchUpdateString(updateCall *ssa.Call) {
	doFn := &DoFn{}
	doFn.Fn = func() string { return b.matchUpdateString(updateCall) }
	doFn.FnType = FnTypeUpdate
	doFn.IsOptional = IsOptionalInstr(updateCall)
	b.DoFns = append(b.DoFns, doFn)
}

func (b *SSAModelStruct) matchUpdateString(updateCall *ssa.Call) string {
	var zeroValues []string
	args := updateCall.Call.Args
	if len(args) < 2 {
		return ""
	}

	for _, arg := range args[1:] {
		switch arg := arg.(type) {
		case *ssa.Slice:
			c := b.getSliceForUpdate(arg)
			if len(c) == 0 {
				continue
			}
			zeroValues = append(zeroValues, c...)
		// 函数参数是一个函数调用
		case *ssa.Call:
			argCall := arg
			fn := argCall.Call.StaticCallee()
			if fn == nil {
				continue
			}

			res, ok := getFnReturnValue(fn)
			if !ok {
				return ""
			}

			zeroValues = res
		case *ssa.Const:
			constVal := getConstString(arg)
			if constVal != "" {
				zeroValues = append(zeroValues)
			}
		default:
			printValueString("matchUpdateString", arg)
		}
	}

	dtoMap, err := b.getSQLModMap(b.Entity, zeroValues)
	if err != nil {
		fmt.Printf("[getSQLModMap failed][err: %s]\n", err)
		return ""
	}

	return b.updateString(dtoMap)
}

func (b *SSAModelStruct) MatchAllString(allCall *ssa.Call) {
	doFn := &DoFn{}
	doFn.Fn = func() string { return b.queryString() }
	doFn.FnType = FnTypeAll
	doFn.IsOptional = IsOptionalInstr(allCall)
	b.DoFns = append(b.DoFns, doFn)
}

func (b *SSAModelStruct) All(zeroValues ...string) string {
	sqlFields := b.getSQLFields(b.Entity, zeroValues)
	if len(sqlFields) > 0 {
		b.fields = strings.Join(sqlFields, ",")
	}

	return b.queryString()
}

func (s *SQLBuilder) queryString() string {
	if s.fields == "" {
		s.fields = "*"
	}
	table := s.table
	if s.forceIndex != "" {
		table += fmt.Sprintf(" force index(%s)", s.forceIndex)
	}

	query := fmt.Sprintf("%sSELECT %s FROM %s %s %s %s %s", s.hint, s.fields, table, s.where, s.orderFormat(), s.limitFormat(), s.offsetFormat())

	query = strings.TrimRight(query, " ")
	query = query + ";"
	fmt.Println("")
	return query
}

func (b *SSAModelStruct) MatchAndInCall(andInCall *ssa.Call) {
	args := andInCall.Call.Args
	if len(args) < 2 {
		return
	}
	var field = "andin"
	switch arg := args[1].(type) {
	case *ssa.Call:
	case *ssa.Const:
		field = arg.Value.ExactString()
	}

	argsStr := "ARG" // sql 语句中只会利用其生成占位符（问号）个数
	isOptional := IsOptionalInstr(andInCall)
	b.matchAndIn(isOptional, field, argsStr)
	b.ModelFnReferrers(andInCall)
}

func (b *SSAModelStruct) matchAndIn(isOptional bool, field string, args string) {
	b.matchIn(isOptional, field, "and", args)
}

func (b *SSAModelStruct) MatchInCall(inCall *ssa.Call) {
	callArgs := inCall.Call.Args
	if len(callArgs) < 3 {
		return
	}

	field := ""
	switch arg := callArgs[1].(type) {
	case *ssa.Call:
	case *ssa.Const:
		field = arg.Value.ExactString()
	default:
		printValueString("MatchInCall", inCall)
	}

	join := ""
	switch arg := callArgs[2].(type) {
	case *ssa.Call:
	case *ssa.Const:
		field = arg.Value.ExactString()
	default:
		printValueString("MatchInCall", inCall)
	}

	var args []string
	for _, arg := range callArgs[3:] {
		str := getConstString(arg)
		if str != "" {
			args = append(args, str)
		} else {
			args = append(args, "InStr")
		}
	}
	if len(args) == 0 {
		args = []string{"Instr"}
	}

	isOptional := IsOptionalInstr(inCall)
	b.matchIn(isOptional, field, join, args)
	b.ModelFnReferrers(inCall)
}

func (b *SSAModelStruct) matchIn(isOptional bool, field string, join string, args ...interface{}) {
	if len(args) > 0 {
		ps := make([]string, len(args))
		for i := 0; i < len(args); i++ {
			ps[i] = "?"
		}
		tags := strings.Join(ps, ",")
		str := fmt.Sprintf(" %s %s in(%s) ", join, field, tags)
		b.SQLBuilder.Where(isOptional, str, args...)
	}
}

func (b *SSAModelStruct) MatchCreateString(createCall *ssa.Call) {
	doFn := &DoFn{}
	doFn.Fn = func() string {
		return b.matchCreateString(createCall)
	}
	doFn.FnType = FnTypeCreate
	doFn.IsOptional = IsOptionalInstr(createCall)
	b.DoFns = append(b.DoFns, doFn)
}

func (b *SSAModelStruct) matchCreateString(createCall *ssa.Call) string {
	dtoMap, err := b.getSQLAddMapWitchCall(createCall)
	if err != nil {
		return ""
	}

	return b.insertString(dtoMap)
}

func (b *SSAModelStruct) getSQLAddMapWitchCall(createCall *ssa.Call) (map[string]string, error) {
	var excludes []string
	args := createCall.Call.Args
	if len(args) < 2 {
		return nil, errors.New("failed: len(args) < 2")
	}

	for _, arg := range args[1:] {
		switch arg := arg.(type) {
		case *ssa.Slice:
			c := b.getSlice(arg)
			if len(c) == 0 {
				continue
			}
			excludes = append(excludes, c...)
		}
	}
	dtoMap, err := b.getSQLAddMap(b.Entity, excludes)
	if err != nil {
		return nil, fmt.Errorf("[getSQLAddMap failed][err: %w]", err)
	}
	return dtoMap, nil
}

func (s *SQLBuilder) insertString(params map[string]string) string {
	var cols, vals []string
	for _, k := range sortedParamKeys(params) {
		cols = append(cols, k)
		vals = append(vals, "?")
		s.args = append(s.args, params[k])
	}
	if len(s.prefix) == 0 {
		s.prefix = "INSERT"
	}
	return fmt.Sprintf("%s %s INTO %s (%s) VALUES(%s);", s.prefix, s.insertPrefix, s.table, strings.Join(cols, ","), strings.Join(vals, ","))
}

func (b *SSAModelStruct) MatchDeleteString(deleteCall *ssa.Call) {
	doFn := &DoFn{}
	doFn.Fn = func() string {
		return b.deleteString()
	}
	doFn.FnType = FnTypeDelete
	doFn.IsOptional = IsOptionalInstr(deleteCall)
	b.DoFns = append(b.DoFns, doFn)
}

// deleteString Assemble the delete statement
func (s *SQLBuilder) deleteString() string {
	query := fmt.Sprintf("DELETE FROM %s %s", s.table, s.where)
	query = strings.TrimRight(query, " ")
	query = query + ";"
	return query
}

func (b *SSAModelStruct) MatchLimitCall(limitCall *ssa.Call) {
	args := limitCall.Call.Args
	if len(args) != 2 {
		return
	}
	field := "limitCnt"
	switch arg := args[1].(type) {
	case *ssa.Call:
	case *ssa.Const:
		field = arg.Value.ExactString()
	case *ssa.UnOp:
	case *ssa.Extract:
	default:
		fmt.Println("match limit call failed, typeof filed: ", reflect.TypeOf(arg), "name: ", arg)
	}
	isOptional := IsOptionalInstr(limitCall)
	b.matchLimit(isOptional, field)
	b.ModelFnReferrers(limitCall)
}

func (b *SSAModelStruct) matchLimit(isOptional bool, limit string) {
	if isOptional {
		// 如果已经被数值赋过值，就不继续赋值了
		l := b.OptionalField.limit
		if l == "" || l == "limitCnt" {
			b.OptionalField.limit = limit
		}
	} else {
		if b.limit == "" || b.limit == "limitCnt" {
			b.limit = limit
		}
	}
}

func (b *SSAModelStruct) MatchSelectCall(selectCall *ssa.Call) {
	args := selectCall.Call.Args
	if len(args) != 2 {
		return
	}
	var field string
	switch arg := args[1].(type) {
	case *ssa.Call:
		field = b.selectCallArgIsCall(arg)
	default:
		fmt.Println("match select call failed, typeof filed: ", reflect.TypeOf(arg))
	}

	b.MatchSelect(field)
	b.ModelFnReferrers(selectCall)
}

func (b *SSAModelStruct) MatchSelect(field string) {
	b.fields = field
}

func (b *SSAModelStruct) selectCallArgIsCall(call *ssa.Call) string {
	var slice []string
	var sep string
	name := call.Call.Value.String()

	switch name {
	case "strings.Join":
		args := call.Call.Args
		if len(args) != 2 {
			return ""
		}

		switch argsFirst := args[0].(type) {
		case *ssa.Call:
			fn := argsFirst.Call.StaticCallee()
			if fn == nil {
				return ""
			}
			var ok bool
			slice, ok = getFnReturnValue(fn)
			if !ok {
				return ""
			}
		}

		switch argsSecond := args[1].(type) {
		case *ssa.Const:
			sepTmp := getConstString(argsSecond)
			if sepTmp == "" {
				sepTmp = ","
			}
			sep = sepTmp
		default:
			fmt.Println("argsSecond type: ", reflect.TypeOf(argsSecond), ", ", argsSecond.String())
		}

	default:
		fmt.Println("fn name")
	}

	if len(slice) != 0 && sep != "" {
		return strings.Join(slice, sep)
	}

	return "Exist Noting"
}

func (b *SSAModelStruct) MatchSetMsFlagCall(setMSFlagCall *ssa.Call) {
	args := setMSFlagCall.Call.Args
	if len(args) < 2 {
		return
	}

	field := ""
	switch arg := args[1].(type) {
	case *ssa.Call:
		// field = b.selectFnArgTypeIsCall(arg)
	case *ssa.Const:
		field = arg.Value.ExactString()
	}

	b.SetMSFlag(field)
	b.ModelFnReferrers(setMSFlagCall)
}

func (b *SSAModelStruct) SetMSFlag(flag string) {
	switch flag {
	case "0":
		b.hint = `/*{"ms_flag":"0"}*/ `
	case "1":
		b.hint = `/*{"ms_flag":"1"}*/ `
	case "2":
		b.hint = `/*{"xdb_comment":"1","ms_flag":"2"}*/ `
	}
}

func (b *SSAModelStruct) MatchWhereCall(whereCall *ssa.Call) {
	callArgs := whereCall.Call.Args
	if len(callArgs) < 3 {
		return
	}
	arg := whereCall.Call.Args[1]
	str := getWhereKey(arg)
	if str != "" {
		isOptional := IsOptionalInstr(whereCall)
		b.SQLBuilder.Where(isOptional, str, nil)
	}

	b.ModelFnReferrers(whereCall)
}

// 如果是上层函数传下来的结构体，这里不予处理（较难获取）
func getWhereKey(value ssa.Value) string {
	res := ""
	switch value := value.(type) {
	case *ssa.Call: // 暂时先特化处理 fmt.Sprintf的case
		call := value
		if call.Call.Value.String() == "fmt.Sprintf" && len(value.Call.Args) != 0 {
			return getWhereKey(call.Call.Args[0])
		}
	case *ssa.Const:
		res := getConstString(value)
		return res
	case *ssa.UnOp:

	default:
		fmt.Println("where key type: ", reflect.TypeOf(value), value.String())
	}
	return res
}

func (b *SSAModelStruct) MatchCountString(countCall *ssa.Call) {
	doFn := &DoFn{}
	doFn.Fn = b.countString
	doFn.FnType = FnTypeCount
	doFn.IsOptional = IsOptionalInstr(countCall)
	b.DoFns = append(b.DoFns, doFn)
}

// countString Assemble the count statement
func (s *SQLBuilder) countString() string {
	query := fmt.Sprintf("%sSELECT count(*) FROM %s %s", s.hint, s.table, s.where)
	query = strings.TrimRight(query, " ")
	query = query + ";"

	return query
}

func (b *SSAModelStruct) MatchSumString(sumCall *ssa.Call) {
	args := sumCall.Call.Args
	if len(args) < 2 {
		return
	}

	field := ""
	switch arg := args[1].(type) {
	case *ssa.Call:
		// field = b.selectFnArgTypeIsCall(arg)
	case *ssa.Const:
		field = arg.Value.ExactString()
	default:
		fmt.Println("sumCall: ", sumCall.String())
	}

	doFn := &DoFn{}
	doFn.Fn = func() string { return b.sumString(field) }
	doFn.FnType = FnTypeAll
	doFn.IsOptional = IsOptionalInstr(sumCall)
	b.DoFns = append(b.DoFns, doFn)
}

func (s *SQLBuilder) sumString(field string) string {
	query := fmt.Sprintf("%sSELECT sum(%s) FROM %s %s", s.hint, field, s.table, s.where)
	query = strings.TrimRight(query, " ")
	query = query + ";"

	return query
}

func (b *SSAModelStruct) MatchIgnoreCreateString(createCall *ssa.Call) {
	b.insertPrefix = "IGNORE"
	b.MatchCreateString(createCall)
}

func (b *SSAModelStruct) MatchReplaceCreateString(createCall *ssa.Call) {
	b.prefix = "REPLACE"
	b.MatchCreateString(createCall)
}

func (b *SSAModelStruct) MatchCreateListString(createCall *ssa.Call) {
	doFn := &DoFn{}
	doFn.Fn = func() string {
		dtoMap, err := b.getSQLAddMapWitchCall(createCall)
		if err != nil {
			fmt.Println("MatchCreateListString failed, err:", err)
			return ""
		}
		return b.insertListString(dtoMap)
	}
	doFn.FnType = FnTypeCreateList
	doFn.IsOptional = IsOptionalInstr(createCall)
	b.DoFns = append(b.DoFns, doFn)
}

func (b *SSAModelStruct) MatchOffsetCall(offsetCall *ssa.Call) {
	if len(offsetCall.Call.Args) != 2 {
		return
	}

	offset := "offset"
	arg := offsetCall.Call.Args[1]
	switch arg := arg.(type) {
	case *ssa.Const:
		offset = arg.Value.ExactString()
	default:
		printValueString("MatchOffsetCall", arg)
	}

	b.matchOffset(offset)
	b.ModelFnReferrers(offsetCall)
}

// Offset
func (b *SSAModelStruct) matchOffset(offset string) {
	b.offset = offset
}

func (b *SSAModelStruct) MatchOrderbyeCall(orderByCall *ssa.Call) {
	args := orderByCall.Call.Args
	if len(args) != 2 {
		return
	}
	field := "orderByField"
	switch arg := args[1].(type) {
	case *ssa.Call:
	case *ssa.Const:
		field = arg.Value.ExactString()
	case *ssa.UnOp:
	case *ssa.Extract:
	default:
		fmt.Println("match orderby call failed, typeof filed: ", reflect.TypeOf(arg), "name: ", arg)
	}

	isOptional := IsOptionalInstr(orderByCall)

	b.matchOrderby(isOptional, field)
	b.ModelFnReferrers(orderByCall)
}

func (b *SSAModelStruct) matchOrderby(isOptional bool, field string) {
	if isOptional {
		b.OptionalField.order = field
	} else {
		b.order = field
	}
}

func (b *SSAModelStruct) MatchOrInCall(orInCall *ssa.Call) {
	args := orInCall.Call.Args
	if len(args) < 2 {
		return
	}
	var field = "orInField"
	switch arg := args[1].(type) {
	case *ssa.Call:
		// field = b.selectFnArgTypeIsCall(arg)
	case *ssa.Const:
		field = arg.Value.ExactString()
	}
	argsStr := "TODO"
	isOptional := IsOptionalInstr(orInCall)
	b.matchOrIn(isOptional, field, argsStr)
	b.ModelFnReferrers(orInCall)
}

func (b *SSAModelStruct) matchOrIn(isOptional bool, field string, args string) {
	b.matchIn(isOptional, field, "or", args)
}

func (b *SSAModelStruct) MatchTableNameCall(tableName *ssa.Call) {
	args := tableName.Call.Args
	if len(args) < 2 {
		return
	}
	table := "tableName"
	switch arg := args[1].(type) {
	case *ssa.Call:
		// field = b.selectFnArgTypeIsCall(arg)
	case *ssa.Const:
		table = arg.Value.ExactString()
	}

	isOptional := IsOptionalInstr(tableName)
	b.matchTableName(isOptional, table)
	b.ModelFnReferrers(tableName)
}

func (b *SSAModelStruct) matchTableName(isOptional bool, table string) {
	if isOptional {
		b.OptionalField.table = table
	} else {
		b.table = table
	}
}

func (b *SSAModelStruct) MatchHintCall(hintCall *ssa.Call) {
	args := hintCall.Call.Args
	if len(args) < 2 {
		return
	}
	hint := "hint"
	switch arg := args[1].(type) {
	case *ssa.Call:
		// field = b.selectFnArgTypeIsCall(arg)
	case *ssa.Const:
		hint = arg.Value.ExactString()
	}

	isOptional := IsOptionalInstr(hintCall)
	b.matchHint(isOptional, hint)
	b.ModelFnReferrers(hintCall)
}

func (b *SSAModelStruct) matchHint(isOptional bool, hint string) {
	if isOptional {
		b.OptionalField.hint = hint
	} else {
		b.hint = hint
	}
}

func (b *SSAModelStruct) MatchForceIndexCall(forceIndexCall *ssa.Call) {
	args := forceIndexCall.Call.Args
	if len(args) < 2 {
		return
	}
	forceIndex := "forceIndex"
	switch arg := args[1].(type) {
	case *ssa.Call:
		// field = b.selectFnArgTypeIsCall(arg)
	case *ssa.Const:
		forceIndex = arg.Value.ExactString()
	}

	isOptional := IsOptionalInstr(forceIndexCall)
	b.matchForceIndex(isOptional, forceIndex)
	b.ModelFnReferrers(forceIndexCall)
}

func (b *SSAModelStruct) matchForceIndex(isOptional bool, forceIndex string) {
	if isOptional {
		b.OptionalField.forceIndex = forceIndex
	} else {
		b.forceIndex = forceIndex
	}
}

func (b *SSAModelStruct) getConstFromArg(arg ssa.Value) string {
	if c, ok := arg.(*ssa.Const); ok {
		return c.Value.ExactString()
	}

	return ""
}

func (s *SQLBuilder) limitFormat() string {
	if s.limit != "" {
		return fmt.Sprintf("LIMIT %s", s.limit)
	}
	return ""
}

func (s *SQLBuilder) optionalLimitFormat() string {
	optionalLimit := s.OptionalField.limit
	if optionalLimit != "" {
		return fmt.Sprintf("LIMIT %s", optionalLimit)
	}
	return ""
}

func (s *SQLBuilder) offsetFormat() string {
	if s.offset != "" {
		return fmt.Sprintf("OFFSET %s", s.offset)
	}
	return ""
}

func (s *SQLBuilder) optionalOffsetFormat() string {
	optionalOffset := s.OptionalField.offset
	if optionalOffset != "" {
		return fmt.Sprintf("OFFSET %s", optionalOffset)
	}
	return ""
}

func (s *SQLBuilder) orderFormat() string {
	if s.order != "" {
		return fmt.Sprintf("ORDER BY %s", s.order)
	}
	return ""
}

func (s *SQLBuilder) optionalOrderFormat() string {
	optionalOrder := s.OptionalField.order
	if optionalOrder != "" {
		return fmt.Sprintf("ORDER BY %s", optionalOrder)
	}
	return ""
}

func (s *SQLBuilder) insertListString(params DtoMap) string {
	var values []string
	var colsStr string

	// 插入一个list, 这里暂定长度为 2
	for i := 0; i < 2; i++ {
		var cols, vals []string

		for _, k := range sortedParamKeys(params) {
			cols = append(cols, k)
			vals = append(vals, "?")
			s.args = append(s.args, params[k])
		}
		colsStr = strings.Join(cols, ",")
		values = append(values, fmt.Sprintf("(%s)", strings.Join(vals, ",")))
	}
	if len(s.prefix) == 0 {
		s.prefix = "INSERT"
	}

	return fmt.Sprintf("%s %s INTO %s (%s) VALUES %s;", s.prefix, s.insertPrefix, s.table, colsStr, strings.Join(values, ","))
}

// updateString Assemble the update statement
func (s *SQLBuilder) updateString(params map[string]string) string {
	var updateFields []string
	args := make([]interface{}, 0)

	for _, k := range sortedParamKeys(params) {
		updateFields = append(updateFields, fmt.Sprintf("%s=?", k))
		args = append(args, params[k])
	}

	args = append(args, s.args...)
	s.args = args

	query := fmt.Sprintf("UPDATE %s SET %s %s", s.table, strings.Join(updateFields, ","), s.where)
	query = strings.TrimRight(query, " ")
	query = query + ";"
	return query
}

func (s *SQLBuilder) Where(isOptional bool, str string, args ...interface{}) {
	if isOptional {
		s.optionalWhere(str, args)
	} else {
		s.requiredWhere(str, args)
	}
}

func (s *SQLBuilder) optionalWhere(str string, args ...interface{}) {
	str = strings.TrimSpace(str)
	ckStr := strings.ToLower(str)
	isHasPrefix := strings.HasPrefix(ckStr, "and") || strings.HasPrefix(ckStr, "or")
	if s.OptionalField.where != "" {
		str = strings.TrimSpace(str)
		if !isHasPrefix {
			s.OptionalField.where = fmt.Sprintf("%s OR (%s)", s.OptionalField.where, str)
		} else {
			s.OptionalField.where = fmt.Sprintf("%s %s", s.OptionalField.where, str)
		}
	} else {
		if isHasPrefix {
			str = strings.TrimPrefix(str, "and ")
			str = strings.TrimPrefix(str, "or ")
		}
		s.OptionalField.where = fmt.Sprintf("WHERE (%s)", str)
	}
}

func (s *SQLBuilder) requiredWhere(str string, args ...interface{}) {
	str = strings.TrimSpace(str)
	ckStr := strings.ToLower(str)
	isHasPrefix := strings.HasPrefix(ckStr, "and") || strings.HasPrefix(ckStr, "or")
	if s.where != "" {
		str = strings.TrimSpace(str)
		if !isHasPrefix {
			s.where = fmt.Sprintf("%s AND (%s)", s.where, str)
		} else {
			s.where = fmt.Sprintf("%s %s", s.where, str)
		}
	} else {
		if isHasPrefix {
			str = strings.TrimPrefix(str, "and ")
			str = strings.TrimPrefix(str, "or ")
		}
		s.where = fmt.Sprintf("WHERE (%s)", str)
	}
}

func sortedParamKeys(params map[string]string) []string {
	sortedKeys := make([]string, len(params))
	i := 0
	for k := range params {
		sortedKeys[i] = k
		i++
	}
	sort.Strings(sortedKeys)
	return sortedKeys
}
