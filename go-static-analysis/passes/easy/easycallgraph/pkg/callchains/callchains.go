package callchains

import (
	"encoding/json"
	"errors"
	"fmt"
	"go/build"
	"go/types"
	"log"
	"os"
	"strings"

	"golang.org/x/tools/go/packages"
	"golang.org/x/tools/go/ssa"
	"golang.org/x/tools/go/ssa/ssautil"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/callgraph"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/callgraph/static"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/utils"
)

const (
	MaxDepth        = 32
	EasySDKPkg      = "icode.baidu.com/baidu/netdisk/easy-go-sdk"
	BaiduRepoSubStr = "8235/baidu"
)

type CallRelation struct {
	Caller    string          `json:"caller"`
	Callee    []*CallRelation `json:"callee,omitempty"`
	SQLStruct *SQLStruct      `json:"sql_struct,omitempty"`
}

type EasyCallChainsRunner struct {
	DFSMaxDepth     int
	RepoName        string
	ICodeName       string
	DaoNames        []string
	ControllerNames []string

	Prog                   *ssa.Program
	Graph                  *callgraph.Graph
	AllPackages            []*ssa.Package
	APIFunctions           map[*ssa.Function]string        // api函数
	CallRelationForRootMap map[*ssa.Function]*CallRelation // 存储结果
	SQLEntryFunctions      map[*ssa.Function]struct{}      // dao层函数
	TableNames             map[*ssa.Type]map[string]string // map[string]string中，结构体成员名称到 sql名称的映射，以及key:"sql_name", value:sqlNmae
	TypeMap                map[string]*ssa.Type
	FuncsVisit             map[*ssa.Function]struct{}
	ChainsVisit            map[string]struct{}
	ChainsToSQLMap         map[string]*SQLStruct // key: chains name, value: sql stmt
}

func NewEasyCallChainsRunner(maxDepth int) *EasyCallChainsRunner {
	return &EasyCallChainsRunner{
		DFSMaxDepth:            maxDepth,
		TypeMap:                make(map[string]*ssa.Type),
		TableNames:             make(map[*ssa.Type]map[string]string),
		CallRelationForRootMap: make(map[*ssa.Function]*CallRelation),
		APIFunctions:           make(map[*ssa.Function]string),
		SQLEntryFunctions:      make(map[*ssa.Function]struct{}),
		FuncsVisit:             make(map[*ssa.Function]struct{}),
		ChainsVisit:            make(map[string]struct{}),
		ChainsToSQLMap:         make(map[string]*SQLStruct),
	}
}

func (r *EasyCallChainsRunner) InitAll(tests bool, dir string, args []string) error {
	if err := r.initProg(tests, dir, args); err != nil {
		return err
	}

	r.initGraph() // 初始化函数调用链
	r.initControllerNames()
	r.initDaoNames()
	r.initTablesNames()

	if err := r.initAPIFunctions(); err != nil {
		return err
	}
	if err := r.initDaoFunctions(); err != nil {
		return err
	}
	if err := r.initRepoNameAndICodeName(); err != nil {
		return err
	}

	r.initSQLFunctionForCallChain()
	return nil
}

func (r *EasyCallChainsRunner) initProg(tests bool, dir string, args []string) error {
	cfg := &packages.Config{
		Mode:       packages.LoadAllSyntax,
		Tests:      tests,
		Dir:        dir,
		BuildFlags: build.Default.BuildTags,
	}

	initial, err := packages.Load(cfg, args...)
	if err != nil {
		return err
	}

	if packages.PrintErrors(initial) > 0 {
		return errors.New("packages contain errors")
	}

	// Create and build SSA-form program representation.
	prog, pkgs := ssautil.AllPackages(initial, 0)
	prog.Build()

	r.Prog = prog
	r.AllPackages = pkgs
	return nil
}

func (r *EasyCallChainsRunner) initControllerNames() {
	var controllerNames []string

	const (
		actionPkgName = "action" // controller 所在包
		structSuffix  = "Controller"
	)
	for _, pkg := range r.Prog.AllPackages() {
		if pkg.Pkg.Name() != actionPkgName {
			continue
		}
		for _, mem := range pkg.Members {
			ssaType, ok := mem.(*ssa.Type)
			if !ok {
				continue
			}
			if !strings.HasSuffix(ssaType.Name(), structSuffix) {
				continue
			}

			controllerNames = append(controllerNames, ssaType.Name())
		}
	}
	r.ControllerNames = controllerNames
}

func (r *EasyCallChainsRunner) initDaoNames() {
	var daoNames []string

	const (
		actionPkgName = "dao" // controller 所在包
		structSuffix  = "Dao"
	)
	for _, pkg := range r.Prog.AllPackages() {
		if pkg.Pkg.Name() != actionPkgName {
			continue
		}
		for _, mem := range pkg.Members {
			ssaType, ok := mem.(*ssa.Type)
			if !ok {
				continue
			}
			if !strings.HasSuffix(ssaType.Name(), structSuffix) {
				continue
			}
			daoNames = append(daoNames, ssaType.Name())
		}
	}

	r.DaoNames = daoNames
}

func (r *EasyCallChainsRunner) initTablesNames() {
	tablePkgName := "entity" // controller 所在包
	for _, pkg := range r.Prog.AllPackages() {
		if pkg.Pkg.Name() != tablePkgName {
			continue
		}

		for _, mem := range pkg.Members {
			ssaType, ok := mem.(*ssa.Type)
			if !ok {
				continue
			}

			if named, ok := ssaType.Type().(*types.Named); ok {
				if s, ok := named.Underlying().(*types.Struct); ok {
					if s.NumFields() != 0 && s.Field(0).Name() == "Entity" {
						r.TableNames[ssaType] = make(map[string]string)
						key := GetTypesType(ssaType.Type())
						if key == "" {
							continue
						}
						r.TypeMap[key] = ssaType
						for i := 0; i < s.NumFields(); i++ {
							tagMap := parseTag(s.Tag(i))
							if i == 0 {
								r.TableNames[ssaType]["table_name"] = tagMap["table_name"]
							}
							sqlName, ok := tagMap["sql"]
							if ok && sqlName != "table_name" {
								r.TableNames[ssaType][sqlName] = s.Field(i).Name()
							}
						}
					}
				}
			}
		}
	}
}

func parseTag(tag string) map[string]string {
	tagMap := make(map[string]string)

	tagPairs := strings.Split(tag, " ")
	for _, pair := range tagPairs {
		kv := strings.Split(pair, ":")
		if len(kv) != 2 {
			continue
		}

		key := strings.TrimSpace(kv[0])
		value := strings.TrimSpace(kv[1])

		tagMap[key] = value
	}

	return tagMap
}

func (r *EasyCallChainsRunner) getCotrollerObjects() (map[string]types.Type, error) {
	res := make(map[string]types.Type)
	for _, name := range r.ControllerNames {
		typ, err := r.getCotorlllerObject(name)
		if err != nil {
			log.Println(err)
			continue
		}

		if typ == nil {
			continue
		}

		realType := typ
		if _, ok := typ.(*types.Named); ok {
			realType = types.NewPointer(typ)
		}

		res[name] = realType
	}
	return res, nil
}

func (r *EasyCallChainsRunner) getCotorlllerObject(controllerName string) (types.Type, error) {
	for _, pkg := range r.Prog.AllPackages() {
		if pkg.Pkg.Name() != "action" {
			continue
		}

		obj := pkg.Pkg.Scope().Lookup(controllerName)
		if obj != nil {
			return obj.Type(), nil
		}
	}
	return nil, fmt.Errorf("the controllerName %s not found", controllerName)
}

func (r *EasyCallChainsRunner) getDaoObjects() (map[string]types.Type, error) {
	res := make(map[string]types.Type)
	for _, name := range r.DaoNames {
		typ, err := r.getDaoObject(name)
		if err != nil {
			continue
		}

		if typ == nil {
			continue
		}

		realType := typ
		if _, ok := typ.(*types.Named); ok {
			realType = types.NewPointer(typ)
		}

		res[name] = realType
	}
	return res, nil
}

func (r *EasyCallChainsRunner) getDaoObject(daoName string) (types.Type, error) {
	for _, pkg := range r.Prog.AllPackages() {
		if pkg.Pkg.Name() != "dao" {
			continue
		}
		obj := pkg.Pkg.Scope().Lookup(daoName)
		if obj != nil {
			return obj.Type(), nil
		}
	}
	return nil, fmt.Errorf("the controllerName %s not found", daoName)
}

func (r *EasyCallChainsRunner) initRepoNameAndICodeName() error {
	repoName, err := utils.ExecCommandWithTimeout("go list -m")
	if err != nil {
		return err
	}
	r.RepoName = strings.TrimSpace(repoName)

	icodeName, err := utils.ExecCommand(`git remote -v | head -1 | awk {'print $2'}`)
	if err != nil {
		return err
	}
	icodeName = strings.TrimSpace(icodeName)
	i := strings.Index(icodeName, BaiduRepoSubStr)
	if i == -1 || i+5 < 0 {
		return errors.New("get icode name err")
	}
	r.ICodeName = icodeName[i+5:]
	return nil
}

func (r *EasyCallChainsRunner) isEasyGoSDKFn(fn *ssa.Function) bool {
	if fn == nil {
		return false
	}
	if strings.Contains(fn.String(), EasySDKPkg) {
		return true
	}
	return false
}

func (r *EasyCallChainsRunner) isLocalFn(fn *ssa.Function) bool {
	if fn == nil {
		return false
	}
	if strings.Contains(fn.String(), r.RepoName) {
		return true
	}
	return false
}

func (r *EasyCallChainsRunner) initAPIFunctions() error {
	controllerNameMap, err := r.getCotrollerObjects()
	if err != nil {
		return err
	}

	const apiFunctionSuffix = "Core"
	// 遍历每个结构体
	for controller, typ := range controllerNameMap {
		methodSet := r.Prog.MethodSets.MethodSet(typ)
		if methodSet == nil {
			continue
		}

		// groupName := strings.TrimSuffix(controllerName, "Controller")

		// 遍历该结构体的所有方法以获取所需的方法
		for i := 0; i < methodSet.Len(); i++ {
			selection := methodSet.At(i)
			if selection == nil || selection.Obj() == nil {
				continue
			}

			name := selection.Obj().Name()
			if strings.HasSuffix(name, apiFunctionSuffix) && selection.Obj().Exported() {
				fn := r.Prog.MethodValue(selection)
				r.APIFunctions[fn] = controller
			}
		}
	}

	return nil
}

// dao包的函数
func (r *EasyCallChainsRunner) initDaoFunctions() error {
	daoNameMap, err := r.getDaoObjects()
	if err != nil {
		log.Println(err)
		return err
	}

	// 遍历每个结构体
	for _, typ := range daoNameMap {
		methodSet := r.Prog.MethodSets.MethodSet(typ)
		if methodSet == nil {
			continue
		}

		// 遍历该结构体的所有方法以获取所需的方法
		for i := 0; i < methodSet.Len(); i++ {
			selection := methodSet.At(i)
			if selection == nil || selection.Obj() == nil {
				continue
			}

			fn := r.Prog.MethodValue(selection)

			if selection.Obj().Exported() && fn.Name() != "Model" {
				fn := r.Prog.MethodValue(selection)
				r.SQLEntryFunctions[fn] = struct{}{}
			}
		}
	}

	return nil
}

func (r *EasyCallChainsRunner) initGraph() {
	graph := static.CallGraph(r.Prog)
	r.Graph = graph
}

func (r *EasyCallChainsRunner) Run() {
	graph := r.Graph
	for fn := range r.APIFunctions {
		node := graph.Nodes[fn]
		r.RunForRootNode(node)
	}
}

func (r *EasyCallChainsRunner) RunForRootNode(rootNode *callgraph.Node) {
	chainsName := rootNode.Func.Name()
	cr := r.runForNode([]*callgraph.Node{rootNode}, chainsName, rootNode, 0, map[*ssa.Function]struct{}{})
	if cr == nil || cr.Caller == "" {
		return
	}
	r.CallRelationForRootMap[rootNode.Func] = cr
}

// nolint: lll
func (r *EasyCallChainsRunner) runForNode(chainNodes []*callgraph.Node, chainNames string, node *callgraph.Node, depth int, visitMap map[*ssa.Function]struct{}) *CallRelation {
	if depth > MaxDepth {
		return nil
	}

	if _, ok := visitMap[node.Func]; ok {
		return nil
	}
	if !r.isLocalFn(node.Func) {
		return nil
	}
	visitMap[node.Func] = struct{}{}
	cr := &CallRelation{}
	cr.Callee = make([]*CallRelation, 0)
	cr.Caller = node.Func.Pkg.Pkg.Name() + "." + node.Func.RelString(node.Func.Pkg.Pkg)
	if sqlStruct, ok := r.ChainsToSQLMap[chainNames]; ok {
		cr.SQLStruct = sqlStruct
		return cr
	}

	for _, outEdge := range node.Out {
		calleeNode := outEdge.Callee
		calleeName := calleeNode.Func.Name()
		if !r.isEasyGoSDKFn(calleeNode.Func) && !r.isLocalFn(calleeNode.Func) {
			continue
		}

		chainNodes = append(chainNodes, calleeNode)
		chainNames = chainNames + "-" + calleeName

		calleeCR := r.runForNode(chainNodes, chainNames, calleeNode, depth+1, visitMap)
		if calleeCR == nil || calleeCR.Caller == "" {
			continue
		}
		cr.Callee = append(cr.Callee, calleeCR)

		chainNodes = chainNodes[:len(chainNodes)-1]
		chainNames = strings.TrimSuffix(chainNames, "-"+calleeName)
	}

	return cr
}

func (r *EasyCallChainsRunner) WriteToFile() {
	for rootFn, cr := range r.CallRelationForRootMap {
		byt, err := json.Marshal(cr)
		if err != nil {
			fmt.Println(rootFn.Name(), " json marshal failed, err is ", err)
			continue
		}

		fileName := rootFn.RelString(rootFn.Pkg.Pkg) + "-static-callchain.json"
		fd, err := os.OpenFile(fileName, os.O_CREATE|os.O_TRUNC|os.O_RDWR, 0666)
		if err != nil {
			fmt.Println(rootFn.Name(), " json marshal failed, err is ", err)
			continue
		}
		defer fd.Close()

		fmt.Fprintf(fd, string(byt))
	}
}
