package callchains

import (
	"fmt"

	"golang.org/x/tools/go/ssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/callgraph"
)

func (r *EasyCallChainsRunner) initSQLFunctionForCallChain() {
	g := r.Graph
	for entryFn := range r.APIFunctions {
		entryNode := g.Nodes[entryFn]
		chainsName := entryNode.Func.Name()
		r.runSQLGenerationDFS(chainsName, entryNode, 0)
	}
}

func (r *EasyCallChainsRunner) runSQLGenerationDFS(chainNames string, curNode *callgraph.Node, depth int) {
	if depth > r.DFSMaxDepth {
		return
	}
	if curNode == nil {
		return
	}

	if _, ok := r.FuncsVisit[curNode.Func]; ok {
		// 防止递归调用
		return
	}

	// 把当前节点加入 VisitMap 中, 避免出现环
	r.FuncsVisit[curNode.Func] = struct{}{}

	for _, out := range curNode.Out {
		callee := out.Callee
		calleeName := callee.Func.Name()
		if !r.isLocalFn(callee.Func) {
			continue
		}

		chainNames = chainNames + "-" + calleeName

		if !r.isSQLEntryFn(callee) {
			r.runSQLGenerationDFS(chainNames, callee, depth+1)
		} else {
			if _, ok := r.ChainsVisit[chainNames]; ok {
				continue
			}
			r.ChainsVisit[chainNames] = struct{}{}
			r.initSQLStmt(chainNames, callee)
		}
	}

	// 把当前节点移出 VisitMap ,避免别的DFS路径无法走到该节点
	delete(r.FuncsVisit, curNode.Func)
}

func (r *EasyCallChainsRunner) isSQLEntryFn(node *callgraph.Node) bool {
	_, ok := r.SQLEntryFunctions[node.Func]
	return ok
}

func (r *EasyCallChainsRunner) initSQLStmt(chainsName string, curNode *callgraph.Node) {
	fn := curNode.Func
	for _, blocks := range fn.DomPreorder() {
		for _, instr := range blocks.Instrs {
			call, ok := instr.(*ssa.Call)
			if !ok {
				continue
			}

			callFn := call.Call.StaticCallee()
			if callFn == nil {
				continue
			}

			if callFn.Name() != "Model" {
				continue
			}
			if len(call.Call.Args) < 2 {
				continue
			}

			daoInterface, ok := call.Call.Args[1].(*ssa.MakeInterface)
			if !ok {
				continue
			}
			x := daoInterface.X
			if x == nil {
				continue
			}

			typ := x.Type()
			key := GetTypesType(typ)
			if key == "" {
				continue
			}

			ssatype, ok := r.TypeMap[key]
			if !ok {
				continue
			}
			ms := NewSSAModuleStruct(r, ssatype, curNode, chainsName)
			if err := ms.MatchModel(call); err != nil {
				fmt.Println("match model err: ", err)
			}
		}
	}
}
