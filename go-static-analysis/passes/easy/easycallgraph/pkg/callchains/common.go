package callchains

import (
	"encoding/json"
	"errors"
	"fmt"
	"go/types"
	"net/http"
	"reflect"
	"strings"

	"github.com/bitly/go-simplejson"
	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const APIMsgCollectionURL = "http://10.138.34.59:8089/code/ssa/func"
const ConnectTimeout = 1000
const ReadWriteTimeout = 3000

const (
	// 会生成 sql 的函数
	FnTypeGet             = "Get"
	FnTypeAll             = "All"
	FnTypeCreate          = "Create"
	FnTypeDelete          = "Delete"
	FnTypeUpdate          = "Update"
	FnTypeLimit           = "Limit"
	FnTypeSetMSFlag       = "SetMSFlag"
	FnTypeCount           = "Count"
	FnTypeSum             = "Sum"
	FnTypeIgnoreCreate    = "IgnoreCreate"
	FnTypeReplaceCreate   = "ReplaceCreate"
	FnTypeCreateList      = "CreateList"
	FnTypeCreateListAsync = "CreateListAsync" // TODO

	// 用于拼接 sql 的函数
	FnTypeOrderby    = "OrderBy"
	FnTypeOffset     = "Offset"
	FnTypeWhere      = "Where"
	FnTypeSelect     = "Select"
	FnTypeIn         = "In"
	FnTypeOrIn       = "OrIn"
	FnTypeAndIn      = "AndIn"
	FnTypeForceIndex = "ForceIndex"
	FnTypeTableName  = "TableName"
	FnTypeHint       = "Hint"

	// 难以适配
	FnTypeTable    = "Table"    // 该函数逻辑由传入一个匿名函数去确认，ssa难以适配
	FnTypePage     = "Page"     // 暂不适配，该函数调用了 Count, Limit, All，这里没有对应的sql对应该函数的语义，不予生成。
	FnTypeRelation = "Relation" //
)

type APICallGraphInfoBody struct {
	ICode string           `json:"icode"`
	Info  []*CallGraphInfo `json:"info"`
}

type CallGraphInfo struct {
	APIName   string        `json:"api_name"`
	GroupName string        `json:"group_name"`
	CallGraph *CallRelation `json:"callgraph"`
}

func (r *EasyCallChainsRunner) newAPICallGraphInfoBody() *APICallGraphInfoBody {
	res := &APICallGraphInfoBody{}
	res.ICode = r.ICodeName
	res.Info = make([]*CallGraphInfo, 0)

	for rootFn, cr := range r.CallRelationForRootMap {
		callGraphInfo := &CallGraphInfo{}
		groupName, ok := r.APIFunctions[rootFn]
		if !ok {
			continue
		}
		callGraphInfo.GroupName = groupName
		callGraphInfo.APIName = rootFn.Name()
		callGraphInfo.CallGraph = cr
		res.Info = append(res.Info, callGraphInfo)
	}
	return res
}

func GetTypesType(typ types.Type) string {
	slice := strings.Split(typ.String(), "/")
	if len(slice) == 0 {
		return ""
	}

	return slice[len(slice)-1]
}

func (r *EasyCallChainsRunner) SendAPICallgraphMsg() error {
	header := make(map[string]string)
	header["Content-Type"] = "application/json"

	body := r.newAPICallGraphInfoBody()

	bodyJSON, err := json.Marshal(body)
	if err != nil {
		fmt.Printf("json marshal failed, err is %v\n", err)
		return err
	}

	bodyJSONString := string(bodyJSON)
	var resp httpclient.HttpResponse
	var postErr error
	for i := 0; i < 3; i++ {
		resp, postErr = httpclient.Post(APIMsgCollectionURL, header, ConnectTimeout, ReadWriteTimeout, bodyJSONString)
		if postErr != nil {
			continue
		}

		break
	}

	if postErr != nil {
		fmt.Printf("send api callgraph failed, err is %v", postErr)
		return postErr
	}

	js, err := simplejson.NewJson(resp.Body)
	if err != nil {
		fmt.Printf("simplejson.NewJson failed err is %v", err)
		return err
	}

	errno, err := js.Get("errno").Int()
	if err != nil {
		fmt.Println("get errno failed, err is ", err)
		return err
	}
	msg, err := js.Get("msg").String()
	if err != nil {
		fmt.Println("get msg failed, err is ", err)
		return err
	}

	if errno != http.StatusOK {
		errMsg := fmt.Sprintf("send api callgraph failed, errno: %d, msg: %s", errno, msg)
		//utils.SendWarnMessageToHiForDailyMessage(errMsg)
		return errors.New(errMsg)
	}

	return nil
}

func getConstString(value ssa.Value) string {
	switch value := value.(type) {
	case *ssa.Const:
		return value.Value.ExactString()
	default:
		fmt.Println("get const string failed, value type is", reflect.TypeOf(value))
		return ""
	}
}

func inSlice(k string, s []string) bool {
	for _, v := range s {
		if k == v {
			return true
		}
	}
	return false
}

func (b *SSAModelStruct) getSQLFields(entity *ssa.Type, zeroValues []string) []string {
	tablesNames := b.Runner.TableNames
	dtoMap, ok := tablesNames[entity]
	if !ok {
		return nil
	}
	sqlFieldMap := copyMapWithoutTableName(dtoMap)

	var res []string
	for _, v := range zeroValues {
		if _, ok := sqlFieldMap[v]; ok {
			res = append(res, v)
		}
	}

	if len(res) == 0 {
		return nil
	}

	return res
}

func IsOptionalInstr(instr ssa.Instruction) bool {
	comment := instr.Block().Comment
	if comment == "if.then" || comment == "if.else" {
		return true
	}
	return false
}
