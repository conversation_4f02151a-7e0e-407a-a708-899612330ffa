package utils

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"
)

func ExecCommand(script string, args ...string) (string, error) {
	cmd := exec.Command("/bin/bash", append([]string{"-s", "-"}, args...)...)
	wc, err := cmd.StdinPipe()
	if err != nil {
		return "", err
	}

	go func() {
		defer wc.Close()
		fmt.Fprintf(wc, "%s", script)
	}()

	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func ExecCommandWithTimeout(script string, args ...string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "/bin/bash", append([]string{"-s", "-"}, args...)...)
	wc, err := cmd.StdinPipe()
	if err != nil {
		return "", err
	}

	go func() {
		defer wc.Close()
		fmt.Fprintf(wc, "%s", script)
	}()

	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

/*
func IsOuterFunc(fn *ssa.Function) bool {
	// 开头
	funcPrefixs := []string{
		"errors", "time", "strings", "strconv", "hash/crc32", "net", "fmt",
	}
	funcName := fn.String()
	for _, prefix := range funcPrefixs {
		if strings.HasPrefix(funcName, prefix) {
			return true
		}
	}

	otherNeedToFilt := []string{
		"(time.Time)",
		"easy-go-sdk.ErrorDto",
		"easy-go-sdk.CustomError",
		"json.Unmarshal",
		"tangram_logger",
	}

	matcher := ahocorasick.NewStringMatcher(otherNeedToFilt)
	if matches := matcher.Match([]byte(fn.String())); len(matches) > 0 {
		return true
	}

	return false
}
*/
