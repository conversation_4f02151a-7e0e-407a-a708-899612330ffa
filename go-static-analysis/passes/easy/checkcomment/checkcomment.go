package checkcomment

import (
	"bufio"
	"encoding/json"
	"fmt"
	"go/ast"
	"go/token"
	"os"
	"strings"
	"sync"

	"golang.org/x/tools/go/analysis"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

const maxBranchDepth = 2
const reportURL = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dccbcbe7914e2c08351def6bde9226d18"

var codeList = []string{
	"baidu/netdisk/nd-membership-order-v3",
	"baidu/netdisk/nd-membership-proxy-v3",
	"baidu/netdisk/nd-membership-user-v3",
	"baidu/netdisk/nd-membership-product-v3",
}

var onceDo sync.Once
var isCode bool

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:  "checkcomment",
		Name: "checkcomment",
		Run:  run,
	}
}

func run(pass *analysis.Pass) (interface{}, error) {
	onceDo.Do(func() {
		isCode = inCodeList()
	})

	if !isCode {
		return nil, nil
	}

	for _, file := range pass.Files {
		if ok := file.Pos().IsValid(); !ok {
			continue
		}

		ast.Inspect(file, func(n ast.Node) bool {
			// 检查函数声明
			fn, ok := n.(*ast.FuncDecl)
			if !ok || fn.Body == nil {
				return true
			}

			checkBranchComments(pass, fn.Body, pass.Fset, 0)
			return true
		})
	}

	return nil, nil
}

// checkBranchComments 递归地检查块中的分支，确保分支深度大于2的地方前面有注释
func checkBranchComments(pass *analysis.Pass, block *ast.BlockStmt, fset *token.FileSet, depth int) {
	for _, stmt := range block.List {
		switch s := stmt.(type) {
		case *ast.IfStmt:
			if depth > maxBranchDepth {
				checkForComment(pass, s, fset)
				return
			}

			for _, stmt := range s.Body.List {
				switch stmt.(type) {
				case *ast.ReturnStmt:
					return
				}
			}

			checkBranchComments(pass, s.Body, fset, depth+1)

			if s.Else != nil {
				// 对于 else 语句，需要特别处理可能的 IfStmt
				if elseIf, ok := s.Else.(*ast.IfStmt); ok {
					checkBranchComments(pass, &ast.BlockStmt{List: []ast.Stmt{elseIf}}, fset, depth+1)
				} else {
					checkBranchComments(pass, &ast.BlockStmt{List: []ast.Stmt{s.Else}}, fset, depth)
				}
			}
		}
	}
}

// checkForComment 检查给定的语句前是否有注释
func checkForComment(pass *analysis.Pass, stmt ast.Stmt, fset *token.FileSet) {
	if stmt == nil {
		return
	}

	pos := fset.Position(stmt.Pos())
	lines, err := readLines(pos.Filename)
	if err == nil {
		lineNum := pos.Line
		if lineNum > 0 && !hasPrecedingComment(lines, lineNum+1) {
			msg := fmt.Sprintf("Missing comment for branch statement %s"+":"+"%d", pos.Filename, lineNum)
			pass.Reportf(token.NoPos, msg)
			// SendWarnMessageToHi(reportURL, msg)
		}
	}
}

// hasPrecedingComment 检查给定行号之前是否有注释
func hasPrecedingComment(lines []string, lineNum int) bool {
	for i := lineNum; i >= 0; i-- {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}
		// 检查单行注释或多行注释的结束
		if strings.HasPrefix(line, "//") || strings.HasSuffix(line, "*/") {
			return true
		}
		// 如果遇到非注释行，则停止检查
		if !strings.HasPrefix(line, "/*") {
			break
		}
	}
	return false
}

// isComment 判断一行是否是注释
func isComment(line string) bool {
	trimmed := strings.TrimSpace(line)
	return strings.HasPrefix(trimmed, "//") || strings.HasPrefix(trimmed, "/*")
}

// readLines 从文件中读取内容并返回行的切片
func readLines(filename string) ([]string, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}
	return lines, scanner.Err()
}

func SendWarnMessageToHi(url, msg string) {
	headerMap := map[string]string{}
	headerMap["Content-Type"] = "application/json"

	conMaps := []map[string]interface{}{}
	conMap := map[string]interface{}{}
	conMap["type"] = "TEXT"
	conMap["content"] = msg
	conMaps = append(conMaps, conMap)

	bodyMap := map[string]interface{}{}
	bodyMap["body"] = conMaps

	messageMap := map[string]interface{}{}
	messageMap["message"] = bodyMap

	bodyJSON, _ := json.Marshal(messageMap)

	for i := 0; i < 3; i++ {
		_, err := httpclient.Post(url, headerMap, 1000, 3000, string(bodyJSON))
		if err == nil {
			break
		}
	}
}

func inCodeList() bool {
	repoName, err := utils.GetProjectName()
	if err != nil {
		return false
	}

	for _, whiteListRepoName := range codeList {
		if repoName == whiteListRepoName {
			return true
		}
	}
	return false
}
