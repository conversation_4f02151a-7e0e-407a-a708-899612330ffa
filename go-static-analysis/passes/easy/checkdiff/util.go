package checkdiff

import (
	"encoding/json"
	"errors"
	"fmt"
	"os/exec"
	"strings"

	"github.com/mitchellh/mapstructure"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const NeedCheckDiff = "平台修改本地update会更新此文件"

type EasyCode struct {
	Code        string `json:"code"`
	FileName    string `json:"fileName"`
	IsHotReload int    `json:"isHotReload"`
	Key         string `json:"key"`
	Path        string `json:"path"`
	Rewrite     bool   `json:"rewrite"`
	Type        int    `json:"type"`
}

func GetProjectName() (string, error) {
	output, err := execScript(`git remote -v | head -1 | awk {'print $2'}`)
	if err != nil {
		return "", err
	}
	i := strings.Index(output, "8235/baidu")
	return output[i+5:], nil
}

func GetRemoteMsg(projectName string) (map[string]EasyCode, error) {
	url := fmt.Sprintf("http://************:8089/code/loadCode?icode=%s", projectName)

	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	body := make(map[string]string)
	bodyJSON, err := json.Marshal(body)
	_ = bodyJSON
	if err != nil {
		return nil, err
	}

	var resp httpclient.HttpResponse
	for i := 0; i < 3; i++ {
		resp, err = httpclient.Post(url, header, 1000, 3000, string(bodyJSON))
		if err == nil {
			break
		}
	}

	if err != nil {
		golog.Error("post failed")
		return nil, err
	}

	respMap := make(map[string]interface{})
	err = json.Unmarshal(resp.Body, &respMap)
	if err != nil {
		golog.Error("unmarshal failed")
		return nil, err
	}

	data, ok := respMap["data"].(map[string]interface{})
	if !ok {
		fmt.Println("data not ok")
		return nil, errors.New("data is not map[string]interface{}")
	}

	res := make(map[string]EasyCode)
	for _, valueSlice := range data {
		v, ok := valueSlice.([]interface{})
		if !ok {
			fmt.Println("not ok")
			continue
		}

		for _, item := range v {
			ec := EasyCode{}
			if err := mapstructure.Decode(item, &ec); err != nil {
				golog.Error("mapstructure decode failed")
				continue
			}

			key := fmt.Sprintf("%s/%s", ec.Path, ec.FileName)
			res[key] = ec
		}
	}
	return res, nil
}

func execScript(script string, args ...string) (string, error) {
	cmd := exec.Command("/bin/bash", append([]string{"-s", "-"}, args...)...)
	wc, err := cmd.StdinPipe()
	if err != nil {
		return "", err
	}

	go func() {
		defer wc.Close()
		fmt.Fprintf(wc, "%s", script)
	}()

	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

func Diff() {}
