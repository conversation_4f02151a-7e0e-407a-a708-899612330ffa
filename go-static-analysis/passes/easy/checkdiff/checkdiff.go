package checkdiff

import (
	"fmt"
	"go/ast"
	"go/format"
	"go/token"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"golang.org/x/tools/go/analysis"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

type Runner struct {
	once          sync.Once
	onceExecError error

	ProjectRoot string
	RemoteMsg   map[string]EasyCode
}

func NewRunner() *Runner {
	return &Runner{
		RemoteMsg: make(map[string]EasyCode),
	}
}

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:  "checkdiff",
		Name: "checkdiff",
		Run:  NewRunner().run,
	}
}

func (runner *Runner) run(pass *analysis.Pass) (interface{}, error) {
	runner.once.Do(func() {
		runner.onceExec()
		if runner.onceExecError != nil {
			// sendHi(todo)
			fmt.Println("once exec err: ", runner.onceExecError.Error())
		}
	})

	if runner.onceExecError != nil {
		return nil, nil
	}

	for _, file := range pass.Files {
		if len(file.Comments) == 0 {
			continue
		}

		comment := file.Doc.Text()
		if !strings.Contains(comment, NeedCheckDiff) {
			continue
		}

		if err := runner.doRule(pass, file); err != nil {
			utils.SendWarnMessageToHiForDailyMessage("do easy-diff-rule err, file name is " + err.Error())
		}
	}

	return nil, nil
}

func (runner *Runner) doRule(pass *analysis.Pass, file *ast.File) error {
	asbFileName := pass.Fset.Position(file.Pos()).Filename
	fileName, err := filepath.Rel(runner.ProjectRoot, asbFileName)
	if err != nil {
		return err
	}

	easyCode, ok := runner.RemoteMsg[fileName]
	if !ok {
		return fmt.Errorf("the file is not in remote file, file name is :%s", fileName)
	}

	localFileBytes, err := os.ReadFile(fileName)
	if err != nil {
		return err
	}
	localFileBytes, err = format.Source(localFileBytes)
	if err != nil {
		return err
	}

	remoteFile := easyCode.Code
	remoteFileBytes, err := format.Source([]byte(remoteFile))
	if err != nil {
		return err
	}

	localFile := strings.TrimSpace(string(localFileBytes))
	remoteFile = strings.TrimSpace(string(remoteFileBytes))

	if localFile != remoteFile {
		issue := diff.NewIssue(pass.Fset, file.Pos(), "该文件和 easy 文件不一致")
		pass.Report(*issue.Diagnostic)

		// 用于分析是否误判
		utils.SendWarnMessageToHiForDailyMessage("match the diff easy-rule, filename is" + fileName)
		localFileName := fileName + "-local"
		remoteFileName := fileName + "-remote"
		if err := os.WriteFile(localFileName, []byte(localFile), 0666); err != nil {
			golog.Error("file is %s, err is %v", localFileName, err)
			return err
		}

		if err := os.WriteFile(remoteFileName, remoteFileBytes, 0666); err != nil {
			golog.Error("file is %s, err is %v", remoteFileName, err)
			return err
		}
	}

	return nil
}

func (runner *Runner) onceExec() {
	// 获取项目根目录
	pwd, err := os.Getwd()
	if err != nil {
		runner.onceExecError = err
		return
	}
	runner.ProjectRoot = pwd

	// 通过接口获取远程代码
	projectName, err := GetProjectName()
	if err != nil {
		runner.onceExecError = err
		return
	}

	easyCodeMap, err := GetRemoteMsg(projectName)
	if err != nil {
		runner.onceExecError = err
		return
	}
	runner.RemoteMsg = easyCodeMap
}

func CompareASTFile(pass *analysis.Pass, localFile *ast.File, remoteFile *ast.File, remoteFset *token.FileSet, fileName string) error {
	remoteFd, err := os.OpenFile(fileName+"-ast-remote", os.O_TRUNC|os.O_CREATE|os.O_RDWR, 0666)
	if err != nil {
		return err
	}
	defer remoteFd.Close()
	if err := ast.Fprint(remoteFd, remoteFset, remoteFile, nil); err != nil {
		return err
	}

	localFd, err := os.OpenFile(fileName+"-ast-local", os.O_TRUNC|os.O_CREATE|os.O_RDWR, 0666)
	if err != nil {
		return err
	}
	defer localFd.Close()

	if err := ast.Fprint(localFd, pass.Fset, localFile, nil); err != nil {
		return err
	}

	vi := NewVistorImpl("vistor")
	Walk(pass, vi, localFile, remoteFile)
	return nil
}
