// Copyright 2009 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package checkdiff

import (
	"fmt"
	"go/ast"
	"go/token"
	"reflect"

	"golang.org/x/tools/go/analysis"
)

type Visitor interface {
	Visit(pass *analysis.Pass, node1 ast.Node, node2 ast.Node) (w Visitor)
}

type VisitorImpl struct {
	Err error
	S   string
}

func NewVistorImpl(s string) *VisitorImpl {
	return &VisitorImpl{S: s}
}

func (vt *VisitorImpl) Visit(pass *analysis.Pass, node1 ast.Node, node2 ast.Node) (w Visitor) {
	return vt
}

// Helper functions for common node lists. They may be empty.
func walkIdentList(localPass *analysis.Pass, v Visitor, localList []*ast.Ident, remoteList []*ast.Ident) {
	if len(localList) != len(remoteList) {
		localPass.Reportf(token.NoPos, "walkIdentList len(localList) != len(remoteList)")
		return
	}

	for i := 0; i < len(localList); i++ {
		localItem := localList[i]
		remoteItem := remoteList[i]
		Walk(localPass, v, localItem, remoteItem)
	}
}

func walkExprList(localPass *analysis.Pass, v Visitor, localList []ast.Expr, remoteList []ast.Expr) {
	localListLen := len(localList)
	remoteListLen := len(remoteList)
	if localListLen != remoteListLen {
		if localListLen != 0 {
			localPass.Reportf(localList[0].Pos(), " len(localList) != len(remoteList), lenpre: %d, lennext: %d", localListLen, remoteListLen)
		} else {
			localPass.Reportf(token.NoPos, " len(localList) != len(remoteList), lenpre: %d, lennext: %d", localListLen, remoteListLen)
		}
		return
	}

	for i := 0; i < len(localList); i++ {
		localItem := localList[i]
		remoteItem := remoteList[i]
		Walk(localPass, v, localItem, remoteItem)
	}
}

func walkStmtList(localPass *analysis.Pass, v Visitor, localList []ast.Stmt, remoteList []ast.Stmt) {
	localListLen := len(localList)
	remoteListLen := len(remoteList)
	if localListLen != remoteListLen {
		if localListLen != 0 {
			localPass.Reportf(localList[0].Pos(), " len(localList) != len(remoteList), lenpre: %d, lennext: %d", localListLen, remoteListLen)
		} else {
			localPass.Reportf(token.NoPos, " len(localList) != len(remoteList), lenpre: %d, lennext: %d", localListLen, remoteListLen)
		}
		return
	}

	for i := 0; i < len(localList); i++ {
		localItem := localList[i]
		remoteItem := remoteList[i]
		Walk(localPass, v, localItem, remoteItem)
	}
}

func walkDeclList(localPass *analysis.Pass, v Visitor, localList []ast.Decl, remoteList []ast.Decl) {
	localListLen := len(localList)
	remoteListLen := len(remoteList)
	if localListLen != remoteListLen {
		if localListLen != 0 {
			localPass.Reportf(localList[0].Pos(), " len(localList) != len(remoteList), lenpre: %d, lennext: %d", localListLen, remoteListLen)
		} else {
			localPass.Reportf(token.NoPos, " len(localList) != len(remoteList), lenpre: %d, lennext: %d", localListLen, remoteListLen)
		}
		return
	}

	for i := 0; i < len(localList); i++ {
		localItem := localList[i]
		remoteItem := remoteList[i]
		Walk(localPass, v, localItem, remoteItem)
	}
}

func Walk(pass *analysis.Pass, v Visitor, node1 ast.Node, node2 ast.Node) {
	if v = v.Visit(pass, node1, node2); v == nil {
		return
	}

	node2Type := reflect.TypeOf(node2)

	// walk children
	// (the order of the cases matches the order
	// of the corresponding node types in ast.go)
	switch n := node1.(type) {
	// Comments and fields
	case *ast.Comment:
		// nothing to do

	case *ast.CommentGroup:
		/*
			for _, c := range n.List {
				Walk(v, c)
			}
		*/

	case *ast.Field:
		if n.Doc != nil {
			/*Walk(v, n.Doc)*/
		}

		n2, ok := node2.(*ast.Field)
		if !ok {
			pass.Reportf(n.Pos(), "该节点处与 easy 文件不相同")
			return
		}

		walkIdentList(pass, v, n.Names, n2.Names)
		if n.Type != nil && n2.Type != nil {
			Walk(pass, v, n.Type, n2.Type)
		} else if (n.Type != nil && n2.Type == nil) || (n.Type == nil && n2.Type != nil) {
			pass.Reportf(n.Pos(), "该节点处与 easy 文件不相同")
		}

		if n.Tag != nil && n2.Tag != nil {
			Walk(pass, v, n.Tag, n2.Tag)
		} else if (n.Tag != nil && n2.Tag == nil) || (n.Tag == nil && n2.Tag != nil) {
			pass.Reportf(n.Pos(), "该节点处与 easy 文件不相同")
		}
		/*
			if n.Comment != nil {
				Walk(v, n.Comment)
			}
		*/

	case *ast.FieldList:
		n2, ok := node2.(*ast.FieldList)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go158")
			return
		}

		if len(n.List) != len(n2.List) {
			pass.Reportf(n.Pos(), "该节点处与 easy 文件不相同, work.go141")
		}

		lenCnt := len(n.List)
		for i := 0; i < lenCnt; i++ {
			subNode1 := n.List[i]
			subNode2 := n.List[i]
			Walk(pass, v, subNode1, subNode2)
		}

	// Expressions
	case *ast.BadExpr:
		pass.Reportf(n.Pos(), "this is a bad expr")

	case *ast.Ident:
		n2, ok := node2.(*ast.Ident)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go192")
			return
		}

		if n == nil {
			pass.Reportf(n.Pos(), "不同， walk.go198")
			return
		}
		if n2 == nil {
			pass.Reportf(n.Pos(), "不同， walk.go202")
			return
		}

		if n.Name != n2.Name {
			pass.Reportf(n.Pos(), "n.Name: %s, n2.Name: %s", n.Name, n2.Name)
		}

	case *ast.BasicLit:
		n2, ok := node2.(*ast.BasicLit)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go192")
			return
		}

		if n.Value != n2.Value {
			pass.Reportf(n.Pos(), "n.Value: %s, n2.Value: %s", n.Value, n2.Value)
		}
	case *ast.Ellipsis:
		n2, ok := node2.(*ast.Ellipsis)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go158")
			return
		}
		if n.Elt != nil && n2.Elt != nil {
			Walk(pass, v, n.Elt, n2.Elt)
		}

	case *ast.FuncLit:
		n2, ok := node2.(*ast.FuncLit)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go167")
			return
		}
		Walk(pass, v, n.Type, n2.Type)
		Walk(pass, v, n.Body, n2.Type)

	case *ast.CompositeLit:
		n2, ok := node2.(*ast.CompositeLit)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go175")
			return
		}
		if n.Type != nil && n2.Type != nil {
			Walk(pass, v, n.Type, n2.Type)
		}
		walkExprList(pass, v, n.Elts, n2.Elts)

	case *ast.ParenExpr:
		n2, ok := node2.(*ast.ParenExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go206")
			return
		}
		Walk(pass, v, n.X, n2.X)

	case *ast.SelectorExpr:
		n2, ok := node2.(*ast.SelectorExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go215, node2type: %s, ", node2Type)
			return
		}
		Walk(pass, v, n.X, n2.X)
		Walk(pass, v, n.Sel, n2.Sel)

		// Walk(v, n.Sel)

	case *ast.IndexExpr:
		n2, ok := node2.(*ast.IndexExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go202")
			return
		}
		Walk(pass, v, n.X, n2.X)
		Walk(pass, v, n.Index, n2.Index)

	case *ast.IndexListExpr:
		n2, ok := node2.(*ast.IndexListExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同， walk.go185")
			return
		}
		Walk(pass, v, n.X, n2.X)

		if len(n.Indices) != len(n2.Indices) {
			pass.Reportf(n.Pos(), "不同， walk.go185")
			return
		}

		lenCnt := len(n.Indices)
		for i := 0; i < lenCnt; i++ {
			index1 := n.Indices[i]
			index2 := n2.Indices[i]
			Walk(pass, v, index1, index2)
		}

	case *ast.SliceExpr:
		n2, ok := node2.(*ast.SliceExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go259")
			return
		}

		Walk(pass, v, n.X, n2.X)

		if n.Low != nil && n2.Low != nil {
			Walk(pass, v, n.Low, n2.Low)
		} else if n.Low != nil || n2.Low != nil {
			pass.Reportf(n.Pos(), "不同, walk.go236")
		}
		if n.High != nil && n2.High != nil {
			Walk(pass, v, n.High, n2.High)
		} else if n.High != nil || n2.High != nil {
			pass.Reportf(n.Pos(), "不同, walk.go240")
		}

		if n.Max != nil && n2.Max != nil {
			Walk(pass, v, n.Max, n2.Max)
		} else if n.Max != nil || n2.Max != nil {
			pass.Reportf(n.Pos(), "不同, walk.go245")
			return
		}

	case *ast.TypeAssertExpr:
		n2, ok := node2.(*ast.TypeAssertExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go251")
			return
		}

		Walk(pass, v, n.X, n2)
		if n.Type != nil && n2.Type != nil {
			Walk(pass, v, n.Type, n2.Type)
			return
		}

	case *ast.CallExpr:
		n2, ok := node2.(*ast.CallExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go305")
			return
		}

		Walk(pass, v, n.Fun, n2.Fun)
		walkExprList(pass, v, n.Args, n2.Args)

	case *ast.StarExpr:
		n2, ok := node2.(*ast.StarExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go315, n2type:%v ", reflect.TypeOf(node2))
			return
		}
		Walk(pass, v, n.X, n2.X)

	case *ast.UnaryExpr:
		n2, ok := node2.(*ast.UnaryExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go325")
			return
		}
		Walk(pass, v, n.X, n2.X)

	case *ast.BinaryExpr:
		n2, ok := node2.(*ast.BinaryExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go262")
			return
		}
		Walk(pass, v, n.X, n2.X)
		Walk(pass, v, n.Y, n2.Y)

	case *ast.KeyValueExpr:
		n2, ok := node2.(*ast.KeyValueExpr)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go344")
			return
		}

		Walk(pass, v, n.Key, n2.Key)
		Walk(pass, v, n.Value, n2.Value)

	// Types
	case *ast.ArrayType:
		n2, ok := node2.(*ast.ArrayType)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go355")
			return
		}
		if n.Len != nil && n2.Len != nil {
			Walk(pass, v, n.Len, n2.Len)
		}
		Walk(pass, v, n.Elt, n2.Elt)

	case *ast.StructType:
		n2, ok := node2.(*ast.StructType)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go367")
			return
		}

		Walk(pass, v, n.Fields, n2.Fields)

	case *ast.FuncType:
		n2, ok := node2.(*ast.FuncType)
		if !ok {
			pass.Reportf(n.Pos(), "不同, walk.go376")
			return
		}

		if n.TypeParams != nil && n2.TypeParams != nil {
			Walk(pass, v, n.TypeParams, n2.TypeParams)
		} else if n.TypeParams != nil || n2.TypeParams != nil {
			pass.Reportf(n.Pos(), "不同，walk.go367")
		}
		if n.Params != nil && n2.Params != nil {
			Walk(pass, v, n.Params, n2.Params)
		} else if n.Params != nil || n2.Params != nil {
			pass.Reportf(n.Pos(), "不同，walk.go372")
		}

		if n.Results != nil && n2.Results != nil {
			Walk(pass, v, n.Results, n2.Results)
		} else if n.Results != nil || n2.Results != nil {
			pass.Reportf(n.Pos(), "不同，walk.go378")
		}

	case *ast.InterfaceType:
		n2, ok := node2.(*ast.InterfaceType)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go384")
			return
		}
		Walk(pass, v, n.Methods, n2.Methods)

	case *ast.MapType:
		n2, ok := node2.(*ast.MapType)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go384")
			return
		}

		Walk(pass, v, n.Key, n2.Key)
		Walk(pass, v, n.Value, n2.Value)

	case *ast.ChanType:
		n2, ok := node2.(*ast.ChanType)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go384")
			return
		}
		Walk(pass, v, n.Value, n2.Value)

	// Statements
	case *ast.BadStmt:
		// nothing to do

	case *ast.DeclStmt:
		n2, ok := node2.(*ast.DeclStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go384")
			return
		}

		Walk(pass, v, n.Decl, n2.Decl)

	case *ast.EmptyStmt:
		// nothing to do

	case *ast.LabeledStmt:
		n2, ok := node2.(*ast.LabeledStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go426")
			return
		}

		Walk(pass, v, n.Label, n2.Label)
		Walk(pass, v, n.Stmt, n2.Stmt)

	case *ast.ExprStmt:
		n2, ok := node2.(*ast.ExprStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go426")
			return
		}
		Walk(pass, v, n.X, n2.X)

	case *ast.SendStmt:
		n2, ok := node2.(*ast.SendStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go426")
			return
		}
		Walk(pass, v, n.Chan, n2.Chan)
		Walk(pass, v, n.Value, n2.Chan)

	case *ast.IncDecStmt:
		n2, ok := node2.(*ast.IncDecStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go426")
			return
		}
		Walk(pass, v, n.X, n2.X)

	case *ast.AssignStmt:
		n2, ok := node2.(*ast.AssignStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go426")
			return
		}
		walkExprList(pass, v, n.Lhs, n2.Lhs)
		walkExprList(pass, v, n.Rhs, n2.Rhs)

	case *ast.GoStmt:
		n2, ok := node2.(*ast.GoStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go426")
			return
		}

		Walk(pass, v, n.Call, n2.Call)

	case *ast.DeferStmt:
		n2, ok := node2.(*ast.DeferStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go426")
			return
		}
		Walk(pass, v, n.Call, n2.Call)

	case *ast.ReturnStmt:
		n2, ok := node2.(*ast.ReturnStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go426")
			return
		}
		walkExprList(pass, v, n.Results, n2.Results)

	case *ast.BranchStmt:
		n2, ok := node2.(*ast.BranchStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go495")
			return
		}
		if n.Label != nil && n2.Label != nil {
			Walk(pass, v, n.Label, n2.Label)
		} else if n.Label != nil || n2.Label != nil {
			pass.Reportf(n.Pos(), "不同，walk.go501")
		}

	case *ast.BlockStmt:
		n2, ok := node2.(*ast.BlockStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go495")
			return
		}
		walkStmtList(pass, v, n.List, n2.List)

	case *ast.IfStmt:
		n2, ok := node2.(*ast.IfStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go495")
			return
		}
		if n.Init != nil && n2.Init != nil {
			Walk(pass, v, n.Init, n2.Init)
		}
		Walk(pass, v, n.Cond, n2.Cond)
		Walk(pass, v, n.Body, n2.Body)
		if n.Else != nil && n2.Else != nil {
			Walk(pass, v, n.Else, n2.Else)
		}
	case *ast.CaseClause:
		n2, ok := node2.(*ast.CaseClause)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go495")
			return
		}
		walkExprList(pass, v, n.List, n2.List)
		walkStmtList(pass, v, n.Body, n2.Body)

	case *ast.SwitchStmt:
		n2, ok := node2.(*ast.SwitchStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go538")
			return
		}
		if n.Init != nil && n2.Init != nil {
			Walk(pass, v, n.Init, n2.Init)
		} else if n.Init != nil || n2.Init != nil {
			pass.Reportf(n.Pos(), "不同，walk.go544")
		}
		if n.Tag != nil && n2.Tag != nil {
			Walk(pass, v, n.Tag, n2.Tag)
		}
		Walk(pass, v, n.Body, n2.Body)

	case *ast.TypeSwitchStmt:
		n2, ok := node2.(*ast.TypeSwitchStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go538")
			return
		}
		if n.Init != nil && n2.Init != nil {
			Walk(pass, v, n.Init, n2.Init)
		}
		Walk(pass, v, n.Assign, n2.Assign)
		Walk(pass, v, n.Body, n2.Body)

	case *ast.CommClause:
		n2, ok := node2.(*ast.CommClause)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go538")
			return
		}
		if n.Comm != nil && n2.Comm != nil {
			Walk(pass, v, n.Comm, n2.Comm)
		}
		walkStmtList(pass, v, n.Body, n2.Body)

	case *ast.SelectStmt:
		n2, ok := node2.(*ast.SelectStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go538")
			return
		}
		Walk(pass, v, n.Body, n2.Body)

	case *ast.ForStmt:
		n2, ok := node2.(*ast.ForStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go538")
			return
		}
		if n.Init != nil && n2.Init != nil {
			Walk(pass, v, n.Init, n2.Init)
		} else if n.Init != nil || n2.Init != nil {
			pass.Reportf(n.Pos(), "walk.go591")
		}
		if n.Cond != nil && n2.Cond != nil {
			Walk(pass, v, n.Cond, n2.Cond)
		} else if n.Cond != nil || n2.Cond != nil {
			pass.Reportf(n.Pos(), "walk.go596")
		}
		if n.Post != nil && n2.Cond != nil {
			Walk(pass, v, n.Post, n2.Post)
		} else if n.Post != nil || n2.Cond != nil {
			pass.Reportf(n.Pos(), "walk.go596")
		}
		Walk(pass, v, n.Body, n2.Body)

	case *ast.RangeStmt:
		n2, ok := node2.(*ast.RangeStmt)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go608")
			return
		}

		if n.Key != nil && n2.Key != nil {
			Walk(pass, v, n.Key, n2.Key)
		} else if n.Key != nil || n2.Key != nil {
			pass.Reportf(n.Pos(), "不同，walk.go615")
		}
		if n.Value != nil && n2.Value != nil {
			Walk(pass, v, n.Value, n2.Value)
		} else if n.Value != nil || n2.Value != nil {
			pass.Reportf(n.Pos(), "不同，walk.go620")
		}
		Walk(pass, v, n.X, n2.X)
		Walk(pass, v, n.Body, n2.Body)

	// Declarations
	case *ast.ImportSpec:
		n2, ok := node2.(*ast.ImportSpec)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go630")
			return
		}

		if n.Doc != nil && n2.Doc != nil {
			Walk(pass, v, n.Doc, n2.Doc)
		}

		if n.Name != nil && n2.Name != nil {
			Walk(pass, v, n.Name, n2.Name)
		}

		Walk(pass, v, n.Path, n2.Path)
		if n.Comment != nil && n2.Comment != nil {
			Walk(pass, v, n.Comment, n2.Comment)
		}

	case *ast.ValueSpec:
		n2, ok := node2.(*ast.ValueSpec)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go630")
			return
		}

		if n.Doc != nil && n2.Doc != nil {
			Walk(pass, v, n.Doc, n2.Doc)
		}
		walkIdentList(pass, v, n.Names, n2.Names)
		if n.Type != nil && n2.Type != nil {
			Walk(pass, v, n.Type, n2.Type)
		} else if n.Type != nil || n2.Type != nil {
			pass.Reportf(n.Pos(), "不同，walk.go659")
		}
		walkExprList(pass, v, n.Values, n2.Values)
		if n.Comment != nil && n2.Comment != nil {
			Walk(pass, v, n.Comment, n2.Comment)
		}

	case *ast.TypeSpec:
		n2, ok := node2.(*ast.TypeSpec)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go630")
			return
		}
		if n.Doc != nil && n2.Doc != nil {
			Walk(pass, v, n.Doc, n2.Doc)
		} else if n.Doc != nil || n2.Doc != nil {
			pass.Reportf(n.Pos(), "不同，walk.go676")
		}
		Walk(pass, v, n.Name, n2.Name)
		if n.TypeParams != nil && n2.TypeParams != nil {
			Walk(pass, v, n.TypeParams, n2.TypeParams)
		} else if n.TypeParams != nil || n2.TypeParams != nil {
			pass.Reportf(n.Pos(), "不同，walk.go682")
		}
		Walk(pass, v, n.Type, n2.Type)
		if n.Comment != nil && n2.Comment != nil {
			Walk(pass, v, n.Comment, n2.Comment)
		} else if n.Comment != nil || n2.Comment != nil {
			pass.Reportf(n.Pos(), "不同，walk.go689")
		}

	case *ast.BadDecl:
		// nothing to do

	case *ast.GenDecl:
		n2, ok := node2.(*ast.GenDecl)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go630")
			return
		}
		if n.Doc != nil && n2.Doc != nil {
			Walk(pass, v, n.Doc, n2.Doc)
		}

		if len(n.Specs) != len(n2.Specs) {
			pass.Reportf(n.Pos(), "不同，walk.go706")
			return
		}
		for i := 0; i < len(n.Specs); i++ {
			subNode1 := n.Specs[i]
			subNode2 := n2.Specs[i]
			Walk(pass, v, subNode1, subNode2)
		}

	case *ast.FuncDecl:
		n2, ok := node2.(*ast.FuncDecl)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go630")
			return
		}
		if n.Doc != nil && n2.Doc != nil {
			Walk(pass, v, n.Doc, n2.Doc)
		}

		if n.Recv != nil && n2.Recv != nil {
			Walk(pass, v, n.Recv, n2.Recv)
		} else if n.Recv != nil || n2.Recv != nil {
			pass.Reportf(n.Pos(), "不同，walk.go747")
		}
		Walk(pass, v, n.Name, n2.Name)
		Walk(pass, v, n.Type, n2.Type)
		if n.Body != nil && n2.Body != nil {
			Walk(pass, v, n.Body, n2.Body)
		} else if n.Body != nil || n2.Body != nil {
			pass.Reportf(n.Pos(), "不同，walk.go738")
		}

	// Files and packages
	case *ast.File:
		n2, ok := node2.(*ast.File)
		if !ok {
			pass.Reportf(n.Pos(), "不同，walk.go745")
			return
		}

		if n.Doc != nil || n2.Doc != nil {
			Walk(pass, v, n.Doc, n2.Doc)
		}
		Walk(pass, v, n.Name, n2.Name)
		walkDeclList(pass, v, n.Decls, n2.Decls)

	case *ast.Package:

	default:
		panic(fmt.Sprintf("ast.Walk: unexpected node type %T", n))
	}
}
