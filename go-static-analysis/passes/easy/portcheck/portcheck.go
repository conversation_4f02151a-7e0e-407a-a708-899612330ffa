package portcheck

import (
	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/buildssa"
	"golang.org/x/tools/go/ssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
)

var ForbidFuncMap = map[string]struct{}{
	// net/http
	"net/http.ListenAndServe":              {},
	"net/http.ListenAndServeTLS":           {},
	"net/http.Serve":                       {},
	"net/http.NewServeMux":                 {},
	"(*net/http.ServeMux).ServeHTTP":       {},
	"(*net/http.Server).ListenAndServe":    {},
	"(*net/http.Server).ListenAndServeTLS": {},
	"(*net/http.Server).Serve":             {},

	// net
	"net.Listen":    {},
	"net.ListenUDP": {},
	"net.ListenTCP": {},

	// 自定义
	"(*icode.baidu.com/baidu/netdisk/pcs-go-lib/framework.Framework).Run":      {},
	"(*icode.baidu.com/baidu/netdisk/pcs-go-lib/framework.Framework).RunHttps": {},
}

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:      "portchecker",
		Name:     "portchecker",
		Requires: []*analysis.Analyzer{buildssa.Analyzer},
		Run:      run,
	}
}

func run(pass *analysis.Pass) (interface{}, error) {
	bs := pass.ResultOf[buildssa.Analyzer].(*buildssa.SSA)

	funcs := bs.SrcFuncs
	for _, fn := range funcs {
		doRule(pass, fn)
	}

	return nil, nil
}

func doRule(pass *analysis.Pass, fn *ssa.Function) {
	for _, block := range fn.Blocks {
		for _, instr := range block.Instrs {
			switch instr := instr.(type) {
			case ssa.CallInstruction:
				comm := instr.Common()
				f := comm.StaticCallee()
				if f == nil {
					continue
				}

				if _, ok := ForbidFuncMap[f.String()]; ok {
					issue := diff.NewIssue(pass.Fset, instr.Pos(), "easy项目不允许自定义端口。因此不允许调用以下函数: %s", f.String())
					if diff.Differ.IssueNeedReport(issue) {
						pass.Reportf(instr.Pos(), "easy项目不允许自定义端口。因此不允许调用以下函数: %s", f.String())
					}
				}
			}
		}
	}
}
