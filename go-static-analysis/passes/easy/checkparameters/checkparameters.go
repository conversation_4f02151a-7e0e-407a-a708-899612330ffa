package checkparameters

import (
	"encoding/json"
	"go/ast"
	"strconv"
	"sync"

	"golang.org/x/tools/go/analysis"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/diff"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

const maxParameters = 5
const reportURL = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dccbcbe7914e2c08351def6bde9226d18"

var codeList = []string{
	"baidu/netdisk/nd-membership-order-v3",
	"baidu/netdisk/nd-membership-proxy-v3",
	"baidu/netdisk/nd-membership-user-v3",
	"baidu/netdisk/nd-membership-product-v3",
}

var onceDo sync.Once
var isCode bool

func NewAnalyzer() *analysis.Analyzer {
	return &analysis.Analyzer{
		Doc:  "checkparameters",
		Name: "checkparameters",
		Run:  run,
	}
}

func run(pass *analysis.Pass) (interface{}, error) {
	onceDo.Do(func() {
		isCode = inCodeList()
	})

	if !isCode {
		return nil, nil
	}

	for _, file := range pass.Files {
		if ok := file.Pos().IsValid(); !ok {
			continue
		}

		ast.Inspect(file, func(n ast.Node) bool {
			fn, ok := n.(*ast.FuncDecl)
			if !ok {
				return true
			}

			// Check parameters
			if len(fn.Type.Params.List) > maxParameters {
				issue := diff.NewIssue(pass.Fset, fn.Pos(), "Has more than %d parameters", maxParameters)
				SendWarnMessageToHi(reportURL, issue.Fset.File(fn.Pos()).Name()+":"+strconv.FormatInt(int64(issue.Line()), 10))
				// pass.Report(*issue.Diagnostic)
				return false
			}

			// Check return values
			if fn.Type.Results != nil && len(fn.Type.Results.List) > maxParameters {
				issue := diff.NewIssue(pass.Fset, fn.Pos(), "Has more than %d return values", maxParameters)
				SendWarnMessageToHi(reportURL, issue.Fset.File(fn.Pos()).Name()+":"+strconv.FormatInt(int64(issue.Line()), 10))
				// pass.Report(*issue.Diagnostic)
				return false
			}

			return true
		})
	}

	return nil, nil
}

func SendWarnMessageToHi(url, msg string) {
	headerMap := map[string]string{}
	headerMap["Content-Type"] = "application/json"

	conMaps := []map[string]interface{}{}
	conMap := map[string]interface{}{}
	conMap["type"] = "TEXT"
	conMap["content"] = msg
	conMaps = append(conMaps, conMap)

	bodyMap := map[string]interface{}{}
	bodyMap["body"] = conMaps

	messageMap := map[string]interface{}{}
	messageMap["message"] = bodyMap

	bodyJSON, _ := json.Marshal(messageMap)

	for i := 0; i < 3; i++ {
		_, err := httpclient.Post(url, headerMap, 1000, 3000, string(bodyJSON))
		if err == nil {
			break
		}
	}
}

func inCodeList() bool {
	repoName, err := utils.GetProjectName()
	if err != nil {
		return false
	}

	for _, whiteListRepoName := range codeList {
		if repoName == whiteListRepoName {
			return true
		}
	}
	return false
}
