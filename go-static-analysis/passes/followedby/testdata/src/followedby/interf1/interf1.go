package interf1

type Dummy struct {
}

type File struct {
}

type Processor interface {
	Process(a *File, b File) (*File, File)
	Clean(dummy1 *Dummy, a *File, b File, c *File, d File, dummy2 Dummy)
}

type TheProcessor struct {
}

func (p *TheProcessor) Process(a *File, b File) (*File, File) {
	return &File{}, File{}
}

func (p *TheProcessor) Clean(dummy1 *Dummy, a *File, b File, c *File, d File, dummy2 Dummy) {
}

func Test1(p Processor, a *File, b File) {
	c, d := p.Process(a, b)
	p.Clean(&Dummy{}, a, b, c, d, Dummy{})
}

func Test2(p Processor, a *File, b File) {
	c, _ := p.Process(a, b) // want `missing "\(followedby/interf1\.Processor\)\.Clean"`
	p.Clean(&Dummy{}, a, b, c, *c, Dummy{})
}

func Test3(p Processor, a *File, b File) {
	c, d := p.Process(a, b)
	func() {
		p.Clean(&Dummy{}, a, b, c, d, Dummy{})
	}()
}

func Test4(p Processor, a *File, b File) {
	c, _ := p.Process(a, b) // want `missing "\(followedby/interf1\.Processor\)\.Clean"`
	func() {
		p.Clean(&Dummy{}, a, b, c, *c, Dummy{})
	}()
}

func Test5(p Processor, a *File, b File) {
	c, d := p.Process(a, b)
	func() {
		func() {
			p.Clean(&Dummy{}, a, b, c, d, Dummy{})
		}()
	}()
}

func Test6(p Processor, a *File, b File) {
	c, _ := p.Process(a, b) // want `missing "\(followedby/interf1\.Processor\)\.Clean"`
	func() {
		func() {
			p.Clean(&Dummy{}, a, b, c, *c, Dummy{})
		}()
	}()
}
