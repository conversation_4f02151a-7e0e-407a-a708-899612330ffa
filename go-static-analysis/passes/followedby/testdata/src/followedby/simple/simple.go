package simple

func open(x int) {
}

func close(x int) {
}

func Test1() {
	open(1)
	close(1)
}

func Test2() {
	open(1) // want `missing "followedby/simple.close"`
	close(2)
}

func Test3() {
	open(1)
	defer close(1)
}

func Test4() {
	open(1) // want `missing "followedby/simple.close"`
	defer close(2)
}

func Test5() {
	open(1)
	func() {
		close(1)
	}()
}

func Test6() {
	open(1) // want `missing "followedby/simple.close"`
	func() {
		close(2)
	}()
}

func Test7() {
	open(1)
	func() {
		func() {
			close(1)
		}()
	}()
}

func Test8() {
	open(1) // want `missing "followedby/simple.close"`
	func() {
		func() {
			close(2)
		}()
	}()
}

func Test9() {
	open(1) // want `missing "followedby/simple.close"`
	func() {
		func() {
			func() {
				close(1)
			}()
		}()
	}()
}

func Test10() {
	open(1) // want `missing "followedby/simple.close"`
	func() {
		func() {
			func() {
				close(2)
			}()
		}()
	}()
}

func Test11(x, y int) {
	open(x)
	close(x)
}

func Test12(x, y int) {
	open(x) // want `missing "followedby/simple.close"`
	close(y)
}

func Test13(x, y int) {
	open(x)
	func() {
		func() {
			close(x)
		}()
	}()
}

func Test14(x, y int) {
	open(x) // want `missing "followedby/simple.close"`
	func() {
		func() {
			close(y)
		}()
	}()
}
