package simple

var G int

func Escape1() {
	open(100)
	G = 100
}

func Escape2() {
	open(100) // want `missing "followedby/simple.close"`
	G = 200
}

func Escape3() {
	open(100)
	func() {
		G = 100
	}()
}

func Escape4() {
	open(100) // want `missing "followedby/simple.close"`
	func() {
		G = 200
	}()
}

func Escape5() {
	open(100)
	func() {
		func() {
			G = 100
		}()
	}()
}

func Escape6() {
	open(100) // want `missing "followedby/simple.close"`
	func() {
		func() {
			G = 200
		}()
	}()
}

func Escape7() {
	open(100) // want `missing "followedby/simple.close"`
	func() {
		func() {
			func() {
				G = 100
			}()
		}()
	}()
}

func Escape8() int {
	open(100)
	return 100
}

func Escape9() int {
	open(100) // want `missing "followedby/simple.close"`
	return 200
}
