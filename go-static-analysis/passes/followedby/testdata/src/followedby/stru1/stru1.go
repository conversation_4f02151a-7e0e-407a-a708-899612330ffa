package stru1

type File struct {
}

func (f *File) open(a, b int) (int, int) {
	return a, b
}

func (f *File) close(a, d int) {}

func Test1() {
	f := File{}
	_, d := f.open(1, 2)
	f.close(1, d)
}

func Test2() {
	f := File{}
	_, d := f.open(1, 2) // want `missing "\(\*followedby/stru1\.File\).close"`
	f.close(2, d)
}

func Test3() {
	f := File{}
	_, d := f.open(1, 2)
	defer f.close(1, d)
}

func Test4() {
	f := File{}
	_, d := f.open(1, 2)
	func() {
		f.close(1, d)
	}()
}

func Test5(f File) {
	_, d := f.open(1, 2)
	fun := func() {
		f.close(1, d)
	}
	fun()
}

func Test6(f File) {
	_, d := f.open(1, 2) // want `missing "\(\*followedby/stru1\.File\).close"`
	fun := func() {
		f.close(2, d)
	}
	fun()
}

func Test7(f *File, a, b int) {
	_, d := f.open(a, b)
	f.close(a, d)
}

func Test8(f *File, a, b int) {
	_, d := f.open(a, b) // want `missing "\(\*followedby/stru1\.File\).close"`
	f.close(b, d)
}

func Test9(f File, a, b int) {
	_, d := f.open(a, b)
	func() {
		f.close(a, d)
	}()
}

func Test10(f File, a, b int) {
	_, d := f.open(a, b) // want `missing "\(\*followedby/stru1\.File\).close"`
	func() {
		f.close(b, d)
	}()
}

func Test11(f File, a, b int) {
	_, d := f.open(a, b)
	func() {
		func() {
			f.close(a, d)
		}()
	}()
}

func Test12(f File, a, b int) {
	_, d := f.open(a, b) // want `missing "\(\*followedby/stru1\.File\).close"`
	func() {
		func() {
			f.close(b, d)
		}()
	}()
}

func Test13(f *File, x, y int) {
	_, d := f.open(x, y)
	a := x
	y = d
	f.close(a, y)
}

func Test14(f *File, x, y int) {
	_, d := f.open(x, y)
	a := x
	y = d
	func() {
		f.close(a, y)
	}()
}

func Test15(f *File, x, y int) {
	_, d := f.open(x, y)
	a := x
	y = d
	func() {
		func() {
			f.close(a, y)
		}()
	}()
}

func Test16(f *File, a, b int) {
	c, d := f.open(a, b)
	func() {
		c = a
		b = d
		f.close(c, b)
	}()
}

func Test17(f *File, a, b int) {
	c, d := f.open(a, b) // want `missing "\(\*followedby/stru1\.File\).close"`
	func() {
		c = a
		b = d
		f.close(a, c)
	}()
}

func Test18(f File, a, b int) {
	c, d := f.open(a, b)
	func(a, b, c, d int) {
		f.close(a, d)
	}(a, b, c, d)
}

func Test19(f File, a, b int) {
	c, d := f.open(a, b) // want `missing "\(\*followedby/stru1\.File\).close"`
	func(a, b, c, d int) {
		f.close(b, d)
	}(a, b, c, d)
}
