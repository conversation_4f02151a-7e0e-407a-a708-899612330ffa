package rows

import "database/sql"

func Test1(rows *sql.Rows) {
	for rows.Next() {
	}

	err := rows.Err()
	if err != nil {
		panic(err)
	}
}

func Test2(rows *sql.Rows) {
	for rows.Next() { // want `missing "\(\*database/sql\.Rows\)\.Err"`
	}
}

func Test3(rows *sql.Rows) *sql.Rows {
	for rows.Next() { // want `missing "\(\*database/sql\.Rows\)\.Err"`
	}
	return rows
}

func Test4(rows *sql.Rows) {
	for rows.Next() {
	}
	func() {
		func() {
			err := rows.Err()
			if err != nil {
				panic(err)
			}
		}()
	}()
}

func Test5(rows1 *sql.Rows, rows2 *sql.Rows) {
	for rows1.Next() {
	}
	for rows2.Next() { // want `missing "\(\*database/sql\.Rows\)\.Err"`
	}
	func(rows *sql.Rows) {
		func() {
			err := rows.Err()
			if err != nil {
				panic(err)
			}
		}()
	}(rows1)
}
