package open

import "os"

func Test1() {
	f, err := os.Open("dummy")
	if err != nil {
		panic(err)
	}
	f.Close()
}

func Test2() {
	f, err := os.Open("dummy") // want `missing "\(\*os\.File\)\.Close"`
	if err != nil {
		panic(err)
	}
	func(*os.File) {
	}(f)
}

func Test3() {
	f, _ := os.Open("dummy")
	func(f *os.File) {
		f.Close()
	}(f)
}

func Test4() {
	f, err := os.Open("dummy")
	if err != nil {
		panic(err)
	}
	func() {
		f.<PERSON>()
	}()
}

func Test5() {
	f, err := os.Open("dummy")
	if err != nil {
		panic(err)
	}
	func() {
		func() {
			f.Close()
		}()
	}()
}

func Test6() *os.File {
	f, err := os.Open("dummy")
	if err != nil {
		panic(err)
	}
	return f
}

var GFile *os.File

func Test7() {
	f, err := os.Open("dummy")
	if err != nil {
		panic(err)
	}
	func() {
		func() {
			GFile = f
		}()
	}()
}

type Container struct {
	F *os.File
	X struct {
		F *os.File
	}
}

var GC Container

func Test8() {
	f, err := os.Open("dummy")
	if err != nil {
		panic(err)
	}
	func() {
		func() {
			GC.F = f
		}()
	}()
}

// func Test9() {
// 	f, err := os.Open("dummy")
// 	if err != nil {
// 		panic(err)
// 	}
// 	GC.X.F = f
// }

// func Test10() {
// 	f, err := os.Open("dummy")
// 	if err != nil {
// 		panic(err)
// 	}
// 	func() {
// 		func() {
// 			GC.X.F = f
// 		}()
// 	}()
// }
