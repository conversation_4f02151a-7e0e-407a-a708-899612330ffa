package multiple

func open(a, b int) (c, d int) {
	return a, b
}

func close(a, d int) {
}

func Test1() {
	_, d := open(1, 2)
	close(1, d)
}

func Test2() {
	_, d := open(1, 2) // want `missing "followedby/multiple.close"`
	close(2, d)
}

func Test3() {
	_, d := open(1, 2)
	defer close(1, d)
}

func Test4() {
	_, d := open(1, 2)
	func() {
		close(1, d)
	}()
}

func Test5() {
	_, d := open(1, 2)
	f := func() {
		close(1, d)
	}
	f()
}

func Test6() {
	_, d := open(1, 2) // want `missing "followedby/multiple.close"`
	f := func() {
		close(2, d)
	}
	f()
}

func Test7(a, b int) {
	_, d := open(a, b)
	close(a, d)
}

func Test8(a, b int) {
	_, d := open(a, b) // want `missing "followedby/multiple.close"`
	close(b, d)
}

func Test9(a, b int) {
	_, d := open(a, b)
	func() {
		close(a, d)
	}()
}

func Test10(a, b int) {
	_, d := open(a, b) // want `missing "followedby/multiple.close"`
	func() {
		close(b, d)
	}()
}

func Test11(a, b int) {
	_, d := open(a, b)
	func() {
		func() {
			close(a, d)
		}()
	}()
}

func Test12(a, b int) {
	_, d := open(a, b) // want `missing "followedby/multiple.close"`
	func() {
		func() {
			close(b, d)
		}()
	}()
}

func Test13(x, y int) {
	_, d := open(x, y)
	a := x
	y = d
	close(a, y)
}

func Test14(x, y int) {
	_, d := open(x, y)
	a := x
	y = d
	func() {
		close(a, y)
	}()
}

func Test15(x, y int) {
	_, d := open(x, y)
	a := x
	y = d
	func() {
		func() {
			close(a, y)
		}()
	}()
}

func Test16(a, b int) {
	c, d := open(a, b)
	func() {
		c = a
		b = d
		close(c, b)
	}()
}

func Test17(a, b int) {
	c, d := open(a, b) // want `missing "followedby/multiple.close"`
	func() {
		c = a
		b = d
		close(a, c)
	}()
}

func Test18(a, b int) {
	c, d := open(a, b)
	func(a, b, c, d int) {
		close(a, d)
	}(a, b, c, d)
}

func Test19(a, b int) {
	c, d := open(a, b) // want `missing "followedby/multiple.close"`
	func(a, b, c, d int) {
		close(b, d)
	}(a, b, c, d)
}
