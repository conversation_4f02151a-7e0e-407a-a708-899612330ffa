package followedby

import (
	"fmt"
	"strings"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/buildssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/followedby/config"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/followedby/function"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/followedby/rule"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

var whiteList = []string{
	"netdisk/pss-pssui-hrdsgoapi/src/golib/common/labix.org/v2/mgo/auth.go",
	"netdisk/pss-pssui-hrdsgoapi/src/golib/common/labix.org/v2/mgo/session.go",
	"netdisk/pss-pssui-hrdsgoapi/src/golib/common/labix.org/v2/mgo/socket.go",
}

var Analyzer = &analysis.Analyzer{
	Name: "followedby",
	Doc:  "check whether X is followed by Y",
	Requires: []*analysis.Analyzer{
		buildssa.Analyzer,
	},
	Run: run,
}

func getYParams(callInstr *function.CookedCallInstr, yItem *rule.YItem) []*function.Var {
	res := []*function.Var{}
	for i := 0; i < len(yItem.ParamsCooked); i += 1 {
		a := yItem.ParamsCooked[i]
		if a == [2]int{-1, -1} {
			res = append(res, nil)
			continue
		}

		if a[0] == 0 {
			res = append(res, callInstr.Params[a[1]])
			continue
		}

		v := callInstr.Results[a[1]].V
		if v == nil { // the result is not used, so this YItem can not exist
			return nil
		}

		res = append(res, v)
	}
	return res
}

func findYItem(d *function.Digest, r *rule.Rule, i int, yItem *rule.YItem) bool {
	callInstr := d.CallInstrs[i]
	yParams := getYParams(callInstr, yItem)
	if yParams == nil {
		return false
	}

	i += 1

	cfg := function.FindConfig{
		MaxDepth: r.MaxDepth,
		Escape:   r.Escape,
	}
	return d.FindCall(cfg, i, yItem.Fn, yParams)
}

func doRule(pass *analysis.Pass, d *function.Digest, r *rule.Rule) {
	for i := 0; i < len(d.CallInstrs); i += 1 {
		callInstr := d.CallInstrs[i]
		// find X
		if callInstr.Name == r.X.Fn && len(callInstr.Params) == len(r.X.Params) && len(callInstr.Results) == len(r.X.Results) {
			// find Y
			found := false
			for j := 0; j < len(r.Y); j += 1 {
				if findYItem(d, r, i, r.Y[j]) {
					found = true
					break
				}
				if ok := d.DeferCall[r.Y[j].Fn]; ok {
					found = true
					break
				}
			}
			if !found {
				arr := []string{}
				for _, yItem := range r.Y {
					arr = append(arr, fmt.Sprintf("%q", yItem.Fn))
				}
				path := util.FinePathName(pass, callInstr.Orig.Pos())
				if !util.IsInWhiteList(path, whiteList) {
					pass.Reportf(callInstr.Orig.Pos(), "[netdisk-sa][%s]: missing %s", r.RuleName, strings.Join(arr, " or "))
				}
			}
		}
	}
}

func run(pass *analysis.Pass) (interface{}, error) {
	config.Configure()
	sa := pass.ResultOf[buildssa.Analyzer].(*buildssa.SSA)
	for _, f := range append(sa.SrcFuncs, sa.Pkg.Func("init")) {
		d := function.GetDigest(f)
		if d == nil {
			continue
		}
		for _, r := range config.Config.Rules {
			doRule(pass, d, r)
		}
	}
	return nil, nil
}
