{"rules": [{"x": {"fn": "(*sync.<PERSON>tex).Lock", "params": "o"}, "y": [{"fn": "(*sync.<PERSON>tex).Unlock", "params": "o"}], "escape": true, "max_depth": 2}, {"x": {"fn": "followedby/simple.open", "params": "x", "results": ""}, "y": [{"fn": "followedby/simple.close", "params": "x"}], "escape": true, "max_depth": 2}, {"x": {"fn": "followedby/multiple.open", "params": "ab", "results": "cd"}, "y": [{"fn": "followedby/multiple.close", "params": "ad"}], "escape": true, "max_depth": 2}, {"x": {"fn": "(*followedby/stru1.File).open", "params": "oab", "results": "cd"}, "y": [{"fn": "(*followedby/stru1.File).close", "params": "oad"}], "escape": true, "max_depth": 2}, {"x": {"fn": "(followedby/stru2.File).open", "params": "oab", "results": "cd"}, "y": [{"fn": "(followedby/stru2.File).close", "params": "oad"}], "escape": true, "max_depth": 2}, {"x": {"fn": "(followedby/interf1.Processor).Process", "params": "oab", "results": "cd"}, "y": [{"fn": "(followedby/interf1.Processor).Clean", "params": "o-abcd-"}], "escape": true, "max_depth": 2}, {"x": {"fn": "os.Open", "params": "-", "results": "f-"}, "y": [{"fn": "(*os.File).Close", "params": "f"}], "escape": true, "max_depth": 2}, {"x": {"fn": "(*database/sql.Rows).Next", "params": "o", "results": "-"}, "y": [{"fn": "(*database/sql.Rows).Err", "params": "o"}], "escape": false, "max_depth": 2}]}