package config

import (
	"flag"
	"sync"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/followedby/rule"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

type Conf struct {
	Rules []*rule.Rule `json:"rules"`
}

var Config Conf

func getConfigFile() string {
	res := "followedby.json"
	flg := flag.Lookup("conf")
	if flg != nil && flg.Value.String() != "" {
		res = flg.Value.String()
	}
	return res
}

var once sync.Once

func Configure() {
	once.Do(func() {
		err := util.LoadConf(getConfigFile(), &Config)
		if err != nil {
			setConf(&Config) //用于插件
		}

		for _, r := range Config.Rules {
			r.<PERSON>()
		}
	})
}

func setConf(conf *Conf) {
	var r1 = &rule.Rule{
		RuleName: "MutexLock",
		X: &rule.X{
			Fn:      "(*sync.Mutex).Lock",
			Params:  "o",
			Results: "",
		},
		Y: []*rule.YItem{
			{
				Fn:     "(*sync.Mutex).Unlock",
				Params: "o",
			},
		},
		MaxDepth: 3,
		Escape:   true,
	}

	var r2 = &rule.Rule{
		RuleName: "RowsErr",
		X: &rule.X{
			Fn:      "(*database/sql.Rows).Next",
			Params:  "o",
			Results: "-",
		},
		Y: []*rule.YItem{
			{
				Fn:     "(*database/sql.Rows).Err",
				Params: "o",
			},
		},
		MaxDepth: 3,
		Escape:   true,
	}
	conf.Rules = append(conf.Rules, r1, r2)
}
