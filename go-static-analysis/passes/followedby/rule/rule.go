package rule

import "log"

type X struct {
	Fn      string `json:"fn"`
	Params  string `json:"params"`
	Results string `jsson:"results"`
}

type YItem struct {
	Fn     string `json:"fn"`
	Params string `json:"params"`

	ParamsCooked [][2]int `json:"-"`
}

type Rule struct {
	RuleName string   `json:"rule_name"`
	X        *X       `json:"X"`
	Y        []*YItem `json:"Y"`
	Escape   bool     `json:"escape"`
	MaxDepth int      `json:"max_depth"`
}

func (r *Rule) Cook() {
	for i := 0; i < len(r.Y); i += 1 {
		yItem := r.Y[i]
		yItem.ParamsCooked = make([][2]int, len(yItem.Params))
	NEXT:
		for j := 0; j < len(yItem.Params); j += 1 {
			yItem.ParamsCooked[j] = [2]int{-1, -1}

			if yItem.Params[j] == '-' {
				continue
			}
			for k := 0; k < len(r.X.Params); k += 1 {
				if yItem.Params[j] == r.X.Params[k] {
					yItem.ParamsCooked[j] = [2]int{0, k} // match kth parameter of X
					continue NEXT
				}
			}
			for k := 0; k < len(r.X.Results); k += 1 {
				if yItem.Params[j] == r.X.Results[k] {
					yItem.ParamsCooked[j] = [2]int{1, k} // match kth result of X
					continue NEXT
				}
			}
			log.Printf("unmatched parameter %q\n", yItem.Params[j])
		}
	}
}
