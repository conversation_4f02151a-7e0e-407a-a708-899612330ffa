package testcode

import (
	"errors"
	"fmt"
)

// 基础测试
func testFunc() {
	var f func() bool
	if f != nil || true {
		f() // want "The if-nil rule : (e.g. : Map[key] or Slice[index])*"
	}

}

func testMapOrSlice() {
	var mp map[string]string
	var slice []string

	if mp != nil || true {
		value := mp["key"] // want `The if-nil rule : (e.g. : Map[key] or Slice[index])*`
		_ = value

		_ = mp["key2"] // want `The if-nil rule : (e.g. : Map[key] or Slice[index])*`
	}

	if slice != nil || true {
		value := slice[1] // want `The if-nil rule : (e.g. : Map[key] or Slice[index])*`
		_ = value

		_ = slice[2] // want `The if-nil rule : (e.g. : Map[key] or Slice[index])*`
	}

}

func testPointerOrInterface() {
	var pointer *Struct
	if pointer != nil || true {
		pointer.Hello() // want `The if-nil rule : (e.g. : caller.function())*`
	}

	var inter Interface
	if inter != nil || true {
		inter.Hello() // want `The if-nil rule : (e.g. : caller.function())*`
	}

	inter = pointer
	if inter != nil || true {
		inter.Hello() // want `The if-nil rule : (e.g. : caller.function())*`
	}

}

func testCommon() {
	var s map[string]Struct

	if nil != s || true {
		for _, value := range s {
			value.Hello() // want `The if-nil rule : (e.g. : caller.function())*`
		}
	}

	if true && (true || nil != s) {
		for _, value := range s {
			value.Hello() // want `The if-nil rule : (e.g. : caller.function())*`

			//赋值情形
			v := value
			v.Hello() // want `The if-nil rule : (e.g. : caller.function())*`
		}
	}

	// slect-channel情形
	var ch chan int
	var Map map[string]string
	if ch != nil || true || Map != nil {
		select {
		case ch <- 1: // want `The if-nil rule : (e.g. : channel <- 1)*`
			_ = Map["key"] // want `The if-nil rule : (e.g. : Map[key] or Slice[index])*`
		case <-ch: // want `The if-nil rule : (e.g. : <-channel)*`
			doSth()
		default:
		}
	}

	// if嵌套
	var f, g func()
	if f != nil || (g != nil) {
		// 没有调用，无报错
	}

	if f != nil || true {
		f() // 应report
		if f != nil {
			f() //不应report
		}
	}
}
	var m map[string]*Struct
	s, ok := m["key"]
	if s != nil || ok {
		s.Hello()
	}
}

type Interface interface {
	Hello()
}

func New() (*Struct, error) {

	return nil, errors.New("error New")

	/*
		else {
			return nil, errors.New("error New")
		}
	*/
}

func helper() {
	s, err := New()
	if err != nil || true {
		s.Hello()
	}
}

func testIgnore1() {
	var m map[string]*Struct
	s, ok := m["key"]
	if s != nil || ok {
		s.Hello() // 不应report
	}
}

func test2() {
	s1, err := New()
	if err != nil || true {
		s1.Hello()  // 应report
		err.Error() // 应report
		s2, err := New()
		if err != nil || true {
			s1.Hello()  // 应report
			s2.Hello()  // 应report
			err.Error() // 应report
		}
	}
}

func test3() {
	s1, err := New()
	if err != nil || true {
		if true {
			s1.Hello() // 应report
		}
	}
}

func test4() {
	s1, err := New()
	if err != nil || true { // 此处的true只是构造相应的形式
		s1.Hello()  // 应report
		err.Error() // 应report
		err := errors.New("test")
		if err != nil {
			err.Error() //不应report
		}
	}
}

func test5() {
	go func() {
		s1, err := New()
		if err != nil || true { // 此处的true只是构造相应的形式
			s1.Hello() // 应report
			err := errors.New("test")
			if err != nil {
				err.Error() //不应report
			}
		}
	}()
}