package ifnilutil

import (
	"encoding/json"
	"fmt"
	"go/token"
	"log"
)

type Diagnostic struct {
	Pos      token.Pos
	End      token.Pos // optional
	Category string    // optional
	Message  string
}

type JSONTree map[string]map[string][]interface{}

func (tree JSONTree) GenDiagnostic(pos token.Pos, msg string) Diagnostic {
	return Diagnostic{Pos: pos, Message: msg}
}

func (tree JSONTree) Add(fset *token.FileSet, id, name string, f Diagnostic) {
	type jsonDiagnostic struct {
		Category string `json:"category,omitempty"`
		Posn     string `json:"posn"`
		Message  string `json:"message"`
	}

	diagnostics := jsonDiagnostic{
		Category: f.Category,
		Posn:     fset.Position(f.Pos).String(),
		Message:  f.Message,
	}

	m, ok := tree[id]
	if !ok {
		m = make(map[string][]interface{})
		tree[id] = m
	}
	m[name] = append(m[name], &diagnostics)

}

func (tree JSONTree) Output() {
	data, err := json.MarshalIndent(tree, "", "\t")
	if err != nil {
		log.Panicf("internal error: JSON marshaling failed: %v", err)
	}
	fmt.Printf("%s\n", data)
}
