package ifnilutil

import (
	"encoding/json"
	"fmt"
	"go/token"
	"io"
	"os"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const ReportUrl = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d6656ea5365684082ff804960a3d2b245" // 静态检测规则错误统计

func SendWarnMessageToHi(msg string) {
	headerMap := map[string]string{}
	headerMap["Content-Type"] = "application/json"

	conMaps := []map[string]interface{}{}
	conMap := map[string]interface{}{}
	conMap["type"] = "TEXT"
	conMap["content"] = msg
	conMaps = append(conMaps, conMap)

	bodyMap := map[string]interface{}{}
	bodyMap["body"] = conMaps

	messageMap := map[string]interface{}{}
	messageMap["message"] = bodyMap

	bodyJson, _ := json.Marshal(messageMap)
	for i := 0; i < 3; i++ {
		_, err := httpclient.Post(ReportUrl, headerMap, 1000, 3000, string(bodyJson))
		if err == nil {
			break
		}
	}
}

type Option struct {
	OutputFormat string // 默认json格式(符合流水线), "text"为text格式（符合插件).即该成员取值为text为符合插件形式，否则则为字符串格式
}

func (o *OutputMessages) GenDiagnostic(pos token.Pos, format string, args ...interface{}) Diagnostic {
	msg := fmt.Sprintf(format, args...)
	return Diagnostic{Pos: pos, Message: msg}
}

type OutputMessages struct {
	JsonMessages map[string]map[string][]interface{}
	TextMessages []string

	FormatIsText bool
}

func (o *OutputMessages) Add(fset *token.FileSet, id, name string, f Diagnostic) {
	if o.FormatIsText {
		o.AddWithText(fset, f)
	} else {
		o.AddWithJson(fset, id, name, f)
	}
}

// 把检测结果以text格式存起来
func (o *OutputMessages) AddWithJson(fset *token.FileSet, id, name string, f Diagnostic) {
	type jsonDiagnostic struct {
		Category string `json:"category,omitempty"`
		Posn     string `json:"posn"`
		Message  string `json:"message"`
	}

	diagnostics := jsonDiagnostic{
		Category: f.Category,
		Posn:     fset.Position(f.Pos).String(),
		Message:  f.Message,
	}

	m, ok := o.JsonMessages[id]
	if !ok {
		m = make(map[string][]interface{})
		o.JsonMessages[id] = m
	}
	m[name] = append(m[name], &diagnostics)
}

// 把检测结果以text格式存起来
func (o *OutputMessages) AddWithText(fset *token.FileSet, f Diagnostic) {
	posn := fset.Position(f.Pos).String()
	message := f.Message

	tmp := fmt.Sprintf("%s: %s", posn, message)

	o.TextMessages = append(o.TextMessages, tmp)

}

func (o *OutputMessages) Output() error {
	if o.FormatIsText {
		o.OutputWithText(os.Stderr)
		return nil
	}

	o.OutputWithJSON(os.Stderr)
	return nil
}

func (o *OutputMessages) OutputWithJSON(w io.Writer) {
	data, err := json.MarshalIndent(o.JsonMessages, "", "\t")
	if err != nil {
		SendWarnMessageToHi("internal error: JSON marshaling failed:" + err.Error())
	}
	fmt.Fprintln(w, string(data))
}

func (o *OutputMessages) OutputWithText(w io.Writer) {
	for _, line := range o.TextMessages {
		fmt.Fprintln(w, line)
	}
}
