package ifnil

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	"golang.org/x/tools/go/ast/astutil"
	"golang.org/x/tools/go/packages"
	"golang.org/x/tools/go/ssa"
	"golang.org/x/tools/go/ssa/ssautil"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/ifnil/ifnilutil"
)

type Runner struct {
	// 代码库全局信息
	Files        map[*ast.File]bool           //  all ast.File from the program
	FilesNameMap map[string]bool              // store pkg name from AST
	SSAFuncMap   map[FuncKey]*ssa.Function    // store function from SSA
	FsetMap      map[*ast.File]*token.FileSet //存储每个文件对应的Fset

	// 当前待检测函数的信息
	CurASTFunc *ast.FuncDecl
	CurSSAFunc *ssa.Function
	CurFSet    *token.FileSet
	AllSSACall map[CallKey]*ssa.Call

	FuncGroupMap *GroupMap //函数级别GroupMap，由ast.Inspect维护和更新。
	CurGroupMap  *GroupMap //if语句级别GroupMap，遇到if语句时从GroupMap深拷贝过来并维护和更新。

	//全局代码库的待打印信息
	OutputMessages *ifnilutil.OutputMessages // 存储输出信息

	Prog *ssa.Program
}

func NewRunner() *Runner {
	return &Runner{
		Files:          make(map[*ast.File]bool),
		FilesNameMap:   make(map[string]bool),
		SSAFuncMap:     make(map[FuncKey]*ssa.Function),
		FsetMap:        make(map[*ast.File]*token.FileSet),
		AllSSACall:     make(map[CallKey]*ssa.Call),
		OutputMessages: new(ifnilutil.OutputMessages),
	}
}

type GroupMap struct {
	AssignMap    map[string][]Pair // key-value get from assignment-statement
	CheckMap     map[string]bool   // key-value get from if-statement
	WhiteListMap map[string]bool   // 白名单Map
}

func newGroupMap() *GroupMap {
	return &GroupMap{
		AssignMap:    make(map[string][]Pair),
		CheckMap:     make(map[string]bool),
		WhiteListMap: make(map[string]bool),
	}
}

func deepCopyGroupMap(oldMap *GroupMap) *GroupMap {
	newMap := newGroupMap()
	for k, v := range oldMap.AssignMap {
		newMap.AssignMap[k] = v
	}

	for k, v := range oldMap.CheckMap {
		newMap.CheckMap[k] = v
	}

	for k, v := range oldMap.WhiteListMap {
		newMap.WhiteListMap[k] = v
	}
	return newMap
}

type Pair struct {
	Name  string
	Check bool
}

func Run(option *ifnilutil.Option) {
	var formatIsText bool
	if option != nil && option.OutputFormat == "text" {
		formatIsText = true
	} else {
		formatIsText = false
	}

	dir, err := os.Getwd()
	if err != nil {
		fmt.Println("os.Getwd() failed.")
		return
	}
	r := NewRunner()
	r.OutputMessages.FormatIsText = formatIsText
	if !formatIsText {
		r.OutputMessages.JsonMessages = make(map[string]map[string][]interface{})
	}

	if err := r.GetFileNames(); err != nil {
		ifnilutil.SendWarnMessageToHi("[ifnil]获取文件名失败: " + dir + "\n err: " + err.Error())
		return
	}
	if err := r.GetSSAFunction(); err != nil {
		ifnilutil.SendWarnMessageToHi("[ifnil]buildssa失败: " + dir + "\n err: " + err.Error())
		return
	}

	r.Run()
	r.Output()
}

func (r *Runner) Run() {
	for file := range r.Files {
		r.CurFSet = r.FsetMap[file]
		ast.Inspect(file, func(node ast.Node) bool {
			if fn, ok := node.(*ast.FuncDecl); ok {
				r.handleASTFunc(fn)
				return false
			}
			return true
		})
	}
}

func (r *Runner) handleASTFunc(fn *ast.FuncDecl) {
	r.FuncGroupMap = newGroupMap()
	r.CurASTFunc = fn
	pos := r.CurFSet.Position(fn.Pos())
	fnKey := PostionToFuncKey(pos)
	if fn, ok := r.SSAFuncMap[fnKey]; ok {
		r.CurSSAFunc = fn
	} else {
		return
	}

	ast.Inspect(fn, func(node ast.Node) bool {
		if assign, ok := node.(*ast.AssignStmt); ok {
			r.handleAssign(assign) // 存储数据阶段
			return false
		}

		if ifStmt, ok := node.(*ast.IfStmt); ok {
			r.handleStmtIsIf(ifStmt) // 检查阶段，其中对if语句内的赋值语句则进行存储or删除数据
			return true
		}
		return true
	})
}

func (r *Runner) handleStmtIsIf(ifStmt *ast.IfStmt) {
	names := r.getIdentsWithOp(ifStmt.Cond, "!=")
	if names == nil {
		return
	}
	r.CurGroupMap = deepCopyGroupMap(r.FuncGroupMap)
	newAssignMap := make(map[string][]Pair)
	for _, name := range names {
		r.CurGroupMap.CheckMap[name] = true
		value := r.CurGroupMap.AssignMap[name]
		if value != nil {
			newAssignMap[name] = value
		}
	}

	r.setWhiteListForIf(names, newAssignMap, ifStmt)

	for _, v := range newAssignMap {
		for _, pair := range v {
			if pair.Check {
				r.CurGroupMap.CheckMap[pair.Name] = true
			}
		}
	}

	r.checkStmtIsBlock(ifStmt.Body)
}

func (r *Runner) setWhiteListForIf(names []string, newAssignMap map[string][]Pair, ifStmt *ast.IfStmt) {
	flag := false
	for _, name := range names {
		if r.CurGroupMap.CheckMap[name] {
			flag = true
			break
		}
	}

	if flag {
		shouldSet := false
		idents := r.getIdentsWithOp(ifStmt.Cond, "==")
		tmpMap := make(map[string]struct{}) // 判断是否有重复
		for _, name := range names {
			values := newAssignMap[name] // a, b, err := fn(), values means [a, b] （不包含 err）

			for _, value := range values {
				tmpMap[value.Name] = struct{}{}
			}
			for _, ident := range idents {
				if _, ok := tmpMap[ident]; ok {
					shouldSet = true
					break
				}
			}
		}

		if shouldSet {
			for _, name := range names {
				r.CurGroupMap.WhiteListMap[name] = true
			}
		}
	}
}

// e.g.: if variable op nil || .. {} , 即有 variable op nil 关系和 || 时，将提取 variable.
// 这里 op 可为 "!=" 或 "=="
func (runner *Runner) getIdentsWithOp(e ast.Expr, op string) []string {
	expr := astutil.Unparen(e)
	b, ok := expr.(*ast.BinaryExpr)
	if !ok {
		return nil
	}

	var res []string

	if b.Op.String() == "||" {
		left := astutil.Unparen(b.X)
		l, lOK := left.(*ast.BinaryExpr)
		if lOK {
			tmp := getIdentsWithOpFallBack(l, op)
			if tmp != nil {
				res = append(res, tmp.Name)
			}
		}

		right := astutil.Unparen(b.Y)
		r, rOK := right.(*ast.BinaryExpr)
		if rOK {
			tmp := getIdentsWithOpFallBack(r, op)
			if tmp != nil {
				res = append(res, tmp.Name)
			}
		}
	}

	l := runner.getIdentsWithOp(b.X, op)
	if l != nil {
		res = append(res, l...)
	}

	r := runner.getIdentsWithOp(b.Y, op)
	if r != nil {
		res = append(res, r...)
	}

	return res
}

func getIdentsWithOpFallBack(binary *ast.BinaryExpr, inputOp string) *ast.Ident {
	if binary == nil {
		return nil
	}

	op := binary.Op.String()
	if op != inputOp {
		return nil
	}

	l, lOK := binary.X.(*ast.Ident)
	r, rOK := binary.Y.(*ast.Ident)
	if lOK && rOK {
		if l.Name == "nil" && r.Name != "nil" {
			return r
		}

		if l.Name != "nil" && r.Name == "nil" {
			return l
		}
	}

	return nil
}

func (r *Runner) handleAssign(assign *ast.AssignStmt) {
	r.storeKVFromAssign(assign)
	r.setWhiteListForAssign(assign)
}

// 检查左边项数为大于等于2项， 右边项数为一项且为函数调用,用于storeKV。将返回右边调用CallExpr的位置信息
func (r *Runner) assignIsValid(assign *ast.AssignStmt) (token.Pos, bool) {
	lhsLen := len(assign.Lhs)
	rhsLen := len(assign.Rhs)
	if lhsLen <= 1 || rhsLen != 1 {
		return token.NoPos, false
	}

	if call, ok := assign.Rhs[0].(*ast.CallExpr); ok {
		return call.Pos(), true
	}

	return token.NoPos, false
}

func (r *Runner) storeKVFromAssign(assign *ast.AssignStmt) {
	tokPos, ok := r.assignIsValid(assign)
	if !ok {
		return
	}

	fn := r.CurSSAFunc
	if fn == nil {
		return
	}

	pos := r.CurFSet.Position(tokPos)
	callKey := PostionToCallKey(pos)

	call := r.AllSSACall[callKey]

	lhsNames := findAssignNames(assign) // len(lhsName) = size
	key := lhsNames[len(lhsNames)-1]
	if key == "" { // 对应场景： a, _ := f()
		return
	}

	rhsReturn := r.findCallReturns(call) // len(rhsReturn) = size - 1， true means nil
	isEnter := true                      // 如果函数右边能跟进去，则true, 否则false
	if rhsReturn == nil {
		isEnter = false
	}

	var values = make([]Pair, len(lhsNames)-1)
	if isEnter { // 左边长度为右边长度 + 1
		for i, v := range rhsReturn {
			values[i] = Pair{
				Name:  lhsNames[i],
				Check: v,
			}
		}
	} else {
		for i := 0; i < len(lhsNames)-1; i++ {
			values[i] = Pair{
				Name:  lhsNames[i],
				Check: false,
			}
		}
	}

	r.FuncGroupMap.AssignMap[key] = values
}

/*
// WhiteMap目前作用为: 针对以下case， 对s加进白名单
//	var m map[string]*testutil.Struct
//	s, ok := m["key"]
//	if s != nil || ok {
//		s.Hello()
//	}
*/
func (r *Runner) setWhiteListForAssign(stmt *ast.AssignStmt) {
	if len(stmt.Rhs) != 1 {
		return
	}
	if _, ok := stmt.Rhs[0].(*ast.IndexExpr); !ok {
		return
	}

	if len(stmt.Lhs) != 2 {
		return
	}
	ident, ok := stmt.Lhs[0].(*ast.Ident)
	if !ok {
		return
	}

	r.FuncGroupMap.WhiteListMap[ident.Name] = true
}

func findAssignNames(assign *ast.AssignStmt) []string {
	lhs := assign.Lhs
	res := make([]string, len(lhs))
	for i, v := range lhs {
		if v, ok := v.(*ast.Ident); ok {
			res[i] = v.Name
		}
	}
	return res
}

func (r *Runner) findCallReturns(call *ssa.Call) []bool {
	if call == nil {
		return nil
	}
	fn := r.CallToSSAFunc(call)
	if fn == nil {
		/*
			pos := r.CurSSAFunc.Prog.Fset.Position(call.Pos())
			ifnilutil.SendWarnMessageToHi("测试用: fn == nil 找不到fn. pos: " + pos.String())
		*/
		return nil
	}

	var res []bool
	for _, block := range fn.Blocks {
		for _, instr := range block.Instrs {
			if rt, ok := instr.(*ssa.Return); ok {
				tmp := getReturns(rt)
				if tmp == nil {
					continue
				}

				if res == nil {
					res = tmp
				} else {
					for i := 0; i < len(tmp); i++ {
						res[i] = res[i] || tmp[i]
					}
				}
			}
		}
	}
	return res
}

func (r *Runner) GetSSAFunction() error {
	prog, err := r.buildSSA()
	if err != nil {
		return err
	}

	fnMap := ssautil.AllFunctions(prog)

	for fn := range fnMap {
		if r.neetToStore(fn) {
			pos := fn.Prog.Fset.Position(fn.Pos())
			fnKey := PostionToFuncKey(pos)
			r.SSAFuncMap[fnKey] = fn
			r.StoreSSACall(fn)
		}
	}
	return nil
}

func (r *Runner) GetFileNames() error {
	curDir, err := os.Getwd()
	if err != nil {
		return err
	}
	walkFunc := func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			slice := strings.Split(path, "/")
			s := slice[len(slice)-1]

			// 忽略隐藏目录
			if len(s) != 0 && s[0] == '.' {
				return fs.SkipDir
			}
		} else {
			if strings.HasSuffix(path, ".go") && !strings.HasSuffix(path, "test.go") && !strings.Contains(path, "testdata") {

				fset := token.NewFileSet()
				f, err := parser.ParseFile(fset, path, nil, 0)
				if err != nil {
					return nil
				}
				r.Files[f] = true
				r.FilesNameMap[path] = true
				r.FsetMap[f] = fset
			}
		}
		return nil
	}

	err = filepath.Walk(curDir, walkFunc)
	return err
}

// 如果返回值是nil
// 多返回值，最后个返回值不为空的情形下，前面的如果为空，便记号为true
func getReturns(rt *ssa.Return) []bool {
	returns := rt.Results
	size := len(returns)
	if size <= 1 {
		return nil
	}

	tail := returns[size-1]
	if v, ok := tail.(*ssa.Const); ok {
		if v.IsNil() {
			return nil
		}
	}

	res := make([]bool, size-1)
	for i := 0; i < size-1; i++ {
		v, ok := returns[i].(*ssa.Const)
		if ok && v.IsNil() {
			res[i] = true
		}
	}
	return res
}

func (r *Runner) CallToSSAFunc(call *ssa.Call) *ssa.Function {
	if call == nil {
		return nil
	}
	comm := call.Common()

	var fn *ssa.Function
	if !comm.IsInvoke() {
		switch v := comm.Value.(type) {
		case *ssa.Builtin: // built-in function call
		case *ssa.MakeClosure: // static function closure call
			fn = v.Fn.(*ssa.Function)
		case *ssa.Function:
			fn = v
		default: // dynamic function call
			/*
				msg := fmt.Sprintln("dymamic function call")
				msg += "pos: " + r.Prog.Fset.Position(call.Pos()).String()
				ifnilutil.SendWarnMessageToHi(msg)
			*/
		}
	}
	return fn
}

func (r *Runner) StoreSSACall(fn *ssa.Function) {
	for _, f := range append(fn.AnonFuncs, fn) {
		for _, block := range f.Blocks {
			for _, instr := range block.Instrs {
				if call1, ok := instr.(*ssa.Call); ok {
					pos1 := fn.Prog.Fset.Position(call1.Pos())
					callKey := PostionToCallKey(pos1)
					if call2, ok := r.AllSSACall[callKey]; !ok {
						r.AllSSACall[callKey] = call1
					} else {
						// 比较两个call， 哪个列数最早存哪个
						pos2 := fn.Prog.Fset.Position(call2.Pos())
						if pos1.Column < pos2.Column {
							r.AllSSACall[callKey] = call1
						} else {
							r.AllSSACall[callKey] = call2
						}
					}

				}
			}
		}
	}
}

// 如果需要存，则返回true
func (r *Runner) neetToStore(fn *ssa.Function) bool {
	if fn == nil {
		return false
	}

	pos := fn.Prog.Fset.Position(fn.Pos())
	if r.FilesNameMap[pos.Filename] {
		return true
	}

	return false
}

type FuncKey struct {
	Filename string
	Line     int
}

type CallKey struct {
	Filename string
	Line     int
}

func PostionToFuncKey(pos token.Position) FuncKey {
	return FuncKey{
		Filename: pos.Filename,
		Line:     pos.Line,
	}
}

func PostionToCallKey(pos token.Position) CallKey {
	return CallKey{
		Filename: pos.Filename,
		Line:     pos.Line,
	}
}

func (r *Runner) buildSSA() (*ssa.Program, error) {
	cfg := &packages.Config{
		Mode: packages.LoadAllSyntax,
	}

	pkgs, err := packages.Load(cfg, "./...")
	if err != nil {
		return nil, err
	}

	prog, _ := ssautil.AllPackages(pkgs, ssa.SanityCheckFunctions)
	prog.Build()
	r.Prog = prog
	return prog, nil
}

func (r *Runner) checkStmtIsBlock(b *ast.BlockStmt) {
	for _, stmt := range b.List {
		r.checkStmt(&stmt)
	}
}

func (r *Runner) checkStmt(s *ast.Stmt) {
	switch stmt := (*s).(type) {
	case *ast.ExprStmt:
		r.checkStmtIsExpr(stmt)
	case *ast.SendStmt:
		r.checkStmtIsSend(stmt)
	case *ast.AssignStmt:
		r.checkStmtIsAssign(stmt)
	case *ast.ReturnStmt:
		r.checkStmtIsReturn(stmt)
	case *ast.BlockStmt:
		r.checkStmtIsBlock(stmt)
	case *ast.IfStmt:
		r.checkStmtIsIf(stmt)
	case *ast.CaseClause:
		r.checkStmtIsCaseCaluse(stmt)
	case *ast.SwitchStmt:
		r.checkStmtIsSwitch(stmt)
	case *ast.CommClause:
		r.checkStmtIsCommClause(stmt)
	case *ast.SelectStmt:
		r.checkStmtIsSelect(stmt)
	case *ast.ForStmt:
		r.checkStmtIsFor(stmt)
	case *ast.RangeStmt:
		r.checkStmtIsRange(stmt)
	}
}

func (r *Runner) checkStmtIsRange(stmt *ast.RangeStmt) {
	if x, ok := stmt.X.(*ast.Ident); ok {
		if exists := r.CurGroupMap.CheckMap[x.Name]; exists {
			if ident, ok := stmt.Value.(*ast.Ident); ok {
				r.CurGroupMap.CheckMap[ident.Name] = true
			}
		}
	}

	r.checkStmtIsBlock(stmt.Body)
}

func (r *Runner) checkStmtIsFor(stmt *ast.ForStmt) {
	r.checkStmtIsBlock(stmt.Body)
}

/*
	A commmClause node represents a case of a select statement.
	e.g :

slect {
case ch<- :
}
*/
func (r *Runner) checkStmtIsCommClause(stmt *ast.CommClause) {
	//stmt.Comm成员
	switch comm := stmt.Comm.(type) {
	case *ast.SendStmt:
		r.checkStmtIsSend(comm)
	case *ast.ExprStmt:
		r.checkStmtIsExpr(comm)
	}
	for _, s := range stmt.Body {
		r.checkStmt(&s)
	}
}

func (r *Runner) checkStmtIsSelect(stmt *ast.SelectStmt) {
	r.checkStmtIsBlock(stmt.Body)
}

func (r *Runner) checkStmtIsSwitch(stmt *ast.SwitchStmt) {
	r.checkStmtIsBlock(stmt.Body)
}

func (r *Runner) checkStmtIsCaseCaluse(stmt *ast.CaseClause) {
	for _, s := range stmt.Body {
		r.checkStmt(&s)
	}
}

// if语句内，检查阶段遇到的if语句,即嵌套if语句
func (r *Runner) checkStmtIsIf(stmt *ast.IfStmt) {
	r.removeIdentFromIf(stmt)
	r.checkStmtIsBlock(stmt.Body)
}

/*
if f != nil || (g != nil) {
		f() // 应report
		if f != nil { （1）
			f()
		} else {
			g()
		}
	}
*/
// 检查阶段，把嵌套if中的条件判断(1)处的ident从CheckMap remove掉
func (r *Runner) removeIdentFromIf(ifStmt *ast.IfStmt) {
	r.removeIdentFromIfFallback(ifStmt.Cond)
}

func (r *Runner) removeIdentFromIfFallback(expr ast.Expr) {
	e := astutil.Unparen(expr)
	binary, ok := e.(*ast.BinaryExpr)
	if !ok {
		return
	}

	checkMap := r.CurGroupMap.CheckMap
	if left, ok := binary.X.(*ast.Ident); ok {
		delete(checkMap, left.Name)
	}
	if right, ok := binary.Y.(*ast.Ident); ok {
		delete(checkMap, right.Name)
	}

	r.removeIdentFromIfFallback(binary.X)
	r.removeIdentFromIfFallback(binary.Y)
}

func (r *Runner) checkStmtIsReturn(stmt *ast.ReturnStmt) {
	for _, expr := range stmt.Results {
		r.checkExpr(&expr)
	}
}

// 该赋值语句为if语句内，检查是否阶段的赋值语句，这里需要对CurGroupMap进行维护：
// 1.以下情形，对GroupMap踢出对应元素：
// 1.1) 赋值语句右边不在GroupMap中，左边在，则把左边踢出。
// 2.以下情形，对GroupMap加入对应元素：
// 2.1) 赋值语句右边在GroupMap中，左边不在，则把左边加入。
func (r *Runner) checkStmtIsAssign(stmt *ast.AssignStmt) {
	curMap := r.CurGroupMap

	lhs := stmt.Lhs
	rhs := stmt.Rhs
	lhsLen, rhsLen := len(lhs), len(rhs)
	if lhsLen != rhsLen { // 左右长度不匹配。情形: a, b := fn()。踢出左边元素。
		for _, v := range lhs {
			ident, ok := v.(*ast.Ident)
			if !ok {
				continue
			}
			delete(curMap.CheckMap, ident.Name)
		}
	} else { // 长度匹配。加入或踢出左边元素。
		for i, v := range lhs {
			leftIdent, ok := v.(*ast.Ident)
			if !ok {
				continue
			}

			rightIdent, ok := rhs[i].(*ast.Ident)
			if !ok { // 右边不为ast.Ident,肯定不在，踢出左边
				delete(curMap.CheckMap, leftIdent.Name)
			} else { // 此时右边为ast.Ident，如果在Map里，则加入左边元素。
				if curMap.CheckMap[rightIdent.Name] {
					curMap.CheckMap[leftIdent.Name] = true
				}
			}
		}
	}

	// 检查右边表达式是否有调用
	for _, expr := range rhs {
		r.checkExpr(&expr)
	}
}

func (r *Runner) checkStmtIsSend(stmt *ast.SendStmt) {
	ch := stmt.Chan
	ident, ok := ch.(*ast.Ident)
	if !ok {
		return
	}

	if r.reportOrNot(ident.Name) {
		message := "[netdisk-sa][IfNil]: the ifnil rule: stmt is SentStmt, like a <-1 and a maybe nil."
		r.Reportf(ident.Pos(), message)
	}
}

func (r *Runner) checkStmtIsExpr(e *ast.ExprStmt) {
	r.checkExpr(&e.X)
}

func (r *Runner) checkExpr(expr *ast.Expr) {
	switch expr := (*expr).(type) {
	case *ast.ParenExpr:
		r.checkExprIsParen(expr)
	case *ast.SelectorExpr: // e.g.: caller.method() or a.b
		r.checkExprIsSelector(expr)
	case *ast.IndexExpr: // e.g.: slice[index] or map[key]
		r.checkExprIsIndex(expr)
	case *ast.CallExpr:
		r.checkExprIsCall(expr)
	case *ast.UnaryExpr:
		r.checkExprIsUnary(expr)
	default:
	}

}

func (r *Runner) checkExprIsUnary(expr *ast.UnaryExpr) {
	if expr.Op.String() != "<-" {
		return
	}

	ident, ok := expr.X.(*ast.Ident)
	if !ok {
		return
	}

	if r.reportOrNot(ident.Name) {
		message := "[netdisk-sa][IfNil]: the ifnil rule: expr is UnaryExpr, like <-a and a maybe nil."
		r.Reportf(ident.Pos(), message)
	}
}

func (r *Runner) checkExprIsCall(expr *ast.CallExpr) {
	fn := expr.Fun
	switch fn := fn.(type) {
	case *ast.SelectorExpr:
		r.checkExprIsSelector(fn)
	case *ast.Ident:
		if r.reportOrNot(fn.Name) {
			message := "[netdisk-sa][IfNil]: the ifnil rule: expr is CallExpr, like f() and f maybe nil."
			r.Reportf(fn.Pos(), message)
		}
	}

	for _, arg := range expr.Args {
		r.checkExpr(&arg)
	}
}

func (r *Runner) checkExprIsIndex(expr *ast.IndexExpr) {
	x := expr.X
	ident, ok := x.(*ast.Ident)
	if !ok {
		return
	}

	if r.reportOrNot(ident.Name) {
		message := "[netdisk-sa][IfNil]: the ifnil rule: expr is IndexExpr, like slice[i] / map[key] and slice/map maybe nil."
		r.Reportf(ident.Pos(), message)
	}
}

func (r *Runner) checkExprIsParen(expr *ast.ParenExpr) {
	r.checkExpr(&expr.X)
}

// e.g: caller.method() or a.b
func (r *Runner) checkExprIsSelector(expr *ast.SelectorExpr) {
	// expr.X means a when expr is "a.b"
	x := expr.X
	ident, ok := x.(*ast.Ident)
	if !ok {
		return
	}

	if r.reportOrNot(ident.Name) {
		message := "[netdisk-sa][IfNil]: expr is SelectorExpr, like a.b / a.method() and a maybe nil."
		r.Reportf(ident.Pos(), message)
	}
}

// 应报告，则返回true; 否则返回false
func (r *Runner) reportOrNot(name string) bool {
	CurMap := r.CurGroupMap
	return CurMap.CheckMap[name] && !CurMap.WhiteListMap[name]
}

// 记录输出信息
func (r *Runner) Reportf(pos token.Pos, format string) {
	om := r.OutputMessages
	diag := om.GenDiagnostic(pos, format)
	pkg := r.CurSSAFunc.Pkg.Pkg
	r.OutputMessages.Add(r.CurFSet, pkg.Path(), pkg.Name(), diag)
}

func (r *Runner) Output() {
	if err := r.OutputMessages.Output(); err != nil {
		fmt.Println("output err: ", err)
	}
}

// 测试用
/*
func FormatFunc(msg string, fs []*ssa.Function) string {
	res := msg + "\n"
	for i, fn := range fs {
		res += fmt.Sprintln("i: ", i, ", fn: ", fn.String())
		res += fmt.Sprintln("pos: ", fn.Prog.Fset.Position(fn.Pos()))
	}
	return res
}
*/
