package recover

import (
	"go/ast"
	"sync"

	"golang.org/x/tools/go/analysis"
)

const doc = "deferrecover checks for deferred functions with recover inside goroutines"

var Analyzer = &analysis.Analyzer{
	Name: "deferrecover",
	Doc:  doc,
	Run:  run,
}

var funcRecover map[string]bool
var mutex sync.Mutex
var onceDo sync.Once

func run(pass *analysis.Pass) (interface{}, error) {
	onceDo.Do(func() {
		funcRecover = make(map[string]bool, 1024)
	})

	for _, file := range pass.Files {
		ast.Inspect(file, func(node ast.Node) bool {
			if fn, ok := node.(*ast.FuncDecl); ok {
				ast.Inspect(fn.Body, func(node ast.Node) bool {
					if callExpr, ok := node.(*ast.CallExpr); ok {
						if ident, ok := callExpr.Fun.(*ast.Ident); ok {
							if ident.Name == "recover" {
								mutex.Lock()
								funcRecover[fn.Name.String()] = true
								// fmt.Println(fn.Name.String())
								mutex.Unlock()
							}
						}
					}
					return true
				})
			}

			return true
		})
	}

	for _, file := range pass.Files {
		ast.Inspect(file, func(node ast.Node) bool {
			if goStmt, ok := node.(*ast.GoStmt); ok {
				// pass.Reportf(goStmt.Pos(), "find goroutine")

				if !hasRecoverUsage(goStmt, pass) {
					pass.Reportf(goStmt.Pos(), "no recover statement inside new goroutine")
				}
			}

			return true
		})
	}

	return nil, nil
}

func hasRecoverUsage(stmt ast.Stmt, pass *analysis.Pass) bool {
	hasRecover := false

	// 匿名函数场景
	// go func {}
	ast.Inspect(stmt, func(node ast.Node) bool {
		if callExpr, ok := node.(*ast.CallExpr); ok {
			if ident, ok := callExpr.Fun.(*ast.Ident); ok {
				if ident.Name == "recover" {
					hasRecover = true
					return false
				}
			}
		}
		return true
	})

	// 非匿名函数场景
	if !hasRecover {
		ast.Inspect(stmt.(*ast.GoStmt), func(node ast.Node) bool {
			// go funcname()
			if callExpr, ok := node.(*ast.CallExpr); ok {
				if ident, ok := callExpr.Fun.(*ast.Ident); ok {
					// fmt.Println("go funcname ", ident.Name)
					mutex.Lock()
					if _, ok := funcRecover[ident.Name]; ok {
						hasRecover = true
					}
					mutex.Unlock()
				}
			}

			// go a.funcname()
			if callExpr, ok := node.(*ast.SelectorExpr); ok {
				// fmt.Println("go name X ", callExpr.Sel.Name)
				mutex.Lock()
				if _, ok := funcRecover[callExpr.Sel.Name]; ok {
					hasRecover = true
				}
				mutex.Unlock()
			}

			return true
		})
	}

	// todo 解决多层封装recover的场景，当前存在多层误判。

	return hasRecover
}
