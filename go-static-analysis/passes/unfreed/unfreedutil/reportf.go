package unfreedutil

import (
	"encoding/json"
	"fmt"
	"go/token"
	"io"
	"os"
)

type DiagnosticA struct {
	Pos      token.Pos
	End      token.Pos // optional
	Category string    // optional
	Message  string
}

func (o *OutputMessages) GenDiagnostic(pos token.Pos, format string, args ...interface{}) DiagnosticA {
	msg := fmt.Sprintf(format, args...)
	return DiagnosticA{Pos: pos, Message: msg}
}

type OutputMessages struct {
	JsonMessages map[string]map[string][]interface{}
	TextMessages []string

	FormatIsText bool
}

func (o *OutputMessages) Add(fset *token.FileSet, id, name string, f DiagnosticA) {
	if o.FormatIsText {
		o.AddWithText(fset, f)
	} else {
		o.AddWithJson(fset, id, name, f)
	}
}

// 把检测结果以text格式存起来
func (o *OutputMessages) AddWithJson(fset *token.FileSet, id, name string, f DiagnosticA) {
	type jsonDiagnostic struct {
		Category string `json:"category,omitempty"`
		Posn     string `json:"posn"`
		Message  string `json:"message"`
	}

	diagnostics := jsonDiagnostic{
		Category: f.Category,
		Posn:     fset.Position(f.Pos).String(),
		Message:  f.Message,
	}

	m, ok := o.JsonMessages[id]
	if !ok {
		m = make(map[string][]interface{})
		o.JsonMessages[id] = m
	}
	m[name] = append(m[name], &diagnostics)
}

// 把检测结果以text格式存起来
func (o *OutputMessages) AddWithText(fset *token.FileSet, f DiagnosticA) {
	posn := fset.Position(f.Pos).String()
	message := f.Message

	tmp := fmt.Sprintf("%s: %s", posn, message)

	o.TextMessages = append(o.TextMessages, tmp)

}

func (o *OutputMessages) Output() error {
	if o.FormatIsText {
		o.OutputWithText(os.Stderr)
		return nil
	}

	o.OutputWithJSON(os.Stderr)
	return nil
}

func (o *OutputMessages) OutputWithJSON(w io.Writer) {
	data, err := json.MarshalIndent(o.JsonMessages, "", "\t")
	if err != nil {
		SendWarnMessageToHi("internal error: JSON marshaling failed:" + err.Error())
	}
	fmt.Fprintln(w, string(data))
}

func (o *OutputMessages) OutputWithText(w io.Writer) {
	for _, line := range o.TextMessages {
		fmt.Fprintln(w, line)
	}
}
