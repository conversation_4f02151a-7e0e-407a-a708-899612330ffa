package unfreedutil

import (
	"golang.org/x/tools/go/packages"
	"golang.org/x/tools/go/ssa"
	"golang.org/x/tools/go/ssa/ssautil"
)

func BuildSSA() (*ssa.Program, error) {
	cfg := &packages.Config{
		Mode: packages.LoadAllSyntax,
	}

	var pkgs []*packages.Package
	var err error

	pkgs, err = packages.Load(cfg, "./...")
	if err != nil {
		return nil, err
	}

	prog, _ := ssautil.AllPackages(pkgs, ssa.SanityCheckFunctions)
	prog.Build()

	return prog, nil
}
