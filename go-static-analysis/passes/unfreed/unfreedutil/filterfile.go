package unfreedutil

import (
	"go/parser"
	"go/token"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
)

type FileMsgs struct {
	// 代码库全局信息
	FilesName map[string]bool // store pkg name from AST
}

func NewFileMsgs() *FileMsgs {
	return &FileMsgs{
		FilesName: make(map[string]bool),
	}
}

func (fm *FileMsgs) Init() error {
	curDir, err := os.Getwd()
	if err != nil {
		return err
	}

	walkFunc := func(path string, info os.FileInfo, err error) error {
		if info.IsDir() {
			slice := strings.Split(path, "/")
			s := slice[len(slice)-1]

			// 忽略隐藏目录
			if len(s) != 0 && s[0] == '.' {
				return fs.SkipDir
			}
		} else {
			if strings.HasSuffix(path, ".go") && !strings.HasSuffix(path, "test.go") && !strings.Contains(path, "testdata") {
				fset := token.NewFileSet()
				_, err := parser.ParseFile(fset, path, nil, 0)
				if err != nil {
					return nil // 此处该递归函数返回 nil 即可，以便可以继续递归下去
				}
				fm.FilesName[path] = true
			}
		}
		return nil
	}

	err = filepath.Walk(curDir, walkFunc)
	return err
}
