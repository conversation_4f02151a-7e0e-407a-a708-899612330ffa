package unfreedutil

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const ReportUrl = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d6656ea5365684082ff804960a3d2b245" // 静态检测规则错误统计

func SendWarnMessageToHi(msg string) {
	headerMap := map[string]string{}
	headerMap["Content-Type"] = "application/json"

	conMaps := []map[string]interface{}{}
	conMap := map[string]interface{}{}
	conMap["type"] = "TEXT"
	conMap["content"] = msg
	conMaps = append(conMaps, conMap)

	bodyMap := map[string]interface{}{}
	bodyMap["body"] = conMaps

	messageMap := map[string]interface{}{}
	messageMap["message"] = bodyMap

	bodyJson, _ := json.Marshal(messageMap)

	for i := 0; i < 3; i++ {
		_, err := httpclient.Post(ReportUrl, headerMap, 1000, 3000, string(bodyJson))
		if err == nil {
			break
		}
	}
}
