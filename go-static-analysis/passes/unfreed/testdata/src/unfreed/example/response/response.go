package response

import (
	"fmt"
	"net/http"
)

func get() *http.Response {
	resp, err := http.Get("xxx")
	if err != nil {
		panic(err)
	}
	return resp
}

func Test1() {
	resp := get()
	defer resp.Body.Close()
}

func Test2() {
	resp := get() // want `missing "\(io\.Closer\)\.Close"`
	func(*http.Response) {}(resp)
}

type Transport struct {
	transport *http.Transport
}

func (t *Transport) Test3() (resp *http.Response, err error) {
	if true {
		resp, err = t.transport.RoundTrip(nil)
	} else {
		resp, err = t.transport.RoundTrip(nil)
	}
	return
}

func Test4() (resp *http.Response, err error) {
	var t *Transport
	if true {
		resp, err = t.transport.RoundTrip(nil)
	} else {
		resp, err = t.transport.RoundTrip(nil)
	}
	return
}

func Test5() {
	resp, err := http.Get("TEST")
	if err != nil {
		fmt.Println(err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	_ = resp
}

func Test6() {
	resp, err := http.Get("TEST")
	if err != nil {
		fmt.Println(err)
	}
	func() {
		if err = resp.Body.Close(); err != nil {
			fmt.Println(err)
		}
	}()
}
