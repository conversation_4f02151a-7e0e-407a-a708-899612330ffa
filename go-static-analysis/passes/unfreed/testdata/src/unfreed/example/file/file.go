package file

import (
	"net"
	"os"
)

func open1() *os.File {
	f, err := os.Open("XXX")
	if err != nil {
		panic(err)
	}
	return f
}

func open2() *os.File {
	return open1()
}

func close1(f *os.File) {
	f.Close()
}

func close2(f *os.File) {
	close1(f)
}

func Test1() {
	f, err := os.Open("XXX")
	if err != nil {
		panic(err)
	}
	defer f.Close()
}

func Test2() {
	f, err := os.Open("XXX") // want `missing "\(\*os\.File\)\.Close"`
	if err != nil {
		panic(err)
	}
	func(*os.File) {}(f)
}

func Test3() {
	f := open1()
	close1(f)
}

func Test4() {
	f := open1() // want `missing "\(\*os\.File\)\.Close"`
	func(*os.File) {}(f)
}

func Test5() {
	f := open1()
	close2(f)
}

func Test6() {
	f := open2()
	close2(f)
}

func Test7() {
	f := open2()
	func() {
		func() {
			f.Close()
		}()
	}()
}

func Test8() {
	f := open2() // want `missing "\(\*os\.File\)\.Close"`
	func() {
		func() {
			close2(f)
		}()
	}()
}

type fileStruct struct {
	file *os.File
}

// 赋值给结构体的成员，该结构体作为返回值
func Test9() *fileStruct {
	file := open1()
	return &fileStruct{
		file: file,
	}
}

func Test10() *fileStruct {
	fs := &fileStruct{}
	file := open1()
	fs.file = file
	return fs
}

var globalStruct *fileStruct

func Test11() {
	file := open1()
	globalStruct.file = file
}

// 场景A: 返回的os.File作为函数参数结构体的方法的传入参数
func Test12(fs *fileStruct) {
	f1 := open1()
	fs.Test12helper(f1)
}

func (fs *fileStruct) Test12helper(file *os.File) {}

// 场景B： os.File作为函数参数，该函数返回值与Return变量关联
func Test14() (net.Listener, error) {
	f := open1()
	if f != nil {
		return net.FileListener(f)
	}
	return nil, nil
}

// 场景C：fd赋值给recevier结构体的成员
func (fs *fileStruct) Test15() {
	f, err := os.Open("test")
	fs.file = f
	_ = err
}

func Test16(fs *fileStruct) error {
	f, err := os.Open("test")
	fs.file = f
	return err
}

// 场景D: fd来自传入参数net.UnixConn结构体
func test17(via *net.UnixConn) {
	f, err := via.File()
	if err != nil {
		_ = err
	}

	_ = f
	return
}

func test18(via *net.UnixConn) {
	f, err := test18helper(via)
	if err != nil {
		_ = err
	}

	_ = f
	return
}

func test18helper(via *net.UnixConn) (*os.File, error) {
	return via.File()
}

func Test19(s string) {
	f, err := os.Create(s) // want `missing "\(\*os\.File\)\.Close"`
	if err != nil {
		_ = err
	}

	_ = f
	return
}

// 场景E：返回的os.File被append给slice，返回该slice.
func Test20() []*os.File {
	var res []*os.File
	f := open1()
	res = append(res, f)
	return res
}
