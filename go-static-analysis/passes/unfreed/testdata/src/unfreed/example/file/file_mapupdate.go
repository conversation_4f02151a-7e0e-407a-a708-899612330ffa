package file

import (
	"os"
	"sync"
)

func AddLogFile(name string, path string) error {
	_log.mu.Lock()
	defer _log.mu.Unlock()

	logger := &Logger{name: name, path: path}
	f, err := os.OpenFile(logger.path, os.O_CREATE|os.O_APPEND|os.O_RDWR, 0666)
	if err == nil {
		logger.fd = f
		_log.out[name] = logger
	}
	return err
}

var _log = &LoggerBase{
	out:         make(map[string]*Logger),
	backupCount: 0,
}

type LoggerBase struct {
	buf         []byte // for accumulating text to write
	backupCount int
	mu          sync.Mutex         // ensures atomic writes; protects the following fields
	out         map[string]*Logger // destination for output
}

type Logger struct {
	fd   *os.File
	name string
	path string
}
