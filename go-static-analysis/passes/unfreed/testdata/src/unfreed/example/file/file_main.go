package file

import (
	"os"
	"os/signal"
	"runtime/pprof"
	"syscall"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

func setupSignal() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT)
	signal.Notify(c, syscall.SIGHUP)
	signal.Notify(c, syscall.SIGUSR1)
	signal.Notify(c, syscall.SIGUSR2)
	signal.Notify(c, syscall.SIGTTIN)
	signal.Notify(c, syscall.SIGTTOU)
	go func() {
		for sig := range c {
			switch sig {
			case syscall.SIGUSR1:
				f, err := os.Create("apollo_cpu.prof")
				if err != nil {
					golog.Error("%v", err)
				}
				// defer f.Close()
				if err = pprof.StartCPUProfile(f); err != nil {
					golog.Error("start cpu profile fail err: %s", err.<PERSON>rror())
				}
			}
		}
	}()
}
