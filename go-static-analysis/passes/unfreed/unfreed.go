package unfreed

import (
	"fmt"
	"os"
	"strings"

	"golang.org/x/tools/go/ssa"
	"golang.org/x/tools/go/ssa/ssautil"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/ifnil/ifnilutil"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/unfreed/config"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/unfreed/function"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/unfreed/rule"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/unfreed/unfreedutil"
)

var whiteList = []string{}

func getYParams(yItem *rule.YItem, resource *function.Var) []*function.Var {
	res := []*function.Var{}
	for i := range yItem.Params {
		if yItem.Params[i] == '-' {
			res = append(res, nil)
		} else {
			res = append(res, resource)
		}
	}
	return res
}

func findYItem(d *function.Digest, r *rule.Rule, i int, yItem *rule.YItem, resource *function.Var) bool {
	yParams := getYParams(yItem, resource)
	if yParams == nil {
		return false
	}

	i += 1

	cfg := function.FindConfig{
		MaxDepth: r.MaxDepth,
		Escape:   r.Escape,
	}
	return d.FindCall(cfg, i, yItem.Fn, yParams, r)
}

func doRule(d *function.Digest, r *rule.Rule, om *unfreedutil.OutputMessages, f *ssa.Function) {
	for i := 0; i < len(d.CallInstrs); i += 1 {
		callInstr := d.CallInstrs[i]
		for _, result := range callInstr.Results {
			if result.T.String() == r.Resource {
				// find Y
				found := false
				for j := 0; j < len(r.Y); j += 1 {
					if findYItem(d, r, i, r.Y[j], result.V) {
						found = true
						break
					}
					if ok := d.DeferCall[r.Y[j].Fn]; ok {
						found = true
						break
					}
				}

				if !found {
					arr := []string{}
					for _, yItem := range r.Y {
						arr = append(arr, fmt.Sprintf("%q", yItem.Fn))
					}

					pkg := f.Pkg.Pkg
					diag := om.GenDiagnostic(callInstr.Orig.Pos(), "[netdisk-sa][%s]: missing %s", r.RuleName, strings.Join(arr, " or "))
					om.Add(f.Pkg.Prog.Fset, pkg.Path(), pkg.Name(), diag)
				}
			}
		}
	}
}

var formatIsText bool

func Run(option *unfreedutil.Option) {
	if option != nil && option.OutputFormat == "text" {
		formatIsText = true
	} else {
		formatIsText = false
	}

	dir, err := os.Getwd()
	if err != nil {
		fmt.Println("os.Getwd() failed.")
		return
	}

	config.Configure()
	prog, err := unfreedutil.BuildSSA()
	if err != nil {
		ifnilutil.SendWarnMessageToHi("[unfreed]buildssa失败: " + dir + "\n err: " + err.Error())
		return
	}

	//jsonTree := make(unfreedutil.JSONTree) // 用于存储sa.json信息
	om := &unfreedutil.OutputMessages{
		FormatIsText: formatIsText,
	}

	if !om.FormatIsText {
		om.JsonMessages = make(map[string]map[string][]interface{})
	}

	funcs := ssautil.AllFunctions(prog)

	fileMsg := unfreedutil.NewFileMsgs()
	if err := fileMsg.Init(); err != nil {
		msg := fmt.Sprintf("[unfreed规则][func name: fileMsg.Init()][err: %s]: ", err.Error())
		unfreedutil.SendWarnMessageToHi(msg)
		return
	}

	for f := range funcs {
		file := f.Prog.Fset.Position(f.Pos()).Filename
		if !fileMsg.FilesName[file] {
			continue
		}

		d := function.GetDigest(f)
		if d == nil {
			continue
		}

		for _, r := range config.Config.Rules {
			doRule(d, r, om, f)
		}
	}

	if err := om.Output(); err != nil {
		fmt.Println("err: ", err)
	}
}
