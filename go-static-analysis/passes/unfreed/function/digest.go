package function

import (
	"go/constant"
	"go/token"
	"go/types"
	"log"
	"strings"
	"sync"

	"golang.org/x/tools/go/ssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/unfreed/rule"
)

var mDigest map[*ssa.Function]*Digest = map[*ssa.Function]*Digest{}
var mu sync.Mutex

// 如果传入参数为以下类型，函数体内开辟的os.File与该参数有关联，也会report。
var paramTypeFilter = []string{
	// 创建os.File
	"string",
	"int",
	"FileMode",
	"uintptr",
}

func GetDigest(f *ssa.Function) *Digest {
	mu.Lock()
	defer mu.Unlock()
	return getDigest(f)
}

func getDigest(f *ssa.Function) *Digest {
	if _, ok := mDigest[f]; !ok {
		mDigest[f] = newDigest(f)
	}
	return mDigest[f]
}

type VarKey struct {
	Val   ssa.Value
	Idx   int
	Const interface{}
}

type Var struct {
	K       VarKey
	From    []*Var
	Capture *Var
}

type Digest struct {
	Params   []*Var
	FreeVars []*Var

	Vars map[VarKey]*Var

	Instrs     []ssa.Instruction
	CallInstrs []*CookedCallInstr
	Closures   []*ssa.MakeClosure

	EscapeGlobal map[*Var]map[*Var]bool
	EscapeOuter  map[*Var]map[*Var]bool
	Returns      map[*ssa.Return]map[*Var]bool

	DelayEscapedGlobal map[*Var]*Var // 用于延期执行
	DelayEscapedOuter  map[*Var]*Var
	DelayReturns       map[*ssa.Return]bool

	mVarSet   map[*Var]map[*Var]bool
	DeferCall map[string]bool
}

type Result struct {
	V *Var
	T types.Type
}

type CookedCallInstr struct {
	F       *ssa.Function
	Name    string
	Params  []*Var
	Results []*Result

	Orig ssa.CallInstruction
}

func (d *Digest) cookCallInstr(callInstr ssa.CallInstruction) *CookedCallInstr {
	res := &CookedCallInstr{
		Params:  []*Var{},
		Results: []*Result{},
		Orig:    callInstr,
	}

	com := callInstr.Common()
	sig := com.Signature()
	var args []ssa.Value

	if com.IsInvoke() {
		// dynamic method call ("invoke" mode)
		//
		// In this mode, Value is the interface value and Method is the interface's
		// abstract method. Note: an abstract method may be shared by multiple interfaces
		// due to embedding; Value.Type() provides the specific interface used for this call.
		//
		// Value is implicitly supplied to the concrete method implementation as the receiver
		// parameter; in other words, Args[0] holds not the receiver but the first true argument.
		args = append([]ssa.Value{com.Value}, com.Args...)
		res.Name = com.Method.FullName()
	} else {
		switch v := com.Value.(type) {
		case *ssa.Builtin: // built-in function call
			return nil

		case *ssa.MakeClosure: // static function closure call
			args = com.Args
			res.F = v.Fn.(*ssa.Function)
			res.Name = res.F.String()

		case *ssa.Function:
			// Args contains the arguments to the call. If Value is a method,
			// Args[0] contains the receiver parameter.
			if v.Signature.Recv() != nil { // static method call
				args = com.Args
			} else { // static function call
				args = com.Args
			}
			res.F = v
			res.Name = res.F.String()

		default: // dynamic function call
			return nil
		}
	}

	for _, arg := range args {
		res.Params = append(res.Params, d.getVar(arg))
	}

	if callInstr.Value() != nil {
		tpl := sig.Results()
		if tpl.Len() == 1 {
			res.Results = append(res.Results, &Result{
				T: tpl.At(0).Type(),
			})
		} else if tpl.Len() > 1 {
			for i := 0; i < tpl.Len(); i += 1 {
				res.Results = append(res.Results, &Result{
					T: tpl.At(i).Type(),
				})
			}
		}
	}
	return res
}

func (d *Digest) GetVarByKey(k VarKey) *Var {
	return d.getVarByKey(k)
}

func (d *Digest) getVar(val ssa.Value) *Var {
	return d.getVarByKey(VarKey{Val: val})
}

func (d *Digest) getVarByKey(k VarKey) *Var {
	if cst, ok := k.Val.(*ssa.Const); ok {
		k = VarKey{Const: constant.Val(cst.Value)}
	}

	if _, ok := d.Vars[k]; !ok {
		v := &Var{k, []*Var{}, nil}
		d.Vars[k] = v
	}

	return d.Vars[k]
}

func (d *Digest) GetVarSet(v *Var) map[*Var]bool {
	return d.getVarSet(v)
}

func (d *Digest) getVarSet(v *Var) map[*Var]bool {
	if _, ok := d.mVarSet[v]; ok {
		return d.mVarSet[v]
	}

	res := map[*Var]bool{}
	x := v
	q := []*Var{v}
	for len(q) > 0 {
		x, q = q[0], q[1:]
		if _, ok := res[x]; ok {
			continue // to avoid infinite loop
		}
		res[x] = true
		for i := range x.From {
			v := x.From[i]
			if !res[v] {
				q = append(q, v)
			}
		}
	}
	d.mVarSet[v] = res
	return res
}

func (d *Digest) MayBeIdentical(a, b *Var) bool {
	sa, sb := d.getVarSet(a), d.getVarSet(b)
	for v := range sb {
		if _, ok := sa[v]; ok {
			return true
		}
	}
	return false
}

func makeClosure(fn *ssa.Function, bindings []*Var) {
	d := getDigest(fn)
	// len(d.FreeVars) == len(bindings)
	for i := 0; i < len(bindings); i += 1 {
		d.FreeVars[i].Capture = bindings[i]
	}
}

func newDigest(f *ssa.Function) *Digest {
	// external
	if f.Blocks == nil {
		return nil
	}

	d := &Digest{
		Params:             []*Var{},
		FreeVars:           []*Var{},
		Vars:               map[VarKey]*Var{},
		Instrs:             []ssa.Instruction{},
		CallInstrs:         []*CookedCallInstr{},
		Closures:           []*ssa.MakeClosure{},
		EscapeGlobal:       map[*Var]map[*Var]bool{},
		EscapeOuter:        map[*Var]map[*Var]bool{},
		Returns:            map[*ssa.Return]map[*Var]bool{},
		DelayEscapedGlobal: map[*Var]*Var{},
		DelayEscapedOuter:  map[*Var]*Var{},
		DelayReturns:       map[*ssa.Return]bool{},
		mVarSet:            map[*Var]map[*Var]bool{},
		DeferCall:          map[string]bool{},
	}
	for _, param := range f.Params {
		d.Params = append(d.Params, d.getVar(param))
	}
	for _, freeVar := range f.FreeVars {
		d.FreeVars = append(d.FreeVars, d.getVar(freeVar))
	}

	for _, block := range f.DomPreorder() {
		d.Instrs = append(d.Instrs, block.Instrs...)
	}

	for i := 0; i < len(d.Instrs); i += 1 {
		switch instr := d.Instrs[i].(type) {
		case *ssa.Alloc:
			d.getVar(instr)
		case *ssa.BinOp:
			d.getVar(instr)
			d.getVar(instr.X)
			d.getVar(instr.Y)
		case ssa.CallInstruction:
			if call, ok := instr.(*ssa.Defer); ok {
				if ok := d.DeferCall[call.Common().Value.String()]; !ok {
					d.DeferCall[call.Common().Value.String()] = true
					if closure, ok := call.Call.Value.(*ssa.MakeClosure); ok {
						d.handleDeferClosure(closure)
					}
				}
			}
			if call, ok := instr.(*ssa.Call); ok {
				callVar := d.getVar(call)

				if call.Call.IsInvoke() {
					v := d.getVar(call.Call.Value)
					v.From = append(v.From, callVar)
					callVar.From = append(callVar.From, v)
				}

				for _, arg := range call.Call.Args {
					argVar := d.getVar(arg)
					callVar.From = append(callVar.From, argVar)
					argVar.From = append(argVar.From, callVar)
				}
			}

			d.getVar(instr.Common().Value)
			for _, v := range instr.Common().Args {
				d.getVar(v)
			}
			cooked := d.cookCallInstr(instr)
			if cooked != nil {
				d.CallInstrs = append(d.CallInstrs, cooked)
			}
		case *ssa.ChangeInterface:
			v1 := d.getVar(instr)
			v2 := d.getVar(instr.X)
			v1.From = []*Var{v2}
		case *ssa.ChangeType:
			v1 := d.getVar(instr)
			v2 := d.getVar(instr.X)
			v1.From = []*Var{v2}
		case *ssa.Convert:
			v1 := d.getVar(instr)
			v2 := d.getVar(instr.X)
			v1.From = []*Var{v2}
		case *ssa.DebugRef:
		case *ssa.Extract:
			v1 := d.getVar(instr)
			v2 := d.getVarByKey(VarKey{Val: instr.Tuple, Idx: instr.Index + 1})
			v1.From = append(v1.From, v2)
			v2.From = append(v2.From, v1)

			d.getVar(instr.Tuple)
		case *ssa.Field:
			v1 := d.getVar(instr)
			v2 := d.getVar(instr.X)
			v1.From = append(v1.From, v2)
			v2.From = append(v2.From, v1)
		case *ssa.FieldAddr:
			v1 := d.getVar(instr)
			v2 := d.getVar(instr.X)
			v1.From = append(v1.From, v2)
			v2.From = append(v2.From, v1)
		case *ssa.If:
			d.getVar(instr.Cond)
		case *ssa.Index:
			d.getVar(instr)
			d.getVar(instr.X)
			d.getVar(instr.Index)
		case *ssa.IndexAddr:
			v1 := d.getVar(instr)
			v2 := d.getVar(instr.X)
			v2.From = append(v2.From, v1)
			d.getVar(instr.Index)
		case *ssa.Jump:
		case *ssa.Lookup:
			d.getVar(instr)
			d.getVar(instr.X)
			d.getVar(instr.Index)
		case *ssa.MakeChan:
			d.getVar(instr)
			d.getVar(instr.Size)
		case *ssa.MakeClosure:
			d.getVar(instr.Fn)
			bindings := []*Var{}
			for _, v := range instr.Bindings {
				bindings = append(bindings, d.getVar(v))
			}
			makeClosure(instr.Fn.(*ssa.Function), bindings)
			d.Closures = append(d.Closures, instr)
		case *ssa.MakeInterface:
			v1 := d.getVar(instr)
			v2 := d.getVar(instr.X)
			v1.From = append(v1.From, v2)
			v2.From = append(v2.From, v1)
			//v1.From = []*Var{v2}
		case *ssa.MakeMap:
			d.getVar(instr)
			d.getVar(instr.Reserve)
		case *ssa.MakeSlice:
			d.getVar(instr)
			d.getVar(instr.Len)
			d.getVar(instr.Cap)
		case *ssa.MapUpdate:
			v1 := d.getVar(instr.Map)
			d.getVar(instr.Key)
			v2 := d.getVar(instr.Value)
			v1.From = append(v1.From, v2)
			v2.From = append(v2.From, v1)

		case *ssa.Next:
			d.getVar(instr)
			d.getVar(instr.Iter)
		case *ssa.Panic:
			d.getVar(instr.X)
		case *ssa.Phi:
			v := d.getVar(instr)
			for i := 0; i < len(instr.Edges); i += 1 {
				v.From = append(v.From, d.getVar(instr.Edges[i]))
			}
		case *ssa.Range:
			d.getVar(instr)
			d.getVar(instr.X)
		case *ssa.Return:
			d.DelayReturns[instr] = true

		case *ssa.RunDefers:
		case *ssa.Select:
			d.getVar(instr)
			for i := 0; i < len(instr.States); i += 1 {
				d.getVar(instr.States[i].Chan)
				d.getVar(instr.States[i].Send)
			}
		case *ssa.Send:
			d.getVar(instr.Chan)
			d.getVar(instr.X)
		case *ssa.Slice:
			v1 := d.getVar(instr)
			v2 := d.getVar(instr.X)
			v1.From = append(v1.From, v2)
			d.getVar(instr.Low)
			d.getVar(instr.High)
			d.getVar(instr.Max)
		case *ssa.SliceToArrayPointer:
			d.getVar(instr)
			d.getVar(instr.X)
		case *ssa.Store:
			v1 := d.getVar(instr.Addr)
			v2 := d.getVar(instr.Val)
			v1.From = append(v1.From, v2)
			v2.From = append(v2.From, v1)

			v3 := v1
			if fieldAddr, ok := instr.Addr.(*ssa.FieldAddr); ok {
				v3 = d.getVar(fieldAddr.X)
			}
			if _, ok := v3.K.Val.(*ssa.Global); ok {
				d.DelayEscapedGlobal[v2] = v1
			} else {
				for _, v := range append(d.Params, d.FreeVars...) {
					if v == v3 {
						d.EscapeOuter[v2] = d.getVarSet(v1)
					}
				}
			}
		case *ssa.TypeAssert:
			d.getVar(instr)
			d.getVar(instr.X)
		case *ssa.UnOp:
			v1 := d.getVar(instr)
			v2 := d.getVar(instr.X)
			switch instr.Op {
			case token.MUL:
				v1.From = append(v1.From, v2)
				v2.From = append(v2.From, v1)
				if _, ok := v2.K.Val.(*ssa.Global); ok {
					d.DelayEscapedGlobal[v2] = v1
				}
			}
		default:
			log.Printf("unknown instruction: %s", instr)
		}
	}

	d.handleDelayEscapedGlobal()
	d.handleDelayEscapeOuter()
	d.handleDelayReturns()

	for _, callInstr := range d.CallInstrs {
		if len(callInstr.Results) == 0 {
			continue
		}
		if len(callInstr.Results) == 1 {
			if v, ok := d.Vars[VarKey{Val: callInstr.Orig.Value()}]; ok {
				callInstr.Results[0].V = v
			}
			continue
		}
		for i, result := range callInstr.Results {
			if v, ok := d.Vars[VarKey{Val: callInstr.Orig.Value(), Idx: i + 1}]; ok {
				result.V = v
			}
		}
	}

	return d
}

func (d *Digest) handleDeferClosure(closure *ssa.MakeClosure) {
	if f, ok := closure.Fn.(*ssa.Function); ok {
		for _, block := range f.Blocks {
			for _, instr := range block.Instrs {
				switch instr := instr.(type) {
				case ssa.CallInstruction:
					if ok := d.DeferCall[instr.Common().Value.String()]; !ok {
						d.DeferCall[instr.Common().Value.String()] = true
					}
				}
			}
		}
	}
}

func (d *Digest) handleDelayEscapedGlobal() {
	for v2, v1 := range d.DelayEscapedGlobal {
		d.EscapeGlobal[v2] = d.getVarSet(v1)
	}
}

func (d *Digest) handleDelayEscapeOuter() {
	for _, v := range append(d.Params, d.FreeVars...) {
		val := v.K.Val

		inFilter := false
		for _, s := range paramTypeFilter {
			if strings.HasSuffix(val.Type().String(), s) {
				inFilter = true
				break
			}
		}
		if !inFilter {
			d.EscapeOuter[v] = d.getVarSet(v)
		}
	}

	for v2, v1 := range d.DelayEscapedOuter {
		d.EscapeOuter[v2] = d.getVarSet(v1)
	}
}

func (d *Digest) handleDelayReturns() {
	for instr := range d.DelayReturns {
		m := map[*Var]bool{}
		for _, val := range instr.Results {
			for v := range d.getVarSet(d.getVar(val)) {
				m[v] = true
			}
		}
		d.Returns[instr] = m
	}
}

func escapedMM(mm []map[*Var]bool, vs []*Var) bool {
	for _, v := range vs {
		if v == nil {
			continue
		}
		escaped := false
		for _, m := range mm {
			if _, ok := m[v]; ok {
				escaped = true
				break
			}
		}
		if !escaped {
			return false
		}
	}
	return true
}

func (d *Digest) EscapedGlobal(vs []*Var) bool {
	mm := []map[*Var]bool{}
	for _, m := range d.EscapeGlobal {
		mm = append(mm, m)
	}
	return escapedMM(mm, vs)
}

func (d *Digest) EscapedOuter(vs []*Var) bool {
	mm := []map[*Var]bool{}
	for _, m := range d.EscapeOuter {
		mm = append(mm, m)
	}
	return escapedMM(mm, vs)
}

func (d *Digest) EscapedReturn(vs []*Var) bool {
	for _, m := range d.Returns {
		escaped := true
		for _, v := range vs {
			if v == nil {
				continue
			}
			if _, ok := m[v]; !ok {
				escaped = false
				break
			}
		}
		if escaped {
			return true
		}
	}
	return false
}

func (d *Digest) MatchCall(i int, fn string, params []*Var, r *rule.Rule) bool {
	for ; i < len(d.CallInstrs); i += 1 {
		if d.meetTheFilterFunc(i, fn, params, r) {
			return true
		}

		if d.matchTheFunc(i, fn, params, r) {
			return true
		}
	}
	return false
}

// 该函数作用为：遇到  runtime/pprof.StartCPUProfile(t10) 等函数，这些函数在里面起了 groutine 调了 fd，因此 fd 在外部不能Close
func (d *Digest) meetTheFilterFunc(i int, fn string, params []*Var, r *rule.Rule) bool {

	/*
	   t6 = os.Create("apollo_cpu.prof":string)              (*os.File, error)
	   t7 = extract t6 #0                                             *os.File
	   t8 = extract t6 #1                                                error
	   t9 = t8 != nil:error                                               bool
	   t10 = make io.Writer <- *os.File (t7)                         io.Writer
	   t11 = runtime/pprof.StartCPUProfile(t10)                          error
	*/

	callInstr := d.CallInstrs[i]
	for _, meetingFunc := range r.FiltWhenMeetFunc {
		if callInstr.Name != meetingFunc {
			continue
		}

		for i := 0; i < len(params); i++ {
			for j := 0; j < len(callInstr.Params); j++ {
				if d.MayBeIdentical(params[i], callInstr.Params[j]) {
					return true
				}
			}
		}

	}

	return false
}

func (d *Digest) matchTheFunc(i int, fn string, params []*Var, r *rule.Rule) bool {
	callInstr := d.CallInstrs[i]
	if callInstr.Name == fn && len(params) == len(callInstr.Params) {
		found := true
		for k := 0; k < len(params); k += 1 {
			if params[k] != nil && !d.MayBeIdentical(params[k], callInstr.Params[k]) {
				found = false
				break
			}
		}
		if found {
			return true
		}
	}
	return false
}

func (d *Digest) MatchClosures(fn string, params []*Var) bool {
	for i := range d.Closures {
		closure := d.Closures[i]
		f := closure.Fn.(*ssa.Function)
		dOther := getDigest(f)

		for _, callInstr := range dOther.CallInstrs {
			if matchCallInstr(d, dOther, fn, params, callInstr) {
				return true
			}
		}
	}
	return false
}

// 检查闭包内的函数调用是否为f.Close. 其中d表示创建该闭包所在函数， dOther表示该闭包，
// freeVarCapture和paramsl在d内创建，因此要传d进来，
// 因为c和freeVar是闭包对应结构体digest创建
// 通过 d.MayBeIdentical(params[k], freeVar.Capture) && dOther.MayBeIdentical(callInstr.Params[k], freeVar)
func matchCallInstr(d *Digest, dOther *Digest, fn string, params []*Var, callInstr *CookedCallInstr) bool {
	if callInstr.Name != fn || len(params) != len(callInstr.Params) {
		return false
	}

	for k := 0; k < len(params); k++ { // 比对params和 callInstr.Params对应参数是否有关联关系
		found := false
		for _, freeVar := range dOther.FreeVars {
			if params[k] != nil && d.MayBeIdentical(params[k], freeVar.Capture) && dOther.MayBeIdentical(callInstr.Params[k], freeVar) {
				found = true
			}
		}
		if !found {
			return false
		}
	}

	return true
}

func (d *Digest) passParam(dOther *Digest, callInstr *CookedCallInstr, param *Var, r *rule.Rule) *Var {
	if param.K.Const != nil { // param is a constant
		return dOther.GetVarByKey(param.K)
	}

	for i := range callInstr.Params {
		if d.MayBeIdentical(param, callInstr.Params[i]) {
			val := callInstr.Params[i].K.Val
			if val == nil {
				continue
			}

			s := val.Type().String()
			if !d.isParamMatch(s, r) {
				continue
			}

			return dOther.Params[i]
		}
	}

	for i := range dOther.FreeVars {
		if d.MayBeIdentical(param, dOther.FreeVars[i].Capture) {
			freeVar := dOther.FreeVars[i].Capture.K.Val
			if !strings.Contains(freeVar.Type().String(), r.Resource) {
				continue
			}

			return dOther.FreeVars[i]
		}
	}

	return nil
}

func (d *Digest) isParamMatch(paramType string, r *rule.Rule) bool {
	if strings.Contains(paramType, r.Resource) {
		return true
	}

	for i := range r.Y {
		item := r.Y[i]
		if strings.Contains(paramType, item.Type) {
			return true
		}
	}

	return false
}

func (d *Digest) PassParams(dOther *Digest, callInstr *CookedCallInstr, params []*Var, r *rule.Rule) []*Var {
	paramsOther := []*Var{}
	for _, param := range params {
		if param == nil {
			paramsOther = append(paramsOther, nil)
			continue
		}
		paramOther := d.passParam(dOther, callInstr, param, r)
		if paramOther == nil { // the param is not passed
			return nil
		}
		paramsOther = append(paramsOther, paramOther)
	}
	return paramsOther
}

type FindConfig struct {
	MaxDepth int
	Escape   bool
	depth    int
}

func (d *Digest) FindCall(cfg FindConfig, i int, fn string, params []*Var, r *rule.Rule) bool {
	// match call in current function
	if d.MatchCall(i, fn, params, r) {
		return true
	}

	if d.MatchClosures(fn, params) {
		return true
	}

	// has yParams escaped in current function?
	if cfg.Escape && (d.EscapedGlobal(params) || d.EscapedOuter(params) || d.EscapedReturn(params)) {
		return true
	}

	cfg.depth += 1

	// step into
	for ; i < len(d.CallInstrs); i += 1 {
		if d.stepInto(cfg, d.CallInstrs[i], fn, params, r) {
			return true
		}
	}

	return false
}

func (d *Digest) stepInto(cfg FindConfig, callInstr *CookedCallInstr, fn string, yParams []*Var, r *rule.Rule) bool {
	if cfg.depth > cfg.MaxDepth { // too deep
		return false
	}

	if callInstr.F == nil { // can not step into
		return false
	}

	dOther := GetDigest(callInstr.F)
	if dOther == nil {
		return false
	}

	yParamsOther := d.PassParams(dOther, callInstr, yParams, r)
	if yParamsOther == nil { // not passed
		return false
	}

	return dOther.findYItemR(cfg, fn, yParamsOther, r)
}

func (d *Digest) findYItemR(cfg FindConfig, fn string, params []*Var, r *rule.Rule) bool {
	// match call in current function
	if d.MatchCall(0, fn, params, r) {
		return true
	}

	// has params escaped in current function?
	if cfg.Escape && d.EscapedGlobal(params) {
		return true
	}

	cfg.depth += 1

	// step into
	for _, callInstr := range d.CallInstrs {
		if d.stepInto(cfg, callInstr, fn, params, r) {
			return true
		}
	}

	return false
}
