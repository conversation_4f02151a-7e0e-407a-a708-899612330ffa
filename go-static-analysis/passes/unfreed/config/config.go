package config

import (
	"flag"
	"sync"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/unfreed/rule"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

type Conf struct {
	Rules []*rule.Rule `json:"rules"`
}

var Config Conf

func getConfigFile() string {
	res := "unfreed.json"
	flg := flag.Lookup("conf")
	if flg != nil && flg.Value.String() != "" {
		res = flg.Value.String()
	}
	return res
}

var once sync.Once

// text 格式为插件，插件应写死配置文件到代码里
func Configure() {
	once.Do(func() {
		err := util.LoadConf(getConfigFile(), &Config)
		if err != nil {
			setConf(&Config)
		}
	})
}

func setConf(c *Conf) {
	var r1 = &rule.Rule{
		RuleName: "FdClose",
		Resource: "*os.File",
		Y: []*rule.YItem{
			{
				Type:   "io.Closer",
				Fn:     "(io.Closer).Close",
				Params: "x",
			},
			{
				Type:   "*os.File",
				Fn:     "(*os.File).Close",
				Params: "x",
			},
		},

		MaxDepth: 3,
		Escape:   true,
		FiltWhenMeetFunc: []string{
			"runtime/pprof.StartCPUProfile",
		},
	}

	var r2 = &rule.Rule{
		RuleName: "BodyClose",
		Resource: "*net/http.Response",
		Y: []*rule.YItem{
			{
				Type:   "io.Closer",
				Fn:     "(io.Closer).Close",
				Params: "x",
			},
		},

		MaxDepth: 3,
		Escape:   true,
	}

	var r3 = &rule.Rule{
		RuleName: "RowsClose",
		Resource: "*database/sql.Rows",
		Y: []*rule.YItem{
			{
				Type:   "*database/sql.Rows",
				Fn:     "(*database/sql.Rows).Close",
				Params: "x",
			},
			{
				Type:   "io.Closer",
				Fn:     "(io.Closer).Close",
				Params: "x",
			},
		},

		MaxDepth: 3,
		Escape:   true,
	}

	c.Rules = append(c.Rules, r1, r2, r3)
}
