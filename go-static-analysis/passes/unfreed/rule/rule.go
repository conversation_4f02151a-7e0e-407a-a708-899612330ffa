package rule

type YItem struct {
	Type   string `json:"type"`
	Fn     string `json:"fn"`
	Params string `json:"params"`
}

type Rule struct {
	RuleName         string   `json:"rule_name"`
	Resource         string   `json:"resource"`
	Y                []*YItem `json:"y"`
	Escape           bool     `json:"escape"`
	MaxDepth         int      `json:"max_depth"`
	FiltWhenMeetFunc []string `json:"filter_func"` // 当对应资源在该函数中有调用时，则过滤，应对 fd close 在 StartCPUProfile(f) 的 case.
}
