package printssa

import (
	"log"
	"strings"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/buildssa"
)

var Analyzer = &analysis.Analyzer{
	Name: "printssa",
	Doc:  "printssa",
	Requires: []*analysis.Analyzer{
		buildssa.Analyzer,
	},
	Run: run,
}

func run(pass *analysis.Pass) (interface{}, error) {
	s := pass.ResultOf[buildssa.Analyzer].(*buildssa.SSA)

	var sb strings.Builder

	_, err := s.Pkg.WriteTo(&sb)
	if err != nil {
		panic(err)
	}

	for _, f := range append(s.SrcFuncs, s.Pkg.Func("init")) {
		_, err = f.WriteTo(&sb)
		if err != nil {
			panic(err)
		}
	}

	log.Printf("%s\n", sb.String())
	return nil, nil
}
