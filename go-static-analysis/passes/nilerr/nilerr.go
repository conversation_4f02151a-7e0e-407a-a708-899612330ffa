package nilerr

import (
	"fmt"
	"go/token"
	"go/types"
	"strings"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/buildssa"
	"golang.org/x/tools/go/ssa"
)

var Analyzer = &analysis.Analyzer{
	Name: "nilerr",
	Doc:  Doc,
	Run:  run,
	Requires: []*analysis.Analyzer{
		buildssa.Analyzer,
	},
}

const Doc = "nilerr checks not continue when err is not nil"

var errType = types.Universe.Lookup("error").Type().Underlying().(*types.Interface)

func run(pass *analysis.Pass) (interface{}, error) {
	pssa := pass.ResultOf[buildssa.Analyzer].(*buildssa.SSA)
	funcs := pssa.SrcFuncs

	reportFail := func(v ssa.Value, pos token.Pos, format string) {
		errLines := getValueLineNumbers(pass, v)
		var errLineText string

		if len(errLines) == 1 {
			errLineText = fmt.Sprintf("line %d", errLines[0])
		} else {
			errLineText = fmt.Sprintf("lines %v", errLines)
		}

		pass.Reportf(pos, format, errLineText)
	}

	for i := range funcs {
		nextBool := false
		scanBool := false

		for _, block := range funcs[i].Blocks {
			for _, instr := range block.Instrs {
				switch instr.(type) {
				case *ssa.Call:
					if strings.Contains(instr.String(), "(*database/sql.Rows).Next") {
						nextBool = true
					}
					if strings.Contains(instr.String(), "(*database/sql.Rows).Scan") {
						scanBool = true
					}
				}
			}
		}

		if nextBool && scanBool {
			for _, block := range funcs[i].Blocks {
				if v := binOpErrNil(block, token.NEQ); v != nil {
					if ret := isContinue(block.Succs[0]); ret != -1 {
						reportFail(v, ret, "In func "+funcs[i].Name()+" error is not nil (%s) but continue")
					}
				}
			}
		}
	}

	return nil, nil
}

func getValueLineNumbers(pass *analysis.Pass, v ssa.Value) []int {
	if phi, ok := v.(*ssa.Phi); ok {
		result := make([]int, 0, len(phi.Edges))
		for _, edge := range phi.Edges {
			result = append(result, getValueLineNumbers(pass, edge)...)
		}
		return result
	}

	value := v
	if extract, ok := value.(*ssa.Extract); ok {
		value = extract.Tuple
	}

	pos := value.Pos()
	return []int{pass.Fset.File(pos).Line(pos)}
}

func getNodeLineNumber(pass *analysis.Pass, node ssa.Node) int {
	pos := node.Pos()
	return pass.Fset.File(pos).Line(pos)
}

func binOpErrNil(b *ssa.BasicBlock, op token.Token) ssa.Value {
	if len(b.Instrs) == 0 {
		return nil
	}

	ifinst, ok := b.Instrs[len(b.Instrs)-1].(*ssa.If)
	if !ok {
		return nil
	}

	str := ""
	for _, val := range ifinst.Block().Instrs {
		str += val.String()
	}
	if !strings.Contains(str, "*database/sql.Rows") {
		return nil
	}

	binop, ok := ifinst.Cond.(*ssa.BinOp)
	if !ok {
		return nil
	}

	if binop.Op != op {
		return nil
	}

	if !types.Implements(binop.X.Type(), errType) {
		return nil
	}

	if !types.Implements(binop.Y.Type(), errType) {
		return nil
	}

	xIsConst, yIsConst := isConst(binop.X), isConst(binop.Y)
	switch {
	case !xIsConst && yIsConst: // err != nil or err == nil
		return binop.X
	case xIsConst && !yIsConst: // nil != err or nil == err
		return binop.Y
	}

	return nil
}

func isConst(v ssa.Value) bool {
	_, ok := v.(*ssa.Const)
	return ok
}

func isContinue(b *ssa.BasicBlock) token.Pos {
	if len(b.Instrs) == 0 {
		return -1
	}

	for _, instr := range b.Instrs {
		if strings.Contains(instr.String(), "(*database/sql.Rows).Err") {
			return -1
		}

		if strings.Contains(instr.String(), "(*database/sql.Rows).Next") {
			return instr.Pos()
		}
	}

	ret, ok := b.Instrs[len(b.Instrs)-1].(*ssa.Jump)
	if !ok {
		return -1
	}

	return ret.Pos()
}
