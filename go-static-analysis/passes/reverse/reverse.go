package reverse

import (
	"errors"
	"go/ast"
	"go/token"
	"strconv"
	"sync"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/inspect"
)

var WhiteList = []string{}

var Analyzer = &analysis.Analyzer{
	Name: "reverse",
	Doc:  "check for reverse",
	Requires: []*analysis.Analyzer{
		inspect.Analyzer,
	},
	Run: run,
}

type Key = string //IdentName
type Value struct {
	IsX bool
	// 当父节点为二元表达式时，以下两个值才有意义
	// 否则 ParentOp = token.ILLEGAL, ParentOpPos = -1.
	ParentOp    token.Token //抽象语法树中标识符的父节点Op, 即二元表达式符号
	ParentOpPos token.Pos   //上述符号的位置
}

type IdentMap struct {
	Map   map[Key]*Value
	Mutex *sync.Mutex
}

func (identMap *IdentMap) set(key Key, value *Value) {
	identMap.Mutex.Lock()
	defer identMap.Mutex.Unlock()

	identMap.Map[key] = value
}

func (identMap *IdentMap) get(key Key) (*Value, bool) {
	identMap.Mutex.Lock()
	defer identMap.Mutex.Unlock()

	value, ok := identMap.Map[key]
	return value, ok
}

func run(pass *analysis.Pass) (interface{}, error) {
	for _, f := range pass.Files {
		ast.Inspect(f, func(node ast.Node) bool {
			forStmt, ok := node.(*ast.ForStmt)
			if !ok {
				return true
			}

			identMap := &IdentMap{
				Map:   make(map[Key]*Value),
				Mutex: new(sync.Mutex),
			}

			//把for循环第二项（Cond，即判断语句）下的所有标识符Name以及对应的Value指针存到IdentMap
			identMap.saveIdentOf(forStmt.Cond)

			if post, ok := forStmt.Post.(*ast.IncDecStmt); ok {
				//for循环第三项为自增自减流程
				doWithPostIsIncDecStmt(post, identMap, pass)
			}
			if post, ok := forStmt.Post.(*ast.AssignStmt); ok {
				// for循环第三项为赋值表达式，里面只检查赋值表达式右边为INT字面量的四则运算
				doWithPostIsAssignStmt(post, identMap, pass)
			}

			identMap = nil
			return true

		})
	}
	return nil, nil
}

func doWithPostIsIncDecStmt(post *ast.IncDecStmt, identMap *IdentMap, pass *analysis.Pass) {
	postX, ok := post.X.(*ast.Ident)
	if !ok {
		return
	}

	key := postX.Name
	value, ok := identMap.get(key)
	if !ok {
		//pass.Reportf(post.X.Pos(), "Can`t find post.X in for-condition")
		return
	}

	if value.IsX {
		// GTR means ">", GEQ means ">=", INC means "++".
		// LSS means "<", LEQ means "<=", DEC means "--".
		if (value.ParentOp == token.LSS || value.ParentOp == token.LEQ) && post.Tok == token.DEC {
			// example : for i := 0; i < 10; i-- {}
			pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the "<" or "<=" in for-condition statement is reverse. `)
		}
		if (value.ParentOp == token.GTR || value.ParentOp == token.GEQ) && post.Tok == token.INC {
			// example : for i := 0; i > 10; i++ {}
			pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the ">" or ">=" in for-condition statement is reverse. `)
		}
	} else {
		if (value.ParentOp == token.LSS || value.ParentOp == token.LEQ) && post.Tok == token.INC {
			// example : for i := 0; 10 < i; i++ {}
			pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the "<" or "<=" in for-condition statement is reverse. `)
		}
		if (value.ParentOp == token.GTR || value.ParentOp == token.GEQ) && post.Tok == token.DEC {
			// example : for i := 0; 10 > i; i-- {}
			pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the ">" or ">=" in for-condition statement is reverse. `)
		}
	}
}

func doWithPostIsAssignStmt(post *ast.AssignStmt, identMap *IdentMap, pass *analysis.Pass) {
	if len(post.Lhs) != 1 {
		return
	}
	lhs, ok := post.Lhs[0].(*ast.Ident)
	if !ok {
		return
	}

	if len(post.Rhs) != 1 {
		return
	}
	rhsValue, err := computeExpr(post.Rhs[0])
	if err != nil {
		return
	}

	isPostive := (rhsValue > 0)

	key := lhs.Name
	value, ok := identMap.get(key)
	if !ok {
		return
	}

	if value.IsX {
		// GTR means ">", GEQ means ">=", INC means "++".
		// LSS means "<", LEQ means "<=", DEC means "--".
		if value.ParentOp == token.LSS || value.ParentOp == token.LEQ {
			if post.Tok == token.ADD_ASSIGN && !isPostive {
				//example : for i := 0; i < 10; i += (2 - 4) {}
				pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the "<" or "<=" in for-condition statement is reverse. `)
			}
			if post.Tok == token.SUB_ASSIGN && isPostive {
				//example : for i := 0; i < 10; i -= (2 + 4) {}
				pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the "<" or "<=" in for-condition statement is reverse. `)
			}
		}

		if value.ParentOp == token.GTR || value.ParentOp == token.GEQ {
			if post.Tok == token.ADD_ASSIGN && isPostive {
				//example : for i := 0; i >= 10; i += (2 + 4) {}
				pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the ">" or ">=" in for-condition statement is reverse. `)

			}

			if post.Tok == token.SUB_ASSIGN && !isPostive {
				// example : for i := 0; i > 10; i -= (2 - 4) {}
				pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the ">" or ">=" in for-condition statement is reverse. `)
			}
		}
	} else {
		if value.ParentOp == token.LSS || value.ParentOp == token.LEQ {
			if post.Tok == token.ADD_ASSIGN && isPostive {
				//example : for i := 0; 10 < i; i += (2 + 4) {}
				pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the "<" or "<=" in for-condition statement is reverse. `)
			}
			if post.Tok == token.SUB_ASSIGN && !isPostive {
				//example : for i := 0; 10 < i; i -= (2 - 4) {}
				pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the "<" or "<=" in for-condition statement is reverse. `)
			}
		}

		if value.ParentOp == token.GTR || value.ParentOp == token.GEQ {
			if post.Tok == token.ADD_ASSIGN && !isPostive {
				//example : for i := 0; 10 > i; i += (2 - 4) {}
				pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the ">" or ">=" in for-condition statement is reverse. `)

			}
			if post.Tok == token.SUB_ASSIGN && isPostive {
				// example : for i := 0; 10 > i; i -= (2 + 4) {}
				pass.Reportf(value.ParentOpPos, `[netdisk-sa][Reverse]: the ">" or ">=" in for-condition statement is reverse. `)
			}
		}
	}

}

// 计算INT字面量的四则运算结果
func computeExpr(expr ast.Expr) (int, error) {
	parenExpr, ok := expr.(*ast.ParenExpr)
	if ok {
		return computeExpr(parenExpr.X)
	}

	binaryExpr, ok := expr.(*ast.BinaryExpr)
	if !ok {
		if basicLitExpr, ok := expr.(*ast.BasicLit); ok {
			if basicLitExpr.Kind == token.INT {
				res, err := strconv.Atoi(basicLitExpr.Value)
				if err != nil {
					return 0, err
				}
				return res, nil
			}
		}
		return 0, errors.New("the type of expr is not BinaryExpr.")
	}

	l, err := computeExpr(binaryExpr.X)
	if err != nil {
		return 0, err
	}

	r, err := computeExpr(binaryExpr.Y)
	if err != nil {
		return 0, err
	}

	switch binaryExpr.Op {
	case token.ADD:
		return l + r, nil
	case token.SUB:
		return l - r, nil
	case token.MUL:
		return l * r, nil
	case token.QUO:
		return l / r, nil
	default:
		return 0, errors.New("the binaryExpr.Op is not in {+, -, *, /}.")
	}

}

// 把以nodeOrExpr节点为根节点的语法树下的Ident存到IdentMap里
func (identMap *IdentMap) saveIdentOf(nodeOrExpr interface{}) {
	identMap.saveIdentOfFallback(nodeOrExpr, false, token.ILLEGAL, -1)
}

func (identMap *IdentMap) saveIdentOfFallback(nodeOrExpr interface{}, isX bool, parentOp token.Token, parentOpPos token.Pos) {
	if parent, ok := nodeOrExpr.(*ast.ParenExpr); ok {
		identMap.saveIdentOfFallback(parent.X, false, token.ILLEGAL, -1)
	}

	if parent, ok := nodeOrExpr.(*ast.BinaryExpr); ok {
		identMap.saveIdentOfFallback(parent.X, true, parent.Op, parent.Pos())
		identMap.saveIdentOfFallback(parent.Y, false, parent.Op, parent.Pos())
	}

	if node, ok := nodeOrExpr.(*ast.Ident); ok {
		key := node.Name

		value := &Value{
			IsX:         isX,
			ParentOp:    parentOp,
			ParentOpPos: parentOpPos,
		}

		identMap.set(key, value)
	}
}
