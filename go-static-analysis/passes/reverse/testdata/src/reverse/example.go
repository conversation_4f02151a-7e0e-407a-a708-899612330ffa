package reverse

func doSomeThing() {}

func test() {
	for i := 0; i > 10; i -= (2 - (2 * 3)) { // want `the ">" or ">=" in for-condition statement is reverse. `
	}
}

func test0() {
	i := 0
	for j := 0; j < 10; i-- {
		doSomeThing()
	}
}

func test1() {
	for i := 0; i < 10; i-- { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test2(len int) {
	for i := 0; i < len; i-- { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test3() {
	for i := 0; 10 > i; i-- { // want `the ">" or ">=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test4(len int) {
	for i := 0; len > i; i-- { // want `the ">" or ">=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test5() {
	for i := 0; i > 10; i++ { // want `the ">" or ">=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test6(len int) {
	for i := 0; i > len; i++ { // want `the ">" or ">=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test7() {
	for i := 0; 10 < i; i++ { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test8(len int) {
	for i := 0; len < i; i++ { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test9() {
	i, j := 0, 0
	for ; i < 10 && j < 10; j -= 2 { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}
func test10() {
	i, j := 0, 0
	for ; i < 10 && j < 10; j -= 2 { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test11() {
	i, j := 20, 0
	for ; i > 10 && j < 10; i++ { // want `the ">" or ">=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test12() {
	i, j := 0, 0
	for ; i > 10 && j < 10; j-- { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test13() {
	i, j := 0, 0
	for ; i < 5 && j > 1; i-- { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test14() {
	i := 0
	for ; i < 20; i -= 2 { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test15() {
	i1, i2 := 0, 0
	for ; i1 < 10 && i2 < 10; i1-- { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}
func test16() {
	i1, i2 := 0, 0
	for ; i1 < 10 && i2 < 10; i2-- { // want `the "<" or "<=" in for-condition statement is reverse. `
		doSomeThing()
	}
}

func test17() {
	for i := 0; ; i-- {

	}
}
