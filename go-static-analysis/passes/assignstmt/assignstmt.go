package assignstmt

import (
	"bytes"
	"go/ast"
	"go/format"
	"go/token"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/inspect"
	"golang.org/x/tools/go/ast/inspector"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

/*
1、只针对for-range这种场景下，如果是赋值语句&右侧包含了range的变量(tmpFileMeta)下，必须包含meta变量或间接变量，否则认为重复计算。
	tmpFileMeta := []FileMetaA{}
	for _, meta := range tmpFileMeta {
			if strings.Compare(":", meta.Path) == 2 {
				tmpTargetFromPath := strings.Split(":", tmpFileMeta[0].Path)[1]  //重复计算
				......
			}
	}
2、将range提取的kv存储，将涉及的range变量作为基础变量。扫描rang循环内的每条语句，提取里面的*ast.AssignStmt左侧的*ast.Ident，作为间接变量。
3、对于*ast.AssignStmt右侧的变量逐层递归遍历，发现仅包含上述range的变量时报告。
*/

var whiteList = []string{
	// baidu/netdisk/nd-go-clouddisk-lite
	"netdisk/nd-go-clouddisk-lite/lib/db/db_handler.go",

	//"baidu/netdisk/nd-sharedir",
	"/netdisk/nd-sharedir/action/acl/auth.go",
}

var Analyzer = &analysis.Analyzer{
	Name: "assignstmt",
	Doc:  "assignstmt cal case",
	Requires: []*analysis.Analyzer{
		inspect.Analyzer,
	},
	Run: run,
}

type AssignStmt struct {
	Left  []ast.Expr
	Right []ast.Expr
}

var ident = ast.Ident{
	NamePos: 0,
	Name:    "",
	Obj:     nil,
}
var builtin = []string{"append", "errors", "buf", "make", "len", "new"}

func run(pass *analysis.Pass) (interface{}, error) {
	inspect := pass.ResultOf[inspect.Analyzer].(*inspector.Inspector)

	nodeFilter := []ast.Node{
		(*ast.RangeStmt)(nil),
		(*ast.ForStmt)(nil),
	}

	inspect.Preorder(nodeFilter, func(n ast.Node) {
		varSet := make(map[string]*ast.Ident, 64)
		curRangeVal := make(map[string]*ast.Ident, 1)
		//assStmt := []AssignStmt{}
		var body *ast.BlockStmt

		switch n := n.(type) {

		//类似 for i=0;i<GetValLength(arr);i++ {}
		/*
			case *ast.ForStmt:
				if bin, ok := n.Cond.(*ast.BinaryExpr); ok {
					if call, ok := bin.Y.(*ast.CallExpr); ok {
						if funIdent, ok := call.Fun.(*ast.Ident); ok {
							if n.Init != nil && funIdent.Name != "len" {
								pass.Reportf(n.Cond.Pos(), `Repeat Calc`)
							}
						}
					}
				}*/

		case *ast.RangeStmt:
			body = n.Body
			addVar(n.Key, varSet)
			addVar(n.Value, varSet)

			rangVal := getRangeVal(n)
			if rangVal == nil {
				return
			}

			curRangeVal[rangVal.Name] = rangVal
			for _, v := range builtin {
				varSet[v] = &ident
			}

			for _, stmt := range body.List {
				var st []AssignStmt
				switch stmt.(type) {

				//循环内声明的变量
				case *ast.DeclStmt:
					getAssignByDeclStmt(stmt.(*ast.DeclStmt), varSet)

				case *ast.ForStmt:
					st = getAssignByForStmt(stmt.(*ast.ForStmt), varSet)
					handleAssigns(pass, st, varSet, curRangeVal)

				case *ast.IfStmt:
					st = getAssignByIfStmt(pass, stmt.(*ast.IfStmt), varSet)
					handleAssigns(pass, st, varSet, curRangeVal)

				case *ast.RangeStmt:
					addVar(n.Key, varSet)
					addVar(n.Value, varSet)

				case *ast.AssignStmt:
					temp := AssignStmt{
						Left:  stmt.(*ast.AssignStmt).Lhs,
						Right: stmt.(*ast.AssignStmt).Rhs,
					}
					handleAssign(pass, temp, varSet, curRangeVal)
				}
			}
		}

	})

	return nil, nil
}

func handleAssigns(pass *analysis.Pass, assigns []AssignStmt, varSet map[string]*ast.Ident, curRangeVal map[string]*ast.Ident) {
	for _, ass := range assigns {
		handleAssign(pass, ass, varSet, curRangeVal)
	}
}

func handleAssign(pass *analysis.Pass, ass AssignStmt, varSet map[string]*ast.Ident, curRangeVal map[string]*ast.Ident) {
	for _, expr := range ass.Right {
		//过滤保存下来的赋值语句，暂时只对函数调用、二进制表达式、下标场景检查
		if !isAcceptableNestedExpr(pass, expr, curRangeVal) {
			continue

		}
		switch expr.(type) {
		case *ast.CallExpr:
			checkExpr(pass, expr, varSet, ass, curRangeVal)
		case *ast.BinaryExpr:
			checkExpr(pass, expr, varSet, ass, curRangeVal)
		case *ast.IndexExpr:
			checkExpr(pass, expr, varSet, ass, curRangeVal)
		}

		//每个表达式检查前，都初始化到之前状态
		for k := range curRangeVal {
			if _, ok := varSet[k]; ok {
				delete(varSet, k)
			}
		}
	}

	//后面赋值语句有可能会遇到，此处将左侧的变量都保存下。
	for _, expr := range ass.Left {
		addVar(expr, varSet)
	}
}

/*
func addVar(expr ast.Expr, varSet map[string]*ast.Ident) {
	if id, ok := expr.(*ast.Ident); ok {
		_, ok = varSet[id.Name]
		if !ok && id.Name != "_" {
			varSet[id.Name] = id
		}
	}

	if id, ok := expr.(*ast.IndexExpr); ok {
		if x, ok := id.X.(*ast.Ident); ok {
			_, ok = varSet[x.Name]
			if !ok && x.Name != "_" {
				varSet[x.Name] = x
			}
		}

		if index, ok := id.Index.(*ast.Ident); ok {
			_, ok = varSet[index.Name]
			if !ok && index.Name != "_" {
				varSet[index.Name] = index
			}
		}
	}

	if id, ok := expr.(*ast.SelectorExpr); ok {
		if x, ok := id.X.(*ast.Ident); ok {
			_, ok = varSet[x.Name]
			if !ok && x.Name != "_" {
				varSet[x.Name] = x
			}
		}
	}
}
*/

func addVar(expr ast.Expr, varSet map[string]*ast.Ident) {
	key := parseStmtName(expr)
	val := parseStmtIdent(expr)
	if key != "" && key != "_" {
		varSet[key] = val
	}
}

// 解析一个复合结构的名称， 作为集合的key
func parseStmtName(stmt ast.Node) string {
	switch n := stmt.(type) {
	case *ast.Ident:
		return n.Name
	case *ast.IndexExpr:
		return parseStmtName(n.X) + parseStmtName(n.Index)
	case *ast.SelectorExpr:
		return parseStmtName(n.X) + parseStmtName(n.Sel)
	}
	return ""
}

// 解析一个符合结构的Ident（因为里面有多个Ident,取其中一个ident), 作为集合的value
func parseStmtIdent(stmt ast.Node) *ast.Ident {
	switch n := stmt.(type) {
	case *ast.Ident:
		return n
	case *ast.IndexExpr:
		return parseStmtIdent(n.X)
	case *ast.SelectorExpr:
		return parseStmtIdent(n.X)
	}
	return nil
}

func queryVar(stmt ast.Node, m map[string]*ast.Ident) (string, bool) {
	switch n := stmt.(type) {
	case *ast.Ident:
		if _, ok := m[n.Name]; ok {
			return n.Name, true
		}
		return n.Name, false
	case *ast.IndexExpr:
		x, xok := queryVar(n.X, m)
		if xok {
			return x, true
		}
		idx, yok := queryVar(n.Index, m)
		if yok {
			return idx, true
		}
		if _, ok := m[x+idx]; ok {
			return x + idx, true
		}
		return x + idx, false

	case *ast.SelectorExpr:
		x, xok := queryVar(n.X, m)
		if xok {
			return x, true
		}

		sel, yok := queryVar(n.Sel, m)
		if yok {
			return sel, true
		}

		if _, ok := m[x+sel]; ok {
			return x + sel, true
		}

		return x + sel, false
	}
	return "", false
}

func getRangeVal(n *ast.RangeStmt) (rangVal *ast.Ident) {
	rangVal = nil
	//暂时只解析下面三种表达式
	temp, ok := n.X.(*ast.Ident)
	if ok {
		rangVal = temp
	}

	temp1, ok := n.X.(*ast.SliceExpr)
	if ok {
		if temp2, ok := temp1.X.(*ast.Ident); ok {
			rangVal = temp2
		}
	}

	temp3, ok := n.X.(*ast.SelectorExpr)
	if ok {
		if temp4, ok := temp3.X.(*ast.Ident); ok {
			rangVal = temp4
		}
	}

	return rangVal
}

func getAssignByDeclStmt(stmt *ast.DeclStmt, varSet map[string]*ast.Ident) {
	if genDel, ok := stmt.Decl.(*ast.GenDecl); ok {
		for _, y := range genDel.Specs {
			if values, ok := y.(*ast.ValueSpec); ok {
				for _, name := range values.Names {
					addVar(name, varSet)
				}
			}
		}
	}
}

func getAssignByForStmt(stmt *ast.ForStmt, varSet map[string]*ast.Ident) []AssignStmt {
	var st []AssignStmt

	if v, ok := stmt.Init.(*ast.AssignStmt); ok {
		for _, vv := range v.Lhs {
			addVar(vv, varSet)
		}
	}

	for _, forstmtBody := range stmt.Body.List {
		if forAssign, ok := forstmtBody.(*ast.AssignStmt); ok {
			temp := AssignStmt{
				Left:  forAssign.Lhs,
				Right: forAssign.Rhs,
			}
			st = append(st, temp)
		}

		if forDel, ok := forstmtBody.(*ast.DeclStmt); ok {
			if genDel, ok := forDel.Decl.(*ast.GenDecl); ok {
				for _, y := range genDel.Specs {
					if values, ok := y.(*ast.ValueSpec); ok {
						for _, name := range values.Names {
							addVar(name, varSet)
						}
					}
				}
			}
		}
	}

	return st
}

func getAssignByIfStmtBreak(pass *analysis.Pass, expr *ast.IfStmt, m map[string]*ast.Ident) bool {
	if cond, ok := expr.Cond.(*ast.BinaryExpr); ok {
		if x, ok := cond.X.(*ast.Ident); ok {
			if isAcceptableNestedExpr(pass, x, m) {
				return true
			}
		}

		if y, ok := cond.Y.(*ast.Ident); ok {
			if isAcceptableNestedExpr(pass, y, m) {
				return true
			}
		}
	}
	return false
}

func getAssignByIfStmt(pass *analysis.Pass, stmt *ast.IfStmt, varSet map[string]*ast.Ident) []AssignStmt {
	var st []AssignStmt

	if getAssignByIfStmtBreak(pass, stmt, varSet) {
		return nil
	}

	if vv, ok := stmt.Init.(*ast.AssignStmt); ok {
		for _, vvv := range vv.Lhs {
			if vvvv, ok := vvv.(*ast.Ident); ok {
				addVar(vvvv, varSet)
			}
		}
	}

	for _, ifstmtBody := range stmt.Body.List {
		if ifAssign, ok := ifstmtBody.(*ast.AssignStmt); ok {
			temp := AssignStmt{
				Left:  ifAssign.Lhs,
				Right: ifAssign.Rhs,
			}
			st = append(st, temp)
		}

		if forDel, ok := ifstmtBody.(*ast.DeclStmt); ok {
			if genDel, ok := forDel.Decl.(*ast.GenDecl); ok {
				for _, y := range genDel.Specs {
					if values, ok := y.(*ast.ValueSpec); ok {
						for _, name := range values.Names {
							addVar(name, varSet)
						}
					}
				}
			}
		}
	}

	if v, ok := stmt.Else.(*ast.BlockStmt); ok {
		for _, ifstmtBody := range v.List {
			if ifAssign, ok := ifstmtBody.(*ast.AssignStmt); ok {
				temp := AssignStmt{
					Left:  ifAssign.Lhs,
					Right: ifAssign.Rhs,
				}
				st = append(st, temp)
			}

			if forDel, ok := ifstmtBody.(*ast.DeclStmt); ok {
				if genDel, ok := forDel.Decl.(*ast.GenDecl); ok {
					for _, y := range genDel.Specs {
						if values, ok := y.(*ast.ValueSpec); ok {
							for _, name := range values.Names {
								addVar(name, varSet)
							}
						}
					}
				}
			}
		}
	}

	return st
}

func checkExpr(pass *analysis.Pass, node ast.Node, rangeVal map[string]*ast.Ident, ass AssignStmt, curRangeVal map[string]*ast.Ident) {
	ret := false
	if !isAcceptableNestedExpr(pass, node, rangeVal) {
		//如果左侧包含基础或间接变量，不认为是重复计算
		for _, expr := range ass.Left {
			for k, v := range curRangeVal {
				rangeVal[k] = v
			}
			if isAcceptableNestedExpr(pass, expr, rangeVal) {
				ret = true
				break
			}
		}

		if !ret {
			path := util.FinePathName(pass, node.Pos())
			if !util.IsInWhiteList(path, whiteList) {
				pass.Reportf(node.Pos(), `[netdisk-sa]: for-range循环中存在和循环不相关的赋值计算，具有潜在bug风险，建议提到循环外 %s`, formatNode(node))
			}
		}
	}
}

func formatNode(node ast.Node) string {
	buf := new(bytes.Buffer)
	if err := format.Node(buf, token.NewFileSet(), node); err != nil {
		return ""
	}

	return buf.String()
}

// 判断是否在rangeVal集合里
func isAcceptableNestedExpr(pass *analysis.Pass, n ast.Node, rangeVal map[string]*ast.Ident) bool {
	if _, ok := queryVar(n, rangeVal); ok {
		return true
	}

	switch e := n.(type) {
	case *ast.BinaryExpr:
		return isAcceptableNestedExpr(pass, e.X, rangeVal) || isAcceptableNestedExpr(pass, e.Y, rangeVal)
	case *ast.UnaryExpr:
		return isAcceptableNestedExpr(pass, e.X, rangeVal)
	case *ast.Ident:
		_, ok := rangeVal[e.Name]
		if ok {
			return true
		}
		return false
	case *ast.CallExpr:
		return astCallExpr(pass, n.(*ast.CallExpr), rangeVal)
	case *ast.SelectorExpr:
		return isAcceptableNestedExpr(pass, e.X, rangeVal) || isAcceptableNestedExpr(pass, e.Sel, rangeVal)
	case *ast.AssignStmt:
		for _, v := range e.Rhs {
			if isAcceptableNestedExpr(pass, v, rangeVal) {
				return true
			}
		}
		return false
	case *ast.StarExpr:
		return isAcceptableNestedExpr(pass, e.X, rangeVal)
	case *ast.ParenExpr:
		return isAcceptableNestedExpr(pass, e.X, rangeVal)
	case *ast.SliceExpr:
		return isAcceptableNestedExpr(pass, e.X, rangeVal) || isAcceptableNestedExpr(pass, e.High, rangeVal) || isAcceptableNestedExpr(pass, e.Low, rangeVal)
	case *ast.TypeAssertExpr:
		return isAcceptableNestedExpr(pass, e.X, rangeVal)
	case *ast.IndexExpr:
		return isAcceptableNestedExpr(pass, e.X, rangeVal) || isAcceptableNestedExpr(pass, e.Index, rangeVal)
	case *ast.CompositeLit:
		return astCompositeLit(pass, n, rangeVal)
	default:
		return false
	}
}

func astCallExpr(pass *analysis.Pass, n *ast.CallExpr, rangeVal map[string]*ast.Ident) bool {
	if v, ok := n.Fun.(*ast.Ident); ok {
		_, ok := rangeVal[v.Name]
		if ok {
			return true
		}
	}

	if v, ok := n.Fun.(*ast.SelectorExpr); ok {
		_, ok := rangeVal[v.Sel.Name]
		if ok {
			return true
		}

		if vv, ok := v.X.(*ast.Ident); ok {
			_, ok := rangeVal[vv.Name]
			if ok {
				return true
			}
		}

		if isAcceptableNestedExpr(pass, v.X, rangeVal) {
			return true
		}
	}

	for _, arg := range n.Args {
		if isAcceptableNestedExpr(pass, arg, rangeVal) {
			return true
		}
	}
	return false
}

func astCompositeLit(pass *analysis.Pass, n ast.Node, rangeVal map[string]*ast.Ident) bool {
	if elts, ok := n.(*ast.CompositeLit); ok {
		if typev, ok := elts.Type.(*ast.Ident); ok {
			_, ok := rangeVal[typev.Name]
			if ok {
				return true
			}
		}
		for _, v := range elts.Elts {
			if vv, ok := v.(*ast.Ident); ok {
				_, ok := rangeVal[vv.Name]
				if ok {
					return true
				}
			}

			if vv, ok := v.(*ast.KeyValueExpr); ok {
				if vvv, ok := vv.Value.(*ast.Ident); ok {
					_, ok := rangeVal[vvv.Name]
					if ok {
						return true
					}
				}

				if vvv, ok := vv.Value.(*ast.IndexExpr); ok {
					if vvv, ok := vvv.X.(*ast.Ident); ok {
						_, ok := rangeVal[vvv.Name]
						if ok {
							return true
						}
					}
				}
			}
		}
	}

	return false
}
