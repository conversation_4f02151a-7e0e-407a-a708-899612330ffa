package assignstmt

import (
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
)

type FileMeta struct {
	Path string
}

func varLen(arr []int) int {
	return len(arr)
}

func test() {
	arr := []int{1, 2, 3}
	for _, v := range arr {
		m := arr[0] + arr[1] // want `Repeat assignment`
		log.Println(m, v)
	}

	tmpFileMeta := []FileMeta{}
	for _, meta := range tmpFileMeta {
		if strings.Compare(":", meta.Path) == 2 {
			tmpTargetFromPath := strings.Split(":", tmpFileMeta[0].Path)[1] // want `Repeat assignment`
			log.Println(tmpTargetFromPath, meta)
		}
	}
}

func test2() {
	m := []*http.Request{}
	for _, v := range m {
		a, errE := strconv.ParseUint(v.Host, 10, 64)
		if errE != nil {

		}

		err := helper(a)
		if err != nil {
			_ = dosth(m[0])
		}
	}
}

func helper(len uint64) error {
	return nil
}

func dosth(n *http.Request) error {
	fmt.Println("fmt")
	return nil
}
