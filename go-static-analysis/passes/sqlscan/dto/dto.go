package dto

type SQLDataUploadRequest struct {
	Repo      string                            `json:"repo"`
	RD        string                            `json:"rd"`
	Result    []*SQLDataUploadRequestResultItem `json:"result"`
	CommitURL string                            `json:"commit_url"`
}

type SQLDataUploadRequestResultItem struct {
	SQL      string `json:"sql"`
	FileName string `json:"filename"`
	FnName   string `json:"fn_name"`
	Key      string `json:"key"`
	Type     string `json:"type"`

	Instance string `json:"instance"`
	Database string `json:"database"` // 数据库名称

}
