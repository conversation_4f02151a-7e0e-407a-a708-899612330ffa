package base

import (
	"go/ast"
	"go/parser"
	"go/token"
)

type ResultInfo struct {
	FileName string // 文件名
	FuncName string // 函数名
	Key      string
	SQL      string // 生成的 SQL
	Type     string

	OriginDBName string // 云控名称
	TableName    string
	Instance     string
	Database     string
}

// 1) 一个函数里面有多个 SQL 调用, 调用也可能会重叠
// 2) 一个函数对应一个调用（如 easy)
func BuildResultInfo(fileName string, funcName string, key string, sql string, typ string) *ResultInfo {
	return &ResultInfo{
		FileName: fileName,
		FuncName: funcName,
		Key:      key,
		Type:     typ,
		SQL:      sql,
	}
}

func (result *ResultInfo) AddExtraInfo(originDBName string, tableName string, instance string, database string) {
	result.OriginDBName = originDBName
	result.TableName = tableName
	result.Instance = instance
	result.Database = database
}

func IsEasyProject() bool {
	fileSet := token.NewFileSet()
	f, err := parser.ParseFile(fileSet, "main.go", nil, 0)
	if err != nil {
		return false
	}

	var hasEasyStart bool
	var hasTangramMode bool
	ast.Inspect(f, func(node ast.Node) bool {
		// 找到 EasyStart 函数就不再调 oundEasyStart
		if !hasEasyStart && foundEasyStart(node) {
			hasEasyStart = true
		}
		// 找到 TangramMode 函数就不再调foundEasyStart
		if !hasTangramMode && foundEasyModule(node) {
			hasTangramMode = true
		}

		return true
	})

	return hasEasyStart && hasTangramMode
}

func foundEasyStart(node ast.Node) bool {
	callExpr, ok := node.(*ast.CallExpr)
	if !ok {
		return false
	}
	selectorExpr, ok := callExpr.Fun.(*ast.SelectorExpr)
	if !ok {
		return false
	}
	x, ok := selectorExpr.X.(*ast.Ident)
	if !ok {
		return false
	}
	sel := selectorExpr.Sel
	if x.Name == "easy" && sel.Name == "Start" {
		return true
	}

	return false
}

func foundEasyModule(node ast.Node) bool {
	callExpr, ok := node.(*ast.CallExpr)
	if !ok {
		return false
	}
	selectorExpr, ok := callExpr.Fun.(*ast.SelectorExpr)
	if !ok {
		return false
	}
	x, ok := selectorExpr.X.(*ast.Ident)
	if !ok {
		return false
	}
	sel := selectorExpr.Sel
	if x.Name == "tangram_module" && sel.Name == "NewModule" {
		return true
	}

	return false
}
