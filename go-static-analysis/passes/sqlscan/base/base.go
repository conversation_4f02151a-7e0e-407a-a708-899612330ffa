package base

import (
	"fmt"
	"strings"

	"golang.org/x/tools/go/ssa"
)

type RuleContext struct {
	Prog     *ssa.Program
	Pkg      *ssa.Package
	RepoName string
	ICode    string
}

type Arg struct {
	Value  ssa.Value     `json:"-"`
	Packge *ssa.Package  `json:"-"` // 参数所在的包
	Call   *ssa.Call     `json:"-"` // 为对应的函数调用里的参数
	Fn     *ssa.Function `json:"-"` // Arg 在哪个函数里面被构造
	Index  int
	Len    int
}

type Caller struct {
	Call       *ssa.Call
	CallName   string
	Args       []*Arg
	ParentCall *Caller
}

func NewArg(value ssa.Value, index int, len int, packge *ssa.Package) *Arg {
	return &Arg{
		Value:  value,
		Packge: packge,
		Index:  index,
		Len:    len,
	}
}

func BuildArgs(call *ssa.Call) []*Arg {
	if call == nil {
		return nil
	}
	// fmt.Println("call string: ", call.String(), ", call.Call: ", call.Call)
	args := call.Call.Args
	len := len(args)
	pkg := call.Parent().Pkg

	res := make([]*Arg, 00)
	for i, v := range args {
		arg := NewArg(v, i, len, pkg)
		res = append(res, arg)
	}

	return res

}

func BuildCaller(call *ssa.Call, parent *Caller) *Caller {
	args := BuildArgs(call)
	return &Caller{
		Call:       call,
		CallName:   call.String(),
		Args:       args,
		ParentCall: parent,
	}
}

func (c *Caller) String() string {
	p := c

	var sb strings.Builder
	depth := 0
	for p != nil {

		var blank string
		for i := 0; i < depth; i++ {
			blank += "  "
		}
		str := fmt.Sprintf("%vdepth: %d, call name: %v\n", blank, depth, p.Call.String())
		sb.WriteString(str)

		p = p.ParentCall
		depth++

	}
	return sb.String()

}

func NewFuncContext(fn *ssa.Function) *FuncContext {
	return &FuncContext{
		Fn: fn,
	}

}

type FuncContext struct {
	Fn        *ssa.Function
	CallIndex int // 第几个函数调用
}
