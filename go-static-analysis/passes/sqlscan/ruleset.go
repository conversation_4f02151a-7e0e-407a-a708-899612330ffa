package sqlscan

import (
	"fmt"

	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/rule/clouddisk"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/rule/easy"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/rule/gorm"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/rule/raw"
)

type SQLRule int

const (
	SQLRuleGROM           SQLRule = 1 << iota
	SQLRuleEasy                   // easy
	SQLRuleClouddiskGoLib         // "icode.baidu.com/baidu/netdisk/clouddisk-golib/db"

	SQLRuleNative // 裸写 sql

	SQLRuleNone
)

type FileContext struct {
	Rule SQLRule
}

type ISQLScanner interface {
	Init(ctx *FileContext) error
	Run() error
}

type RuleContext struct {
	Prog *ssa.Program
	Pkg  *ssa.Package
}

func (r *SQLScanner) runRuleEasyOnPkg(prog *ssa.Program, pkg *ssa.Package) {
	fmt.Println("runRuleEasyOnPkg start ")
	ctx := &base.RuleContext{
		Prog:     prog,
		Pkg:      pkg,
		RepoName: r.RepoName,
		ICode:    r.ICodeName,
	}
	easyScanner := easy.NewSQLEasyScanner(128, ctx)
	easyScanner.Run()

	r.Results = append(r.Results, easyScanner.Results...)
}

func (r *SQLScanner) runRuleGROMOnPkg(prog *ssa.Program, pkg *ssa.Package) {
	ctx := &base.RuleContext{
		Prog:     prog,
		Pkg:      pkg,
		RepoName: r.RepoName,
		ICode:    r.ICodeName,
	}
	gormScanner := gorm.NewGormScanner(ctx)
	gormScanner.Run()

	r.Results = append(r.Results, gormScanner.Results...)
	return

}

func (r *SQLScanner) runRuleClouddiskGolibOnPkg(prog *ssa.Program, pkg *ssa.Package) {
	ctx := &base.RuleContext{
		Prog:     prog,
		Pkg:      pkg,
		RepoName: r.RepoName,
		ICode:    r.ICodeName,
	}
	scanner := clouddisk.NewClouddiskScanner(ctx)
	scanner.Scan(ctx)
	r.Results = append(r.Results, scanner.Results...)

}

func (r *SQLScanner) runRuleNativeOnPkg(prog *ssa.Program, pkg *ssa.Package) {
	rawScanner := raw.NewRawScanner(&base.RuleContext{
		Prog:     prog,
		Pkg:      pkg,
		RepoName: r.RepoName,
		ICode:    r.ICodeName,
	})
	if err := rawScanner.Run(); err != nil {
		fmt.Printf("rawScanner run err : %v\n", err)
	}
	r.Results = append(r.Results, rawScanner.Results...)

}
