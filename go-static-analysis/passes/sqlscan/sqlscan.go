package sqlscan

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"

	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easyfn/pkg/utils"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
	remote "icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/dto"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/easyutil"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

var BaiduRepoSubStr = "8235/baidu"

type SQLScanner struct {
	IsEasyProject bool

	Prog *ssa.Program
	Pkgs []*ssa.Package

	PkgRules map[string]SQLRule

	RepoName  string
	ICodeName string
	Rd        string

	CommitID   string
	CommitUser string

	Results []*base.ResultInfo
}

type SQLScanResult struct {
	FnName  string
	SQLStmt string
	IsDiff  bool
}

func NewSQLScanner() *SQLScanner {
	return &SQLScanner{
		PkgRules: make(map[string]SQLRule),
	}

}

func (s *SQLScanner) init() error {
	if err := s.initRepoNameAndICodeName(); err != nil {
		return err
	}
	if err := s.initProg(false, base.GetDir(s.ICodeName), []string{}); err != nil {
		return err
	}

	if err := s.initCommitIDsAndCommitUser(); err != nil {
		return err
	}

	s.IsEasyProject = base.IsEasyProject()

	if err := s.initRule(); err != nil {
		return err
	}

	if err := initLog(); err != nil {
		return err
	}

	return nil
}

func (s *SQLScanner) Scan() error {
	if err := s.init(); err != nil {
		fmt.Println("init error, err: %v", err)
		return err
	}

	if err := s.run(); err != nil {
		fmt.Println("run error, err: %v", err)
		return err
	}

	return nil
}

func (s *SQLScanner) initCommitIDsAndCommitUser() error {
	// get last long commit id for git command
	getCommitIDCmd := `git rev-parse HEAD`
	commitID, err := easyutil.ExecScript(getCommitIDCmd)
	if err != nil {
		return fmt.Errorf("get commit id failed, err is %w, output is  %s", err, commitID)
	}

	getCommitUserCmd := `git log -1 --format="%an"`
	commitUser, err := easyutil.ExecScript(getCommitUserCmd)
	if err != nil {
		return fmt.Errorf("get last commit user failed, err is %w, output is %s", err, commitUser)
	}

	getCommitMsgCmd := `git log -1 --pretty=format:'%s'`
	commitMsg, err := easyutil.ExecScript(getCommitMsgCmd)
	if err != nil {
		return fmt.Errorf("get last commit message failed, err is %w, output is %s", err, commitMsg)
	}

	s.CommitID = commitID
	s.CommitUser = commitUser
	return nil
}

func (s *SQLScanner) initRule() error {
	prog := s.Prog

	for _, pkg := range prog.AllPackages() {
		if s.IsEasyProject {
			if strings.Contains(pkg.Pkg.Path(), "dao") {
				fmt.Println("is easy project, pkg: ", pkg.Pkg.Path(), ", name: ", pkg.Pkg.Path())
				s.PkgRules[pkg.Pkg.Path()] = s.PkgRules[pkg.Pkg.Path()] | SQLRuleEasy
			}
		}

		for _, imp := range pkg.Pkg.Imports() {
			if imp.Path() == "icode.baidu.com/baidu/netdisk/clouddisk-golib/db" { // "github.com/jinzhu/gorm" {
				s.PkgRules[pkg.Pkg.Path()] = s.PkgRules[pkg.Pkg.Path()] | SQLRuleClouddiskGoLib
			}

			if imp.Path() == "gorm.io/gorm" || imp.Path() == "gorm.io/driver/mysql" {
				s.PkgRules[pkg.Pkg.Path()] = s.PkgRules[pkg.Pkg.Path()] | SQLRuleGROM
			}

			if imp.Path() == "database/sql" {
				s.PkgRules[pkg.Pkg.Path()] = s.PkgRules[pkg.Pkg.Path()] | SQLRuleNative
			}
		}

		if _, ok := s.PkgRules[pkg.Pkg.Path()]; !ok {
			s.PkgRules[pkg.Pkg.Path()] = SQLRuleNone
		}

	}

	return nil
}

func (s *SQLScanner) run() error {
	for _, pkg := range s.Pkgs {
		rule := s.PkgRules[pkg.Pkg.Path()]
		if rule&SQLRuleNone != 0 {
			continue
		}

		/*
			if rule&SQLRuleGROM != 0 {
				s.runRuleGROMOnPkg(s.Prog, pkg)
			}*/

		if rule&SQLRuleEasy != 0 {
			s.runRuleEasyOnPkg(s.Prog, pkg)
		}

		if rule&SQLRuleClouddiskGoLib != 0 {
			s.runRuleClouddiskGolibOnPkg(s.Prog, pkg)
		}

		if rule&SQLRuleNative != 0 {
			s.runRuleNativeOnPkg(s.Prog, pkg)
		}
	}

	return nil
}

func (s *SQLScanner) SendResultToServer() error {
	keyMap := make(map[string]bool)
	request := &remote.SQLDataUploadRequest{}
	for _, item := range s.Results {
		if _, exists := keyMap[item.Key]; !exists {
			resultItem := s.convertToSQLDataResultItem(item)
			request.Result = append(request.Result, resultItem)
			keyMap[item.Key] = true
		}
	}

	request.CommitURL = fmt.Sprintf("https://console.cloud.baidu-int.com/devops/icode/repos/%s/commits/%s", s.ICodeName, s.CommitID)
	request.RD = s.CommitUser
	request.Repo = s.ICodeName
	reqeustByte, err := json.Marshal(request)
	if err != nil {
		return err
	}
	requestJSON := string(reqeustByte)

	url := fmt.Sprintf("http://%s/sqldata?method=upload", "10.11.97.54:80")
	header := make(map[string]string)
	resp, err := httpclient.Post(url, header, 1000, 3000, requestJSON)
	if err != nil {
		golog.Error("send sql data to server error, [url: %v] [err: %v]", url, err)
		return err
	}

	if resp.StatusCode != 200 {
		golog.Error("send sql data to server error, [url: %v] [status: %v] [body: %v]", url, resp.StatusCode, string(resp.Body))
		return fmt.Errorf("send sql data to server error, [url: %v] [status: %v]", url, resp.StatusCode)
	}
	// "10.92.33.210:2072/codemetric?method=incre&start_time=1690770317&end_time=1690943117"

	return nil
}

func (r *SQLScanner) convertToSQLDataResultItem(item *base.ResultInfo) *remote.SQLDataUploadRequestResultItem {
	result := &remote.SQLDataUploadRequestResultItem{}
	result.FnName = item.FuncName
	result.FileName = item.FileName
	result.SQL = item.SQL
	result.Type = item.Type
	result.Key = item.Key

	result.Database = item.Database
	result.Instance = item.Instance

	return result
}

func (r *SQLScanner) initRepoNameAndICodeName() error {
	repoName, err := utils.ExecCommandWithTimeout("go list -m")
	if err != nil {
		return err
	}
	r.RepoName = strings.TrimSpace(repoName)

	icodeName, err := utils.ExecCommand(`git remote -v | head -1 | awk {'print $2'}`)
	if err != nil {
		return err
	}
	icodeName = strings.TrimSpace(icodeName)
	i := strings.Index(icodeName, BaiduRepoSubStr)
	if i == -1 || i+5 < 0 {
		return errors.New("get icode name err")
	}
	r.ICodeName = icodeName[i+5:]
	fmt.Println("initRepoNameAndICodeName repoName: ", r.RepoName, ", icode name: ", r.ICodeName)

	return nil
}

func initLog() error {
	// 和正常服务不同，这里不需要做日志切割（跑完即停止），仅记录工具上一次运行产出的结果即可
	logName := "sqlscan.log"
	logNameWf := logName + ".wf"

	// 为了避免 append 在上次运行产出的日志，每次运行先remove
	if ok := isFileExist(logName); ok {
		if err := os.Remove(logName); err != nil {
			return err
		}
	}

	if ok := isFileExist(logNameWf); ok {
		if err := os.Remove(logNameWf); err != nil {
			return err
		}
	}

	if err := golog.SetFile(logName); err != nil {
		return err
	}

	golog.SetLevel(8)
	return nil
}

func isFileExist(fileName string) bool {
	info, err := os.Stat(fileName)
	if err != nil {
		return false
	}

	if info.IsDir() {
		return false
	}

	return true
}
