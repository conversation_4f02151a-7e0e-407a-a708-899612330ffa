package sqlscan

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"

	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easyfn/pkg/utils"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
	remote "icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/dto"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/easyutil"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

var BaiduRepoSubStr = "8235/baidu"

type SQLScanner struct {
	IsEasyProject bool

	Prog *ssa.Program
	Pkgs []*ssa.Package

	PkgRules map[string]SQLRule

	RepoName  string
	ICodeName string
	Rd        string

	CommitID   string
	CommitUser string

	// Git diff相关字段
	GitDiffer    *Git<PERSON>iffer
	ChangedFiles map[string]FileChangeInfo // key: 文件路径, value: 变更信息
	LastCommitID string                    // 上次扫描的commit ID

	Results []*base.ResultInfo
}

type SQLScanResult struct {
	FnName  string
	SQLStmt string
	IsDiff  bool
}

func NewSQLScanner() *SQLScanner {
	return &SQLScanner{
		PkgRules:     make(map[string]SQLRule),
		ChangedFiles: make(map[string]FileChangeInfo),
	}
}

func (s *SQLScanner) init() error {
	if err := s.initRepoNameAndICodeName(); err != nil {
		return err
	}
	if err := s.initProg(false, base.GetDir(s.ICodeName), []string{}); err != nil {
		return err
	}

	if err := s.initCommitIDsAndCommitUser(); err != nil {
		return err
	}

	// 初始化Git diff功能
	if err := s.initGitDiff(); err != nil {
		return err
	}

	s.IsEasyProject = base.IsEasyProject()

	if err := s.initRule(); err != nil {
		return err
	}

	if err := initLog(); err != nil {
		return err
	}

	return nil
}

func (s *SQLScanner) Scan() error {
	if err := s.init(); err != nil {
		fmt.Println("init error, err: %v", err)
		return err
	}

	if err := s.run(); err != nil {
		fmt.Println("run error, err: %v", err)
		return err
	}

	return nil
}

func (s *SQLScanner) initCommitIDsAndCommitUser() error {
	// get last long commit id for git command
	getCommitIDCmd := `git rev-parse HEAD`
	commitID, err := easyutil.ExecScript(getCommitIDCmd)
	if err != nil {
		return fmt.Errorf("get commit id failed, err is %w, output is  %s", err, commitID)
	}

	getCommitUserCmd := `git log -1 --format="%an"`
	commitUser, err := easyutil.ExecScript(getCommitUserCmd)
	if err != nil {
		return fmt.Errorf("get last commit user failed, err is %w, output is %s", err, commitUser)
	}

	getCommitMsgCmd := `git log -1 --pretty=format:'%s'`
	commitMsg, err := easyutil.ExecScript(getCommitMsgCmd)
	if err != nil {
		return fmt.Errorf("get last commit message failed, err is %w, output is %s", err, commitMsg)
	}

	s.CommitID = commitID
	s.CommitUser = commitUser
	return nil
}

func (s *SQLScanner) initRule() error {
	prog := s.Prog

	for _, pkg := range prog.AllPackages() {
		if s.IsEasyProject {
			if strings.Contains(pkg.Pkg.Path(), "dao") {
				fmt.Println("is easy project, pkg: ", pkg.Pkg.Path(), ", name: ", pkg.Pkg.Path())
				s.PkgRules[pkg.Pkg.Path()] = s.PkgRules[pkg.Pkg.Path()] | SQLRuleEasy
			}
		}

		for _, imp := range pkg.Pkg.Imports() {
			if imp.Path() == "icode.baidu.com/baidu/netdisk/clouddisk-golib/db" { // "github.com/jinzhu/gorm" {
				s.PkgRules[pkg.Pkg.Path()] = s.PkgRules[pkg.Pkg.Path()] | SQLRuleClouddiskGoLib
			}

			if imp.Path() == "gorm.io/gorm" || imp.Path() == "gorm.io/driver/mysql" {
				s.PkgRules[pkg.Pkg.Path()] = s.PkgRules[pkg.Pkg.Path()] | SQLRuleGROM
			}

			if imp.Path() == "database/sql" {
				s.PkgRules[pkg.Pkg.Path()] = s.PkgRules[pkg.Pkg.Path()] | SQLRuleNative
			}
		}

		if _, ok := s.PkgRules[pkg.Pkg.Path()]; !ok {
			s.PkgRules[pkg.Pkg.Path()] = SQLRuleNone
		}

	}

	return nil
}

func (s *SQLScanner) run() error {
	for _, pkg := range s.Pkgs {
		rule := s.PkgRules[pkg.Pkg.Path()]
		if rule&SQLRuleNone != 0 {
			continue
		}

		/*
			if rule&SQLRuleGROM != 0 {
				s.runRuleGROMOnPkg(s.Prog, pkg)
			}*/

		if rule&SQLRuleEasy != 0 {
			s.runRuleEasyOnPkg(s.Prog, pkg)
		}

		if rule&SQLRuleClouddiskGoLib != 0 {
			s.runRuleClouddiskGolibOnPkg(s.Prog, pkg)
		}

		if rule&SQLRuleNative != 0 {
			s.runRuleNativeOnPkg(s.Prog, pkg)
		}
	}

	// 扫描完成后，设置变更信息
	s.setChangeInfoForResults()

	return nil
}

func (s *SQLScanner) SendResultToServer() error {
	keyMap := make(map[string]bool)
	request := &remote.SQLDataUploadRequest{}
	for _, item := range s.Results {
		if _, exists := keyMap[item.Key]; !exists {
			resultItem := s.convertToSQLDataResultItem(item)
			request.Result = append(request.Result, resultItem)
			keyMap[item.Key] = true
		}
	}

	request.CommitURL = fmt.Sprintf("https://console.cloud.baidu-int.com/devops/icode/repos/%s/commits/%s", s.ICodeName, s.CommitID)
	request.RD = s.CommitUser
	request.Repo = s.ICodeName
	reqeustByte, err := json.Marshal(request)
	if err != nil {
		return err
	}
	requestJSON := string(reqeustByte)

	url := fmt.Sprintf("http://%s/sqldata?method=upload", "10.11.97.54:80")
	header := make(map[string]string)
	resp, err := httpclient.Post(url, header, 1000, 3000, requestJSON)
	if err != nil {
		golog.Error("send sql data to server error, [url: %v] [err: %v]", url, err)
		return err
	}

	if resp.StatusCode != 200 {
		golog.Error("send sql data to server error, [url: %v] [status: %v] [body: %v]", url, resp.StatusCode, string(resp.Body))
		return fmt.Errorf("send sql data to server error, [url: %v] [status: %v]", url, resp.StatusCode)
	}
	// "10.92.33.210:2072/codemetric?method=incre&start_time=1690770317&end_time=1690943117"

	return nil
}

func (r *SQLScanner) convertToSQLDataResultItem(item *base.ResultInfo) *remote.SQLDataUploadRequestResultItem {
	result := &remote.SQLDataUploadRequestResultItem{}
	result.FnName = item.FuncName
	result.FileName = item.FileName
	result.SQL = item.SQL
	result.Type = item.Type
	result.Key = item.Key

	result.Database = item.Database
	result.Instance = item.Instance

	// 设置Git diff相关字段
	result.ChangeType = item.ChangeType
	result.PreviousSQL = item.PreviousSQL
	result.IsInChangedFile = item.IsInChangedFile

	return result
}

func (r *SQLScanner) initRepoNameAndICodeName() error {
	repoName, err := utils.ExecCommandWithTimeout("go list -m")
	if err != nil {
		return err
	}
	r.RepoName = strings.TrimSpace(repoName)

	icodeName, err := utils.ExecCommand(`git remote -v | head -1 | awk {'print $2'}`)
	if err != nil {
		return err
	}
	icodeName = strings.TrimSpace(icodeName)
	i := strings.Index(icodeName, BaiduRepoSubStr)
	if i == -1 || i+5 < 0 {
		return errors.New("get icode name err")
	}
	r.ICodeName = icodeName[i+5:]
	fmt.Println("initRepoNameAndICodeName repoName: ", r.RepoName, ", icode name: ", r.ICodeName)

	return nil
}

// setChangeInfoForResults 为扫描结果设置变更信息
func (s *SQLScanner) setChangeInfoForResults() {
	if s.GitDiffer == nil {
		// 如果Git diff功能未初始化，所有SQL标记为UNCHANGED
		for _, result := range s.Results {
			result.SetChangeInfo("UNCHANGED", "", false)
		}
		return
	}

	for _, result := range s.Results {
		// 检查文件是否在变更列表中
		isInChangedFile, changeType := s.GitDiffer.IsFileChanged(result.FileName, s.getChangedFilesList())

		if isInChangedFile {
			// 文件有变更
			if changeType == "A" {
				// 新增文件，SQL标记为NEW
				result.SetChangeInfo("NEW", "", true)
			} else if changeType == "M" {
				// 修改文件，SQL可能是新增或修改，这里简化为MODIFIED
				// 实际应该通过更精细的分析来判断SQL是新增还是修改
				result.SetChangeInfo("MODIFIED", "", true)
			} else {
				// 其他变更类型（如重命名、删除等）
				result.SetChangeInfo("MODIFIED", "", true)
			}
		} else {
			// 文件未变更，SQL标记为UNCHANGED
			result.SetChangeInfo("UNCHANGED", "", false)
		}
	}

	fmt.Printf("set change info for %d results\n", len(s.Results))
}

// getChangedFilesList 获取变更文件列表
func (s *SQLScanner) getChangedFilesList() []FileChangeInfo {
	var changes []FileChangeInfo
	for _, change := range s.ChangedFiles {
		changes = append(changes, change)
	}
	return changes
}

// initGitDiff 初始化Git diff功能
func (s *SQLScanner) initGitDiff() error {
	// 创建Git diff工具
	s.GitDiffer = NewGitDiffer(".")

	// 验证Git仓库
	if err := s.GitDiffer.ValidateGitRepo(); err != nil {
		fmt.Printf("git repo validation failed: %v\n", err)
		return err
	}

	// 获取上次扫描的commit ID
	lastCommitID, err := s.GitDiffer.GetLastScanCommit()
	if err != nil {
		fmt.Printf("get last scan commit failed: %v\n", err)
		// 不返回错误，继续执行，但不进行diff检测
		return nil
	}
	s.LastCommitID = lastCommitID

	// 获取变更文件列表
	if s.LastCommitID != "" && s.CommitID != "" {
		changes, err := s.GitDiffer.GetChangedFiles(s.LastCommitID, s.CommitID)
		if err != nil {
			fmt.Printf("get changed files failed: %v\n", err)
			// 不返回错误，继续执行
			return nil
		}

		// 构建变更文件映射
		for _, change := range changes {
			s.ChangedFiles[change.FilePath] = change
		}

		fmt.Printf("found %d changed files between %s and %s\n", len(changes), s.LastCommitID, s.CommitID)
	}

	return nil
}

func initLog() error {
	// 和正常服务不同，这里不需要做日志切割（跑完即停止），仅记录工具上一次运行产出的结果即可
	logName := "sqlscan.log"
	logNameWf := logName + ".wf"

	// 为了避免 append 在上次运行产出的日志，每次运行先remove
	if ok := isFileExist(logName); ok {
		if err := os.Remove(logName); err != nil {
			return err
		}
	}

	if ok := isFileExist(logNameWf); ok {
		if err := os.Remove(logNameWf); err != nil {
			return err
		}
	}

	if err := golog.SetFile(logName); err != nil {
		return err
	}

	golog.SetLevel(8)
	return nil
}

func isFileExist(fileName string) bool {
	info, err := os.Stat(fileName)
	if err != nil {
		return false
	}

	if info.IsDir() {
		return false
	}

	return true
}
