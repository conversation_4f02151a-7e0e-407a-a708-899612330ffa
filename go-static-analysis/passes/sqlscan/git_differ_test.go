package sqlscan

import (
	"testing"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
)

func TestGitDiffer_GetChangedFiles(t *testing.T) {
	differ := NewGitDiffer(".")

	// 测试空commit ID的情况
	changes, err := differ.GetChangedFiles("", "")
	if err != nil {
		t.<PERSON><PERSON>("Expected no error for empty commit IDs, got: %v", err)
	}
	if len(changes) != 0 {
		t.<PERSON>("Expected empty changes for empty commit IDs, got: %d", len(changes))
	}
}

func TestGitDiffer_ValidateGitRepo(t *testing.T) {
	differ := NewGitDiffer(".")

	// 测试Git仓库验证
	err := differ.ValidateGitRepo()
	if err != nil {
		t.Logf("Git repo validation failed (expected in non-git environment): %v", err)
	}
}

func TestGitDiffer_IsFileChanged(t *testing.T) {
	differ := NewGitDiffer(".")

	changes := []FileChangeInfo{
		{FilePath: "test.go", ChangeType: "M"},
		{FilePath: "new.go", ChangeType: "A"},
		{FilePath: "deleted.go", ChangeType: "D"},
	}

	// 测试文件变更检查
	isChanged, changeType := differ.IsFileChanged("test.go", changes)
	if !isChanged {
		t.Error("Expected test.go to be changed")
	}
	if changeType != "M" {
		t.Errorf("Expected change type 'M', got: %s", changeType)
	}

	// 测试未变更文件
	isChanged, changeType = differ.IsFileChanged("unchanged.go", changes)
	if isChanged {
		t.Error("Expected unchanged.go to not be changed")
	}
	if changeType != "" {
		t.Errorf("Expected empty change type, got: %s", changeType)
	}
}

func TestSQLScanner_setChangeInfoForResults(t *testing.T) {
	scanner := NewSQLScanner()

	// 模拟一些扫描结果
	scanner.Results = []*base.ResultInfo{
		{
			FileName: "dao/user.go",
			FuncName: "GetUser",
			SQL:      "SELECT * FROM users WHERE id = ?",
		},
		{
			FileName: "dao/product.go",
			FuncName: "GetProduct",
			SQL:      "SELECT * FROM products WHERE id = ?",
		},
	}

	// 模拟变更文件
	scanner.ChangedFiles = map[string]FileChangeInfo{
		"dao/user.go": {FilePath: "dao/user.go", ChangeType: "M"},
	}

	// 创建GitDiffer
	scanner.GitDiffer = NewGitDiffer(".")

	// 设置变更信息
	scanner.setChangeInfoForResults()

	// 验证结果
	if len(scanner.Results) != 2 {
		t.Errorf("Expected 2 results, got: %d", len(scanner.Results))
	}

	// 检查变更文件的标记
	userResult := scanner.Results[0]
	if userResult.ChangeType != "MODIFIED" {
		t.Errorf("Expected user result change type 'MODIFIED', got: %s", userResult.ChangeType)
	}
	if !userResult.IsInChangedFile {
		t.Error("Expected user result to be marked as in changed file")
	}

	// 检查未变更文件的标记
	productResult := scanner.Results[1]
	if productResult.ChangeType != "UNCHANGED" {
		t.Errorf("Expected product result change type 'UNCHANGED', got: %s", productResult.ChangeType)
	}
	if productResult.IsInChangedFile {
		t.Error("Expected product result to not be marked as in changed file")
	}
}

func TestSQLScanner_getChangedFilesList(t *testing.T) {
	scanner := NewSQLScanner()

	// 添加一些变更文件
	scanner.ChangedFiles = map[string]FileChangeInfo{
		"file1.go": {FilePath: "file1.go", ChangeType: "M"},
		"file2.go": {FilePath: "file2.go", ChangeType: "A"},
		"file3.go": {FilePath: "file3.go", ChangeType: "D"},
	}

	changes := scanner.getChangedFilesList()

	if len(changes) != 3 {
		t.Errorf("Expected 3 changes, got: %d", len(changes))
	}

	// 验证所有文件都在列表中
	fileMap := make(map[string]bool)
	for _, change := range changes {
		fileMap[change.FilePath] = true
	}

	expectedFiles := []string{"file1.go", "file2.go", "file3.go"}
	for _, file := range expectedFiles {
		if !fileMap[file] {
			t.Errorf("Expected file %s to be in changes list", file)
		}
	}
}

func TestGitDiffer_extractSQLKeywords(t *testing.T) {
	differ := NewGitDiffer(".")

	testCases := []struct {
		sql      string
		expected []string
	}{
		{
			sql:      "SELECT * FROM users WHERE id = ?",
			expected: []string{"select", "users"},
		},
		{
			sql:      "INSERT INTO products (name, price) VALUES (?, ?)",
			expected: []string{"insert", "products"},
		},
		{
			sql:      "UPDATE orders SET status = ? WHERE id = ?",
			expected: []string{"update", "orders"},
		},
		{
			sql:      "DELETE FROM logs WHERE created_at < ?",
			expected: []string{"delete", "logs"},
		},
	}

	for _, tc := range testCases {
		keywords := differ.extractSQLKeywords(tc.sql)

		for _, expected := range tc.expected {
			found := false
			for _, keyword := range keywords {
				if keyword == expected {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected keyword '%s' not found in SQL: %s, got: %v", expected, tc.sql, keywords)
			}
		}
	}
}

func TestGitDiffer_detectSQLChangeInDiff(t *testing.T) {
	differ := NewGitDiffer(".")

	// 模拟一个SQL结果
	result := &base.ResultInfo{
		FileName: "dao/user.go",
		FuncName: "GetUser",
		Key:      "user_query",
		SQL:      "SELECT * FROM users WHERE id = ?",
	}

	// 测试新增SQL的情况
	diffLines := []DiffLine{
		{Type: "+", Content: `	sql := "SELECT * FROM users WHERE id = ?"`, LineNum: 10},
		{Type: "+", Content: `	rows, err := db.Query(sql, id)`, LineNum: 11},
	}

	changeType := differ.detectSQLChangeInDiff(result, diffLines)
	if changeType != "NEW" {
		t.Errorf("Expected NEW for added SQL, got: %s", changeType)
	}

	// 测试修改SQL的情况
	diffLines = []DiffLine{
		{Type: "-", Content: `	sql := "SELECT id FROM users WHERE id = ?"`, LineNum: 10},
		{Type: "+", Content: `	sql := "SELECT * FROM users WHERE id = ?"`, LineNum: 10},
	}

	changeType = differ.detectSQLChangeInDiff(result, diffLines)
	if changeType != "MODIFIED" {
		t.Errorf("Expected MODIFIED for changed SQL, got: %s", changeType)
	}
}
