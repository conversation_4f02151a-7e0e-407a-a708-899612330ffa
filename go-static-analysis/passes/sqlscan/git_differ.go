package sqlscan

import (
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/easyutil"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

// FileChangeInfo 文件变更信息
type FileChangeInfo struct {
	FilePath   string // 文件路径
	ChangeType string // 变更类型: "A"(added), "M"(modified), "D"(deleted), "R"(renamed)
}

// GitDiffer Git差异检测器
type GitDiffer struct {
	RepoPath string // 仓库路径
}

// NewGitDiffer 创建Git差异检测器
func NewGitDiffer(repoPath string) *GitDiffer {
	return &GitDiffer{
		RepoPath: repoPath,
	}
}

// GetChangedFiles 获取两个commit之间的变更文件列表
func (g *GitDiffer) GetChangedFiles(fromCommit, toCommit string) ([]FileChangeInfo, error) {
	if fromCommit == "" || toCommit == "" {
		golog.Warn("empty commit id, fromCommit: %s, toCommit: %s", fromCommit, toCommit)
		return []FileChangeInfo{}, nil
	}

	cmd := fmt.Sprintf("git diff --name-status %s..%s", fromCommit, toCommit)
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		golog.Error("get changed files failed, cmd: %s, err: %v", cmd, err)
		return nil, fmt.Errorf("get changed files failed: %w", err)
	}

	var changes []FileChangeInfo
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			changes = append(changes, FileChangeInfo{
				ChangeType: parts[0],
				FilePath:   parts[1],
			})
		}
	}

	golog.Info("found %d changed files between %s and %s", len(changes), fromCommit, toCommit)
	return changes, nil
}

// GetLastScanCommit 获取上次扫描的commit ID
// 这里简化实现，实际应该从配置文件或数据库获取
func (g *GitDiffer) GetLastScanCommit() (string, error) {
	// 方案1: 获取前一个commit (简化实现)
	cmd := "git rev-parse HEAD~1"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		golog.Warn("get last scan commit failed, try to get first commit, err: %v", err)
		// 如果没有前一个commit，尝试获取第一个commit
		return g.getFirstCommit()
	}

	lastCommit := strings.TrimSpace(output)
	golog.Info("get last scan commit: %s", lastCommit)
	return lastCommit, nil
}

// getFirstCommit 获取仓库的第一个commit
func (g *GitDiffer) getFirstCommit() (string, error) {
	cmd := "git rev-list --max-parents=0 HEAD"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return "", fmt.Errorf("get first commit failed: %w", err)
	}

	firstCommit := strings.TrimSpace(output)
	if firstCommit == "" {
		return "", fmt.Errorf("no commits found in repository")
	}

	golog.Info("get first commit: %s", firstCommit)
	return firstCommit, nil
}

// IsFileChanged 检查指定文件是否在变更列表中
func (g *GitDiffer) IsFileChanged(filePath string, changes []FileChangeInfo) (bool, string) {
	for _, change := range changes {
		if change.FilePath == filePath {
			return true, change.ChangeType
		}
	}
	return false, ""
}

// GetCommitInfo 获取commit的详细信息
func (g *GitDiffer) GetCommitInfo(commitID string) (*CommitInfo, error) {
	if commitID == "" {
		return nil, fmt.Errorf("empty commit id")
	}

	// 获取commit信息
	cmd := fmt.Sprintf("git show --format='%%H|%%an|%%ae|%%at|%%s' --no-patch %s", commitID)
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return nil, fmt.Errorf("get commit info failed: %w", err)
	}

	parts := strings.Split(strings.TrimSpace(output), "|")
	if len(parts) < 5 {
		return nil, fmt.Errorf("invalid commit info format")
	}

	// 解析时间戳
	timestamp := int64(0)
	if len(parts) > 3 {
		fmt.Sscanf(parts[3], "%d", &timestamp)
	}

	return &CommitInfo{
		Hash:      parts[0],
		Author:    parts[1],
		Email:     parts[2],
		Timestamp: timestamp,
		Message:   parts[4],
		Time:      time.Unix(timestamp, 0),
	}, nil
}

// CommitInfo commit信息
type CommitInfo struct {
	Hash      string    // commit hash
	Author    string    // 作者
	Email     string    // 邮箱
	Timestamp int64     // 时间戳
	Message   string    // commit消息
	Time      time.Time // 时间
}

// ValidateGitRepo 验证当前目录是否为有效的Git仓库
func (g *GitDiffer) ValidateGitRepo() error {
	cmd := "git rev-parse --git-dir"
	_, err := easyutil.ExecScript(cmd)
	if err != nil {
		return fmt.Errorf("not a valid git repository: %w", err)
	}
	return nil
}

// GetCurrentBranch 获取当前分支名
func (g *GitDiffer) GetCurrentBranch() (string, error) {
	cmd := "git rev-parse --abbrev-ref HEAD"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return "", fmt.Errorf("get current branch failed: %w", err)
	}

	branch := strings.TrimSpace(output)
	golog.Info("current branch: %s", branch)
	return branch, nil
}

// HasUncommittedChanges 检查是否有未提交的变更
func (g *GitDiffer) HasUncommittedChanges() (bool, error) {
	cmd := "git status --porcelain"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return false, fmt.Errorf("check uncommitted changes failed: %w", err)
	}

	hasChanges := strings.TrimSpace(output) != ""
	if hasChanges {
		golog.Warn("found uncommitted changes")
	}
	return hasChanges, nil
}
