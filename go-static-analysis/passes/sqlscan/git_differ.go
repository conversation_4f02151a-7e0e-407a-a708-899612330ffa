package sqlscan

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/easyutil"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

// FileChangeInfo 文件变更信息
type FileChangeInfo struct {
	FilePath   string // 文件路径
	ChangeType string // 变更类型: "A"(added), "M"(modified), "D"(deleted), "R"(renamed)
}

// GitDiffer Git差异检测器
type GitDiffer struct {
	RepoPath string // 仓库路径
}

// NewGitDiffer 创建Git差异检测器
func NewGitDiffer(repoPath string) *GitDiffer {
	return &GitDiffer{
		RepoPath: repoPath,
	}
}

// GetChangedFiles 获取两个commit之间的变更文件列表
func (g *GitDiffer) GetChangedFiles(fromCommit, toCommit string) ([]FileChangeInfo, error) {
	if fromCommit == "" || toCommit == "" {
		golog.Warn("empty commit id, fromCommit: %s, toCommit: %s", fromCommit, toCommit)
		return []FileChangeInfo{}, nil
	}

	cmd := fmt.Sprintf("git diff --name-status %s..%s", fromCommit, toCommit)
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		golog.Error("get changed files failed, cmd: %s, err: %v", cmd, err)
		return nil, fmt.Errorf("get changed files failed: %w", err)
	}

	var changes []FileChangeInfo
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			changes = append(changes, FileChangeInfo{
				ChangeType: parts[0],
				FilePath:   parts[1],
			})
		}
	}

	golog.Info("found %d changed files between %s and %s", len(changes), fromCommit, toCommit)
	return changes, nil
}

// GetLastScanCommit 获取上次扫描的commit ID
// 这里简化实现，实际应该从配置文件或数据库获取
func (g *GitDiffer) GetLastScanCommit() (string, error) {
	// 方案1: 获取前一个commit (简化实现)
	cmd := "git rev-parse HEAD~1"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		golog.Warn("get last scan commit failed, try to get first commit, err: %v", err)
		// 如果没有前一个commit，尝试获取第一个commit
		return g.getFirstCommit()
	}

	lastCommit := strings.TrimSpace(output)
	golog.Info("get last scan commit: %s", lastCommit)
	return lastCommit, nil
}

// getFirstCommit 获取仓库的第一个commit
func (g *GitDiffer) getFirstCommit() (string, error) {
	cmd := "git rev-list --max-parents=0 HEAD"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return "", fmt.Errorf("get first commit failed: %w", err)
	}

	firstCommit := strings.TrimSpace(output)
	if firstCommit == "" {
		return "", fmt.Errorf("no commits found in repository")
	}

	golog.Info("get first commit: %s", firstCommit)
	return firstCommit, nil
}

// IsFileChanged 检查指定文件是否在变更列表中
func (g *GitDiffer) IsFileChanged(filePath string, changes []FileChangeInfo) (bool, string) {
	for _, change := range changes {
		if change.FilePath == filePath {
			return true, change.ChangeType
		}
	}
	return false, ""
}

// SQLChangeInfo SQL变更信息
type SQLChangeInfo struct {
	FileName   string // 文件名
	FuncName   string // 函数名
	Key        string // SQL的唯一标识
	ChangeType string // "NEW", "MODIFIED", "DELETED", "UNCHANGED"
	OldSQL     string // 旧SQL内容
	NewSQL     string // 新SQL内容
	LineNumber int    // 行号
}

// GetSQLChanges 获取SQL级别的变更信息
func (g *GitDiffer) GetSQLChanges(fromCommit, toCommit string, currentResults []*base.ResultInfo) ([]SQLChangeInfo, error) {
	if fromCommit == "" || toCommit == "" {
		golog.Warn("empty commit id, cannot get SQL changes")
		return []SQLChangeInfo{}, nil
	}

	// 1. 获取变更的文件列表
	changedFiles, err := g.GetChangedFiles(fromCommit, toCommit)
	if err != nil {
		return nil, fmt.Errorf("get changed files failed: %w", err)
	}

	// 2. 获取上一个版本的SQL扫描结果
	oldResults, err := g.getSQLResultsFromCommit(fromCommit)
	if err != nil {
		golog.Warn("get old SQL results failed: %v", err)
		// 如果获取失败，将所有当前SQL标记为新增
		return g.markAllAsNew(currentResults), nil
	}

	// 3. 比较新旧SQL结果
	return g.compareSQLResults(oldResults, currentResults, changedFiles), nil
}

// HistorySQLResponse 历史SQL查询响应
type HistorySQLResponse struct {
	Code int                     `json:"code"`
	Msg  string                  `json:"msg"`
	Data []*HistorySQLResultItem `json:"data"`
}

// HistorySQLResultItem 历史SQL记录项
type HistorySQLResultItem struct {
	SQL      string `json:"sql"`
	FileName string `json:"filename"`
	FnName   string `json:"fn_name"`
	Key      string `json:"key"`
	Type     string `json:"type"`
	Database string `json:"database"`
	Instance string `json:"instance"`
}

// getSQLResultsFromCommit 从指定commit获取SQL扫描结果
func (g *GitDiffer) getSQLResultsFromCommit(commitID string) (map[string]*base.ResultInfo, error) {
	golog.Info("getting SQL results from commit: %s", commitID)

	// 调用服务端API获取历史SQL记录
	url := fmt.Sprintf("http://10.11.97.54:80/sqldata/history?commit_id=%s", commitID)
	resp, err := http.Get(url)
	if err != nil {
		golog.Warn("failed to call history API: %v", err)
		return make(map[string]*base.ResultInfo), nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		golog.Warn("history API returned status: %d", resp.StatusCode)
		return make(map[string]*base.ResultInfo), nil
	}

	var response HistorySQLResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		golog.Warn("failed to decode history response: %v", err)
		return make(map[string]*base.ResultInfo), nil
	}

	if response.Code != 0 {
		golog.Warn("history API returned error: %s", response.Msg)
		return make(map[string]*base.ResultInfo), nil
	}

	// 构建结果映射
	results := make(map[string]*base.ResultInfo)
	for _, item := range response.Data {
		result := &base.ResultInfo{
			FileName: item.FileName,
			FuncName: item.FnName,
			Key:      item.Key,
			SQL:      item.SQL,
			Type:     item.Type,
			Database: item.Database,
			Instance: item.Instance,
		}

		key := g.generateSQLKey(result)
		results[key] = result
	}

	golog.Info("loaded %d historical SQL records from commit: %s", len(results), commitID)
	return results, nil
}

// markAllAsNew 将所有SQL标记为新增
func (g *GitDiffer) markAllAsNew(currentResults []*base.ResultInfo) []SQLChangeInfo {
	var changes []SQLChangeInfo
	for _, result := range currentResults {
		changes = append(changes, SQLChangeInfo{
			FileName:   result.FileName,
			FuncName:   result.FuncName,
			Key:        result.Key,
			ChangeType: "NEW",
			OldSQL:     "",
			NewSQL:     result.SQL,
		})
	}
	return changes
}

// compareSQLResults 比较新旧SQL结果
func (g *GitDiffer) compareSQLResults(oldResults map[string]*base.ResultInfo, currentResults []*base.ResultInfo, changedFiles []FileChangeInfo) []SQLChangeInfo {
	var changes []SQLChangeInfo

	// 创建变更文件映射
	changedFileMap := make(map[string]bool)
	for _, file := range changedFiles {
		changedFileMap[file.FilePath] = true
	}

	// 检查当前结果
	for _, current := range currentResults {
		key := g.generateSQLKey(current)

		if old, exists := oldResults[key]; exists {
			// SQL已存在，检查是否有变更
			if old.SQL != current.SQL {
				changes = append(changes, SQLChangeInfo{
					FileName:   current.FileName,
					FuncName:   current.FuncName,
					Key:        current.Key,
					ChangeType: "MODIFIED",
					OldSQL:     old.SQL,
					NewSQL:     current.SQL,
				})
			} else if changedFileMap[current.FileName] {
				// 文件有变更但SQL未变，标记为UNCHANGED但在变更文件中
				changes = append(changes, SQLChangeInfo{
					FileName:   current.FileName,
					FuncName:   current.FuncName,
					Key:        current.Key,
					ChangeType: "UNCHANGED",
					OldSQL:     old.SQL,
					NewSQL:     current.SQL,
				})
			}
		} else {
			// 新增的SQL
			changes = append(changes, SQLChangeInfo{
				FileName:   current.FileName,
				FuncName:   current.FuncName,
				Key:        current.Key,
				ChangeType: "NEW",
				OldSQL:     "",
				NewSQL:     current.SQL,
			})
		}
	}

	return changes
}

// generateSQLKey 生成SQL的唯一标识
func (g *GitDiffer) generateSQLKey(result *base.ResultInfo) string {
	return fmt.Sprintf("%s_%s_%s", result.FileName, result.FuncName, result.Key)
}

// GetCommitInfo 获取commit的详细信息
func (g *GitDiffer) GetCommitInfo(commitID string) (*CommitInfo, error) {
	if commitID == "" {
		return nil, fmt.Errorf("empty commit id")
	}

	// 获取commit信息
	cmd := fmt.Sprintf("git show --format='%%H|%%an|%%ae|%%at|%%s' --no-patch %s", commitID)
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return nil, fmt.Errorf("get commit info failed: %w", err)
	}

	parts := strings.Split(strings.TrimSpace(output), "|")
	if len(parts) < 5 {
		return nil, fmt.Errorf("invalid commit info format")
	}

	// 解析时间戳
	timestamp := int64(0)
	if len(parts) > 3 {
		fmt.Sscanf(parts[3], "%d", &timestamp)
	}

	return &CommitInfo{
		Hash:      parts[0],
		Author:    parts[1],
		Email:     parts[2],
		Timestamp: timestamp,
		Message:   parts[4],
		Time:      time.Unix(timestamp, 0),
	}, nil
}

// CommitInfo commit信息
type CommitInfo struct {
	Hash      string    // commit hash
	Author    string    // 作者
	Email     string    // 邮箱
	Timestamp int64     // 时间戳
	Message   string    // commit消息
	Time      time.Time // 时间
}

// ValidateGitRepo 验证当前目录是否为有效的Git仓库
func (g *GitDiffer) ValidateGitRepo() error {
	cmd := "git rev-parse --git-dir"
	_, err := easyutil.ExecScript(cmd)
	if err != nil {
		return fmt.Errorf("not a valid git repository: %w", err)
	}
	return nil
}

// GetCurrentBranch 获取当前分支名
func (g *GitDiffer) GetCurrentBranch() (string, error) {
	cmd := "git rev-parse --abbrev-ref HEAD"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return "", fmt.Errorf("get current branch failed: %w", err)
	}

	branch := strings.TrimSpace(output)
	golog.Info("current branch: %s", branch)
	return branch, nil
}

// HasUncommittedChanges 检查是否有未提交的变更
func (g *GitDiffer) HasUncommittedChanges() (bool, error) {
	cmd := "git status --porcelain"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return false, fmt.Errorf("check uncommitted changes failed: %w", err)
	}

	hasChanges := strings.TrimSpace(output) != ""
	if hasChanges {
		golog.Warn("found uncommitted changes")
	}
	return hasChanges, nil
}
