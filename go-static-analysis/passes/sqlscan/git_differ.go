package sqlscan

import (
	"fmt"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/easyutil"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

// FileChangeInfo 文件变更信息
type FileChangeInfo struct {
	FilePath   string // 文件路径
	ChangeType string // 变更类型: "A"(added), "M"(modified), "D"(deleted), "R"(renamed)
}

// GitDiffer Git差异检测器
type GitDiffer struct {
	RepoPath string // 仓库路径
}

// NewGitDiffer 创建Git差异检测器
func NewGitDiffer(repoPath string) *GitDiffer {
	return &GitDiffer{
		RepoPath: repoPath,
	}
}

// GetChangedFiles 获取两个commit之间的变更文件列表
func (g *GitDiffer) GetChangedFiles(fromCommit, toCommit string) ([]FileChangeInfo, error) {
	if fromCommit == "" || toCommit == "" {
		golog.Warn("empty commit id, fromCommit: %s, toCommit: %s", fromCommit, toCommit)
		return []FileChangeInfo{}, nil
	}

	cmd := fmt.Sprintf("git diff --name-status %s..%s", fromCommit, toCommit)
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		golog.Error("get changed files failed, cmd: %s, err: %v", cmd, err)
		return nil, fmt.Errorf("get changed files failed: %w", err)
	}

	var changes []FileChangeInfo
	lines := strings.Split(strings.TrimSpace(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 2 {
			changes = append(changes, FileChangeInfo{
				ChangeType: parts[0],
				FilePath:   parts[1],
			})
		}
	}

	golog.Info("found %d changed files between %s and %s", len(changes), fromCommit, toCommit)
	return changes, nil
}

// GetLastScanCommit 获取上次扫描的commit ID
// 这里简化实现，实际应该从配置文件或数据库获取
func (g *GitDiffer) GetLastScanCommit() (string, error) {
	// 方案1: 获取前一个commit (简化实现)
	cmd := "git rev-parse HEAD~1"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		golog.Warn("get last scan commit failed, try to get first commit, err: %v", err)
		// 如果没有前一个commit，尝试获取第一个commit
		return g.getFirstCommit()
	}

	lastCommit := strings.TrimSpace(output)
	golog.Info("get last scan commit: %s", lastCommit)
	return lastCommit, nil
}

// getFirstCommit 获取仓库的第一个commit
func (g *GitDiffer) getFirstCommit() (string, error) {
	cmd := "git rev-list --max-parents=0 HEAD"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return "", fmt.Errorf("get first commit failed: %w", err)
	}

	firstCommit := strings.TrimSpace(output)
	if firstCommit == "" {
		return "", fmt.Errorf("no commits found in repository")
	}

	golog.Info("get first commit: %s", firstCommit)
	return firstCommit, nil
}

// IsFileChanged 检查指定文件是否在变更列表中
func (g *GitDiffer) IsFileChanged(filePath string, changes []FileChangeInfo) (bool, string) {
	for _, change := range changes {
		if change.FilePath == filePath {
			return true, change.ChangeType
		}
	}
	return false, ""
}

// SQLChangeInfo SQL变更信息
type SQLChangeInfo struct {
	FileName   string // 文件名
	FuncName   string // 函数名
	Key        string // SQL的唯一标识
	ChangeType string // "NEW", "MODIFIED", "DELETED", "UNCHANGED"
	OldSQL     string // 旧SQL内容
	NewSQL     string // 新SQL内容
	LineNumber int    // 行号
}

// GetSQLChanges 获取SQL级别的变更信息
// 通过分析Git diff内容来识别SQL变更，而不是比较数据库记录
func (g *GitDiffer) GetSQLChanges(fromCommit, toCommit string, currentResults []*base.ResultInfo) ([]SQLChangeInfo, error) {
	if fromCommit == "" || toCommit == "" {
		golog.Warn("empty commit id, cannot get SQL changes")
		return []SQLChangeInfo{}, nil
	}

	// 1. 获取变更的文件列表
	changedFiles, err := g.GetChangedFiles(fromCommit, toCommit)
	if err != nil {
		return nil, fmt.Errorf("get changed files failed: %w", err)
	}

	var sqlChanges []SQLChangeInfo

	// 2. 对每个变更的文件分析其Git diff内容
	for _, fileChange := range changedFiles {
		// 只处理Go文件
		if !strings.HasSuffix(fileChange.FilePath, ".go") {
			continue
		}

		// 获取该文件的详细diff内容
		fileDiff, err := g.getFileDiffContent(fromCommit, toCommit, fileChange.FilePath)
		if err != nil {
			golog.Warn("get file diff failed for %s: %v", fileChange.FilePath, err)
			continue
		}

		// 分析diff内容，识别SQL相关的变更
		fileChanges := g.analyzeSQLChangesInDiff(fileChange.FilePath, fileDiff, currentResults)
		sqlChanges = append(sqlChanges, fileChanges...)
	}

	// 3. 对于未在变更文件中的SQL，标记为UNCHANGED
	changedFileMap := make(map[string]bool)
	for _, file := range changedFiles {
		changedFileMap[file.FilePath] = true
	}

	for _, result := range currentResults {
		if !changedFileMap[result.FileName] {
			// 文件未变更，SQL标记为UNCHANGED
			found := false
			for _, change := range sqlChanges {
				if change.FileName == result.FileName && change.FuncName == result.FuncName && change.Key == result.Key {
					found = true
					break
				}
			}
			if !found {
				sqlChanges = append(sqlChanges, SQLChangeInfo{
					FileName:   result.FileName,
					FuncName:   result.FuncName,
					Key:        result.Key,
					ChangeType: "UNCHANGED",
					OldSQL:     result.SQL, // 假设未变更
					NewSQL:     result.SQL,
				})
			}
		}
	}

	return sqlChanges, nil
}

// 注释：删除了基于API调用的历史记录比较方法
// 现在完全基于Git diff内容来分析SQL变更

// generateSQLKey 生成SQL的唯一标识
func (g *GitDiffer) generateSQLKey(result *base.ResultInfo) string {
	return fmt.Sprintf("%s_%s_%s", result.FileName, result.FuncName, result.Key)
}

// getFileDiffContent 获取指定文件的Git diff内容
func (g *GitDiffer) getFileDiffContent(fromCommit, toCommit, filePath string) (string, error) {
	cmd := fmt.Sprintf("git diff %s..%s -- %s", fromCommit, toCommit, filePath)
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return "", fmt.Errorf("get file diff failed: %w", err)
	}
	return output, nil
}

// DiffLine Git diff中的一行
type DiffLine struct {
	Type    string // "+", "-", " " (context)
	Content string // 行内容
	LineNum int    // 行号
}

// analyzeSQLChangesInDiff 分析Git diff内容，识别SQL相关的变更
func (g *GitDiffer) analyzeSQLChangesInDiff(filePath string, diffContent string, currentResults []*base.ResultInfo) []SQLChangeInfo {
	var changes []SQLChangeInfo

	// 解析diff内容
	diffLines := g.parseDiffContent(diffContent)

	// 查找该文件中的SQL结果
	var fileResults []*base.ResultInfo
	for _, result := range currentResults {
		if result.FileName == filePath {
			fileResults = append(fileResults, result)
		}
	}

	// 分析每个SQL是否在diff中有变更
	for _, result := range fileResults {
		changeType := g.detectSQLChangeInDiff(result, diffLines)

		changes = append(changes, SQLChangeInfo{
			FileName:   result.FileName,
			FuncName:   result.FuncName,
			Key:        result.Key,
			ChangeType: changeType,
			OldSQL:     "", // 从diff中提取会比较复杂，暂时留空
			NewSQL:     result.SQL,
		})
	}

	return changes
}

// parseDiffContent 解析Git diff内容
func (g *GitDiffer) parseDiffContent(diffContent string) []DiffLine {
	var lines []DiffLine

	diffLines := strings.Split(diffContent, "\n")
	for i, line := range diffLines {
		if len(line) == 0 {
			continue
		}

		var diffLine DiffLine
		diffLine.LineNum = i + 1

		switch line[0] {
		case '+':
			diffLine.Type = "+"
			diffLine.Content = line[1:]
		case '-':
			diffLine.Type = "-"
			diffLine.Content = line[1:]
		case ' ':
			diffLine.Type = " "
			diffLine.Content = line[1:]
		default:
			// 跳过diff头部信息
			continue
		}

		lines = append(lines, diffLine)
	}

	return lines
}

// detectSQLChangeInDiff 检测SQL在diff中的变更类型
func (g *GitDiffer) detectSQLChangeInDiff(result *base.ResultInfo, diffLines []DiffLine) string {
	// 简化的检测逻辑：
	// 1. 如果SQL内容出现在新增行(+)中，可能是NEW或MODIFIED
	// 2. 如果SQL内容出现在删除行(-)中，说明有旧版本，是MODIFIED
	// 3. 如果都没有出现，是UNCHANGED

	hasAddition := false
	hasDeletion := false

	// 提取SQL中的关键词进行匹配（简化处理）
	sqlKeywords := g.extractSQLKeywords(result.SQL)

	for _, line := range diffLines {
		lineContent := strings.ToLower(strings.TrimSpace(line.Content))

		// 检查是否包含SQL关键词
		containsSQL := false
		for _, keyword := range sqlKeywords {
			if strings.Contains(lineContent, strings.ToLower(keyword)) {
				containsSQL = true
				break
			}
		}

		if containsSQL {
			switch line.Type {
			case "+":
				hasAddition = true
			case "-":
				hasDeletion = true
			}
		}
	}

	// 判断变更类型
	if hasAddition && hasDeletion {
		return "MODIFIED"
	} else if hasAddition && !hasDeletion {
		return "NEW"
	} else if !hasAddition && hasDeletion {
		return "DELETED"
	} else {
		return "UNCHANGED"
	}
}

// extractSQLKeywords 从SQL中提取关键词用于匹配
func (g *GitDiffer) extractSQLKeywords(sql string) []string {
	var keywords []string

	// 提取SQL中的关键词
	sql = strings.ToLower(sql)

	// 基本SQL关键词
	if strings.Contains(sql, "select") {
		keywords = append(keywords, "select")
	}
	if strings.Contains(sql, "insert") {
		keywords = append(keywords, "insert")
	}
	if strings.Contains(sql, "update") {
		keywords = append(keywords, "update")
	}
	if strings.Contains(sql, "delete") {
		keywords = append(keywords, "delete")
	}

	// 提取表名（简化处理）
	words := strings.Fields(sql)
	for i, word := range words {
		if (word == "from" || word == "into" || word == "update") && i+1 < len(words) {
			tableName := strings.Trim(words[i+1], "`,")
			if len(tableName) > 0 {
				keywords = append(keywords, tableName)
			}
		}
	}

	return keywords
}

// GetCommitInfo 获取commit的详细信息
func (g *GitDiffer) GetCommitInfo(commitID string) (*CommitInfo, error) {
	if commitID == "" {
		return nil, fmt.Errorf("empty commit id")
	}

	// 获取commit信息
	cmd := fmt.Sprintf("git show --format='%%H|%%an|%%ae|%%at|%%s' --no-patch %s", commitID)
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return nil, fmt.Errorf("get commit info failed: %w", err)
	}

	parts := strings.Split(strings.TrimSpace(output), "|")
	if len(parts) < 5 {
		return nil, fmt.Errorf("invalid commit info format")
	}

	// 解析时间戳
	timestamp := int64(0)
	if len(parts) > 3 {
		fmt.Sscanf(parts[3], "%d", &timestamp)
	}

	return &CommitInfo{
		Hash:      parts[0],
		Author:    parts[1],
		Email:     parts[2],
		Timestamp: timestamp,
		Message:   parts[4],
		Time:      time.Unix(timestamp, 0),
	}, nil
}

// CommitInfo commit信息
type CommitInfo struct {
	Hash      string    // commit hash
	Author    string    // 作者
	Email     string    // 邮箱
	Timestamp int64     // 时间戳
	Message   string    // commit消息
	Time      time.Time // 时间
}

// ValidateGitRepo 验证当前目录是否为有效的Git仓库
func (g *GitDiffer) ValidateGitRepo() error {
	cmd := "git rev-parse --git-dir"
	_, err := easyutil.ExecScript(cmd)
	if err != nil {
		return fmt.Errorf("not a valid git repository: %w", err)
	}
	return nil
}

// GetCurrentBranch 获取当前分支名
func (g *GitDiffer) GetCurrentBranch() (string, error) {
	cmd := "git rev-parse --abbrev-ref HEAD"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return "", fmt.Errorf("get current branch failed: %w", err)
	}

	branch := strings.TrimSpace(output)
	golog.Info("current branch: %s", branch)
	return branch, nil
}

// HasUncommittedChanges 检查是否有未提交的变更
func (g *GitDiffer) HasUncommittedChanges() (bool, error) {
	cmd := "git status --porcelain"
	output, err := easyutil.ExecScript(cmd)
	if err != nil {
		return false, fmt.Errorf("check uncommitted changes failed: %w", err)
	}

	hasChanges := strings.TrimSpace(output) != ""
	if hasChanges {
		golog.Warn("found uncommitted changes")
	}
	return hasChanges, nil
}
