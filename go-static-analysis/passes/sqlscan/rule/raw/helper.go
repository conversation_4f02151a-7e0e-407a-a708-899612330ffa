package raw

import (
	"fmt"
	"go/constant"
	"go/token"
	"strings"

	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
)

const ConstPlaceHolder = " __PLACE_HOLDER__ "
const MaxDepth = 10

type Resolver struct {
	Cache map[ssa.Value]string
}

func NewResolver() *Resolver {
	return &Resolver{
		Cache: make(map[ssa.Value]string),
	}
}

// resolveValue 尝试还原常量或返回表达式
func (r *Resolver) resolveValueString(v ssa.Value, parent *base.Caller, depth int) string {
	if depth > MaxDepth {
		return ""
	}

	if sql, ok := r.Cache[v]; ok {
		return sql
	}

	var res string
	switch val := v.(type) {
	case *ssa.Const:
		res = val.Value.ExactString()
	case *ssa.Parameter:
		res = val.Name() ///  fmt.Sprintf("参数: %s", val.Name())
	case *ssa.Field:
		res = val.Name() // fmt.Sprintf("字段: %s", val.Name())
	case *ssa.BinOp:
		res = r.resolveBinOPString(val, parent, depth)
	case *ssa.UnOp:
		res = r.resolveValueString(val.X, parent, depth) // fmt.Sprintf("解引用: %v", resolveValue(val.X))
	case *ssa.Call:
		res = r.resolveCallString(val, parent, depth) // fmt.Sprintf("调用: %v", val.Call)
	case *ssa.Extract:
		res = r.resolveExtractString(val, parent)
	case *ssa.Convert:
		res = r.resolveValueString(val.X, parent, depth+1)
	case *ssa.FieldAddr:
		res = r.resolveValueString(val.X, parent, depth+1)
	case *ssa.Slice:
		res = r.resolveValueString(val.X, parent, depth+1)
	case *ssa.Phi:
		res = r.resolvePhiString(val, parent, depth)

	case *ssa.MakeInterface:
		res = r.resolveValueString(val.X, parent, depth+1)
	default:
		fmt.Printf("resolveValueString default %T: %v\n", val, val)
	}

	res = strings.Trim(res, "\"")

	if strings.TrimSpace(res) == "" {
		res = ConstPlaceHolder // 占位符
	}

	r.Cache[v] = res

	return res
}

func (r *Resolver) resolvePhiString(phi *ssa.Phi, parent *base.Caller, depth int) string {
	for _, e := range phi.Edges {
		v := r.resolveValueString(e, parent, depth+1)
		return v
		/*
			vTrimSpace := strings.TrimSpace(v)
			if vTrimSpace != "" && vTrimSpace != ConstPlaceHolderTrimSpace {
				return v
			}
			/*
				vNew := strings.TrimSpace(v)
				if vNew != "" && vNew != ConstPlaceHolderTrimSpace {
					fmt.Printf("resolvePhiString, v: -%v-\n", v)
					return v
				}*/
	}

	return ""
}

func (r *Resolver) resolveAllocSlice(alloc *ssa.Alloc, parent *base.Caller, depth int) []string {
	elements := make(map[int]string)

	// 在 alloc 所属函数中寻找相关 Store 指令
	for i, instr := range *alloc.Referrers() {
		fmt.Printf("resolveAllocString p1: %d, instr: %T, %v\n", i, instr, instr)
		switch instr := instr.(type) {
		case *ssa.IndexAddr:
			indexAddr := instr
			// 尝试解析 index 是常数

			index := r.resolveValueInt(indexAddr.Index)
			if index < 0 {
				continue
			}

			for _, refer := range *indexAddr.Referrers() {
				switch refer := refer.(type) {
				case *ssa.Store:
					store := refer

					if store.Addr != indexAddr {
						continue
					}
					val := r.resolveValueString(store.Val, parent, depth+1)
					fmt.Printf("resolveAllocString, store val, %T: %v, concrete: %v\n", store.Val, store.Val, val)

					elements[index] = val

				default:
					fmt.Printf("resolveAllocString, default val, %T: %v\n", refer, refer)

				}

			}
		default:
			fmt.Printf("resolveAllocString, default, %T: %v\n", instr, instr)
		}

	}
	maxIndex := -1
	for i := range elements {
		if i > maxIndex {
			maxIndex = i
		}
	}

	slice := make([]string, maxIndex+1)
	for i, v := range elements {
		slice[i] = v
	}
	return slice
}

func (r *Resolver) resolveExtractString(extract *ssa.Extract, parent *base.Caller) string {
	tuple := extract.Tuple
	fmt.Printf("tuple type: %T, val: %v", tuple, tuple)
	return ""

}

func (r *Resolver) resolveCallString(call *ssa.Call, parent *base.Caller, depth int) string {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return ""
	}
	switch fn.String() {
	case "GetTableBySub":
		if len(call.Call.Args) >= 2 {
			return r.resolveValueString(call.Call.Args[1], parent, depth+1)
		}
		return ""
	case "fmt.Sprintf":
		str := r.resolveValueString(call.Call.Args[0], parent, depth+1)
		if len(call.Call.Args) == 1 {
			return str
		}
		if len(call.Call.Args) == 2 {
			slice := r.resolveValueSlice(call.Call.Args[1], parent, depth+1)
			var argsSlice []interface{}
			for _, item := range slice {
				argsSlice = append(argsSlice, item)
			}
			// fmt.Println("resolveCallString fmt.Sprintf slice: %v", slice)
			return fmt.Sprintf(str, argsSlice...)
		}
		return ""

	case "strings.Join":
		slice := r.resolveValueSlice(call.Call.Args[0], parent, depth+1)
		sep := r.resolveValueString(call.Call.Args[1], parent, depth+1)

		res := strings.Join(slice, sep)

		return strings.TrimSpace(res)
	default:
		res := r.resolveDefaultCallValue(call, parent, depth)
		return res
	}
}

func (r *Resolver) resolveDefaultCallValue(call *ssa.Call, parent *base.Caller, depth int) string {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return ""
	}
	for _, block := range fn.Blocks {
		for _, instr := range block.Instrs {
			switch instr := instr.(type) {
			case *ssa.BinOp:
				return r.resolveBinOPString(instr, parent, depth)
			case *ssa.Return:
				return r.resolveValueString(instr.Results[0], parent, depth+1)
			default:
				fmt.Printf("resolveDefaultCallValue, instr: %T, %v\n", instr, instr)
			}
		}
	}

	return ""
}

func (r *Resolver) resolveBinOPString(instr *ssa.BinOp, parent *base.Caller, depth int) string {
	x := r.resolveValueString(instr.X, parent, depth+1)
	y := r.resolveValueString(instr.Y, parent, depth+1)
	if instr.Op == token.ADD {
		return x + y
	}

	return ""

}

// ###########################

func (r *Resolver) resolveValueSlice(v ssa.Value, parent *base.Caller, depth int) []string {
	switch val := v.(type) {
	case *ssa.Slice:
		return r.resolveSliceFromArray(val.X, parent, depth)

	case *ssa.Call:
		if fn := val.Call.StaticCallee(); fn != nil && fn.Name() == "append" && len(val.Call.Args) >= 2 {
			base := r.resolveValueSlice(val.Call.Args[0], parent, depth+1)
			add := r.resolveValueSlice(val.Call.Args[1], parent, depth+1)
			return append(base, add...)
		}

	case *ssa.Phi:
		var result []string
		for _, edge := range val.Edges {
			res := r.resolveValueSlice(edge, parent, depth+1)
			for _, item := range res {
				if item != "" {
					result = append(result, item)
				}
			}

		}
		return result
	case *ssa.Alloc:
		return r.resolveAllocSlice(val, parent, depth+1)

	case *ssa.Const:
		if val.IsNil() {
			return nil
		}
		return []string{val.Value.ExactString()}

	default:
		s := r.resolveValueString(val, parent, depth+1)
		if s != "" {
			return []string{s}
		}
	}
	return nil
}

func (r *Resolver) resolveSliceFromArray(array ssa.Value, parent *base.Caller, depth int) []string {
	alloc, ok := array.(*ssa.Alloc)
	if !ok {
		return nil
	}

	// 从 alloc 找到所在函数
	fn := getEnclosingFunction(alloc)
	if fn == nil {
		return nil
	}

	elemMap := map[int]string{}

	for _, block := range fn.Blocks {
		for _, instr := range block.Instrs {
			store, ok := instr.(*ssa.Store)
			if !ok {
				continue
			}
			addr, ok := store.Addr.(*ssa.IndexAddr)
			if !ok {
				continue
			}
			if addr.X != alloc {
				continue
			}
			indexConst, ok := addr.Index.(*ssa.Const)
			if !ok {
				continue
			}
			indexInt := r.resolveValueInt(indexConst)
			if !ok {
				continue
			}
			strVal := r.resolveValueString(store.Val, parent, depth+1)
			elemMap[int(indexInt)] = strVal
		}
	}

	// 构造有序数组
	maxIdx := -1
	for i := range elemMap {
		if i > maxIdx {
			maxIdx = i
		}
	}
	res := make([]string, maxIdx+1)
	for i := 0; i <= maxIdx; i++ {
		res[i] = elemMap[i]
	}
	return res
}

// 从任意 ssa.Value 获取所在函数
func getEnclosingFunction(v ssa.Value) *ssa.Function {
	if v == nil {
		return nil
	}
	if instr, ok := v.(ssa.Instruction); ok {
		return instr.Parent()
	}
	if valWithPos, ok := v.(interface{ Parent() *ssa.Function }); ok {
		return valWithPos.Parent()
	}
	return nil
}

// 安全解析常量字符串
func (r *Resolver) resolveSSAConstString(v ssa.Value) string {
	switch c := v.(type) {
	case *ssa.Const:
		return strings.Trim(c.Value.ExactString(), `"`)
	default:
		return fmt.Sprintf("%v", v)
	}
}

func (r *Resolver) resolveValueInt(v ssa.Value) int {
	c, ok := v.(*ssa.Const)
	if !ok {
		return 0 // 或者 panic / 返回 error
	}

	// 只处理整数
	if c.Value.Kind() == constant.Int {
		val, _ := constant.Int64Val(c.Value)
		return int(val)
	}

	// 其他类型（如 float, string），可根据需要加处理
	return 0
}
