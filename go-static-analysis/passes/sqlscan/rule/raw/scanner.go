package raw

import (
	"fmt"
	"strings"

	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
)

func (scanner *RawScanner) scanChains() error {
	var results []*base.ResultInfo
	for _, chain := range scanner.Chains {
		resultInfo := scanner.scanChain(chain)
		if resultInfo != nil {
			results = append(results, resultInfo)
		}
	}
	scanner.Results = results

	return nil

}

func (scanner *RawScanner) scanChain(chain *Chain) *base.ResultInfo {
	var parent *base.Caller
	var sqlStmt *ssa.Call

	for _, edge := range chain.Edge {
		if edge == nil || edge.Site == nil || edge.Site.Value() == nil {
			continue
		}

		ssaCall := edge.Site.Value()
		parent = base.BuildCaller(ssaCall, parent)
		sqlStmt = ssaCall
	}
	if sqlStmt == nil {
		scanner.showChain("sql stmt is nil ", chain)
		return nil
	}

	return scanner.handleSQLCall(chain, sqlStmt, parent)

}

func (scanner *RawScanner) isLocalDBFunction(fn *ssa.Function, call *ssa.Call) bool {
	if !strings.Contains(fn.String(), "database/sql.DB") {
		return false
	}

	fileName := scanner.Ctx.Prog.Fset.Position(call.Pos()).Filename
	if strings.Contains(fileName, scanner.Ctx.ICode) {
		// fmt.Println("isLocalDBFunction, call fileName: ", fileName, ", repo Name: ", scanner.Ctx.ICode)
		return true
	}
	// ssh://<EMAIL>:8235/baidu/netdisk/pcs-go-thumbnail
	// fmt.Println("isLocalDBFunction: ", call.String())
	return false
}

func formatCaller(caller *base.Caller) string {
	return formatCallerDetail(caller)
}
func formatCallerDetail(caller *base.Caller) string {
	callStr := caller.Call.String()
	if caller.ParentCall != nil {
		callStr += "\n   <---" + caller.ParentCall.String()
	}
	return callStr
}

func (scanner *RawScanner) handleSQLCall(chain *Chain, call *ssa.Call, parent *base.Caller) *base.ResultInfo {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return nil
	}

	if !scanner.isLocalDBFunction(fn, call) {
		return nil
	}

	var preFn *ssa.Function
	for i, node := range chain.Nodes {
		if node.Func.String() == fn.String() {
			if i-1 >= 0 {
				preFn = chain.Nodes[i-1].Func
				break
			}
		}
	}
	if preFn == nil {
		fmt.Println("match panic")
		return nil
	}

	funcContext, ok := scanner.FuncContextMap[preFn]
	if !ok {
		funcContext = base.NewFuncContext(preFn)
	}

	switch fn.String() {
	case "(*database/sql.DB).Query":
		return scanner.handleQueryFn(funcContext, call, parent)
	case "(*database/sql.DB).Exec":
		return scanner.handleExecFn(funcContext, call, parent)
	case "(*database/sql.DB).QueryRow":
		return scanner.handleQueryRowFn(funcContext, call, parent)
	default:
		fmt.Println("handleSQLCall fn string: ", fn.String())
	}

	scanner.FuncContextMap[fn] = funcContext

	return nil

}

func (scanner *RawScanner) handleQueryFn(funcContext *base.FuncContext, call *ssa.Call, parent *base.Caller) *base.ResultInfo {
	callStr := ""
	if call != nil {
		callStr = scanner.Ctx.Prog.Fset.Position(call.Pos()).Filename
	}

	r := NewResolver()
	sql := r.resolveValueString(call.Call.Args[1], parent, 0)
	fmt.Println("handleQueryFn sql: ", sql, "  #### parent call: ", callStr)

	funcContext.CallIndex++
	if sql == "" {
		return nil
	}

	fileName := scanner.Ctx.Prog.Fset.Position(call.Pos()).Filename
	funcName := funcContext.Fn.String()
	key := fmt.Sprintf("%s%s%d", fileName, funcName, funcContext.CallIndex)

	return base.BuildResultInfo(fileName, funcName, key, sql, "raw")

}

func (scanner *RawScanner) handleQueryRowFn(funcContext *base.FuncContext, call *ssa.Call, parent *base.Caller) *base.ResultInfo {
	/*
		callStr := ""
		if call != nil {
			callStr = scanner.Ctx.Prog.Fset.Position(call.Pos()).String()
		}*/

	r := NewResolver()
	sql := r.resolveValueString(call.Call.Args[1], parent, 0)
	// fmt.Println("handleQueryRowFn sql: ", sql, "  #### pos:  ", callStr)

	funcContext.CallIndex++
	if sql == "" {
		return nil
	}

	fileName := scanner.Ctx.Prog.Fset.Position(call.Pos()).Filename
	funcName := funcContext.Fn.String()
	key := fmt.Sprintf("%s%s%d", fileName, funcName, funcContext.CallIndex)

	return base.BuildResultInfo(fileName, funcName, key, sql, "raw")

}

func (scanner *RawScanner) handleExecFn(funcContext *base.FuncContext, call *ssa.Call, parent *base.Caller) *base.ResultInfo {
	r := NewResolver()
	sql := r.resolveValueString(call.Call.Args[1], parent, 0)
	fmt.Println("handleExecFn sql: ", sql)

	funcContext.CallIndex++
	if sql == "" {
		return nil
	}

	fileName := scanner.Ctx.Prog.Fset.Position(call.Pos()).Filename
	funcName := funcContext.Fn.String()
	key := fmt.Sprintf("%s%s%d", fileName, funcName, funcContext.CallIndex)

	return base.BuildResultInfo(fileName, funcName, key, sql, "raw")

}
