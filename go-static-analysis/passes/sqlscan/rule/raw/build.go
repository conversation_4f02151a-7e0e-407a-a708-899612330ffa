package raw

import (
	"fmt"
	"go/types"
	"strings"

	"golang.org/x/tools/go/callgraph"
	"golang.org/x/tools/go/callgraph/static"
	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
)

type RawScanner struct {
	Ctx          *base.RuleContext
	Graph        *callgraph.Graph
	ReverseGraph *ReverseGraph

	// 正向调用链
	Chains []*Chain

	// 结果
	Results []*base.ResultInfo

	FuncContextMap map[*ssa.Function]*base.FuncContext
}

type ReverseGraph struct {
	Roots []*ReverseNode
}

type ReverseNode struct {
	Func *ssa.Function
	Ins  []*Edge // unordered set of incoming call edges (n.Parents[*].Callee == n)
	Outs []*Edge //  unordered set of incoming call edges (n.Childs[*].Caller == n)
}

type Edge struct {
	Caller *ReverseNode
	Site   ssa.CallInstruction
	Callee *ReverseNode
}

type Chain struct {
	Nodes []*ReverseNode
	Edge  []*Edge
}

func NewRawScanner(ctx *base.RuleContext) *RawScanner {
	return &RawScanner{
		Ctx:            ctx,
		FuncContextMap: make(map[*ssa.Function]*base.FuncContext),
	}

}

func (scanner *RawScanner) Run() error {
	scanner.buildGraph()
	scanner.showGraph()
	if err := scanner.buildReverseGraph(); err != nil {
		return err
	}

	// scanner.showChains()
	if err := scanner.scanChains(); err != nil {
		fmt.Printf("scanner scan chains failed, err:%v\n", err)
		return err
	}
	return nil
}

func newReverseNode(node *callgraph.Node, visitedFn map[*ssa.Function]struct{}) *ReverseNode {
	if _, ok := visitedFn[node.Func]; ok {
		return nil
	}
	visitedFn[node.Func] = struct{}{}

	newNode := &ReverseNode{
		Func: node.Func,
	}
	for _, e := range node.In {
		child := newReverseNode(e.Caller, visitedFn)
		// child 是调用者，即反向调用链的子节点
		if child == nil {
			continue
		}
		newNode.Ins = append(newNode.Ins, &Edge{
			Caller: child,
			Callee: newNode,
			Site:   e.Site,
		})

		child.Outs = append(newNode.Outs, &Edge{
			Caller: child,
			Callee: newNode,
			Site:   e.Site,
		})

	}

	return newNode
}

func (scanner *RawScanner) buildGraph() {
	cg := static.CallGraph(scanner.Ctx.Prog)
	scanner.Graph = cg
}

func (scanner *RawScanner) showGraph() {
	//graph := scanner.Graph
}

func (scanner *RawScanner) buildReverseGraph() error {
	fnMaps, err := scanner.findTargetFns()
	if err != nil {
		return err
	}
	var fnSlice []*ssa.Function
	for key, fns := range fnMaps {
		var fnStrs []string
		for _, fn := range fns {
			fnStrs = append(fnStrs, fn.Name())
		}

		if key == "DB" {
			fnSlice = append(fnSlice, fns...)
			// fmt.Printf("BuildReverseGraph, key: %s, values: %v\n", key, fnStrs)
			break
		}
	}

	fnSlice = scanner.filtTargetFns(fnSlice)
	// fmt.Println("fnSlice: ", fnSlice)

	scanner.buildReverseGraphDetail(fnSlice)

	/*
		if err := scanner.showReverseGraph(); err != nil {
			fmt.Printf("scanner show reverse graph, err: %v\n ", err)
			return err
		}*/

	if err := scanner.buildChains(); err != nil {
		fmt.Printf("scanner build chains faildd, err:%v\n", err)
		return err
	}

	// scanner.showChains()

	return nil
}

func (scanner *RawScanner) buildReverseGraphDetail(roots []*ssa.Function) {
	g := &ReverseGraph{}
	for _, root := range roots {
		n, ok := scanner.Graph.Nodes[root]
		if !ok {
			continue
		}
		reverseNode := newReverseNode(n, make(map[*ssa.Function]struct{}))
		g.Roots = append(g.Roots, reverseNode)
	}
	scanner.ReverseGraph = g
}

func (scanner *RawScanner) showReverseGraph() error {
	reverseGraph := scanner.ReverseGraph
	for _, root := range reverseGraph.Roots {
		scanner.showReverseGraphDetail(root, "")
	}

	return nil
}

// 根据反向链表构建正向调用链
func (scanner *RawScanner) buildChains() error {
	for _, root := range scanner.ReverseGraph.Roots {
		scanner.buildReverseChain(root, &Chain{})
	}
	scanner.filterChains()
	scanner.reverseChains()
	return nil
}

func (scanner *RawScanner) reverseChains() {
	var chains []*Chain
	for _, chain := range scanner.Chains {
		newNodes := Reverse(chain.Nodes)
		newEdge := Reverse((chain.Edge))

		newChain := &Chain{
			Nodes: newNodes,
			Edge:  newEdge,
		}
		chains = append(chains, newChain)

	}
	scanner.Chains = chains

}

// ssh://<EMAIL>:8235/baidu/netdisk/nd-ai-trans
func (scanner *RawScanner) buildReverseChain(root *ReverseNode, chain *Chain) {

	chain.Nodes = append(chain.Nodes, root)

	if len(root.Ins) == 0 {
		scanner.Chains = append(scanner.Chains, chain)
		return
	}

	for _, edge := range root.Ins {
		newChain := deepCopyChain(chain)
		newChain.Edge = append(newChain.Edge, edge)

		scanner.buildReverseChain(edge.Caller, newChain)
	}
}

func deepCopyChain(chain *Chain) *Chain {
	newNode := make([]*ReverseNode, len(chain.Nodes))
	for i, node := range chain.Nodes {
		newNode[i] = node
	}

	newEdge := make([]*Edge, len(chain.Edge))
	newChain := &Chain{
		Nodes: newNode,
		Edge:  newEdge,
	}
	return newChain

}
func (scanner *RawScanner) filterChains() {
	var res []*Chain

	visitedStr := make(map[string]struct{})
	for _, chain := range scanner.Chains {
		filt := false

		var str string
		for _, item := range chain.Nodes {
			if strings.Contains(item.Func.String(), "baidu/netdisk/clouddisk-golib/db") {
				filt = true
				break
			}

			str += item.Func.String()
		}

		if filt {
			continue
		}
		if _, ok := visitedStr[str]; !ok {
			visitedStr[str] = struct{}{}
			res = append(res, chain)
		}

	}
	scanner.Chains = res
}

func (scanner *RawScanner) showChains() {
	for _, chain := range scanner.Chains {
		scanner.showChain("", chain)
	}
}

func (scanner *RawScanner) showChain(pre string, chain *Chain) {
	rep := ""
	for _, node := range chain.Nodes {
		rep += node.Func.String() + " -> "
	}

	fmt.Printf("show chain, pre: %v, chains: %v\n", pre, rep)
}

func (scanner *RawScanner) showReverseGraphDetail(reverseNode *ReverseNode, pre string) error {
	pre += "-> " + reverseNode.Func.String()
	if len(reverseNode.Ins) == 0 {
		//fmt.Println("func chain: ", pre)
	}
	for _, edge := range reverseNode.Ins {
		scanner.showReverseGraphDetail(edge.Caller, pre)

	}
	return nil
}

func Reverse[T any](s []T) []T {
	for i, j := 0, len(s)-1; i < j; i, j = i+1, j-1 {
		s[i], s[j] = s[j], s[i]
	}
	return s
}

func (scanner *RawScanner) filtTargetFns(fns []*ssa.Function) []*ssa.Function {
	res := make([]*ssa.Function, 0)
	for _, fn := range fns {

		name := fn.Name()

		if name == "Exec" || name == "ExecContext" || name == "Query" || name == "QueryContext" ||
			name == "QueryRow" || name == "QueryRowContext" || name == "Prepare" || name == "PrepareContext" {
			res = append(res, fn)
		}
	}

	return res
}

/*
func (scanner *RawScanner) findTargetFns() ([]*ssa.Function, error) {
	pkgs := scanner.Ctx.Prog.AllPackages()
	for _, pkg := range pkgs {
		// fmt pkg.Pkg.Path()
		// 	strings.Contain(pkg.Pkg.Path(), "strings")
		if !strings.Contains(pkg.Pkg.Path(), "database/sql") {
			continue
		}
		fmt.Println("findTargetFns pkg path: ", pkg.Pkg.Path())
		for _, fn := range pkg.Members


	}
	return nil, nil
}
*/

/*
func (scanner *RawScanner) findTargetFns() (map[string][]*ssa.Function, error) {
	ctx := scanner.Ctx
	typeTargetFns := make(map[string][]*ssa.Function)
	for _, pkg := range ctx.Prog.AllPackages() {
		if !strings.Contains(pkg.Pkg.Path(), "database/sql") {
			continue
		}

		for _, mem := range pkg.Members {
			ssaType, ok := mem.(*ssa.Type)
			if !ok {
				continue
			}

			named, ok := ssaType.Type().(*types.Named)
			if !ok {
				continue // 忽略匿名类型
			}
			fmt.Println("database/sql: named: ", named)
			// value receiver 方法集
			methodSet := ctx.Prog.MethodSets.MethodSet(named)
			if methodSet != nil {
				for i := 0; i < methodSet.Len(); i++ {
					sel := methodSet.At(i)
					if sel == nil || sel.Obj() == nil {
						continue
					}
					fn := ctx.Prog.MethodValue(sel)
					if fn != nil {
						typeTargetFns[sel.String()] = append(typeTargetFns[sel.String()], fn)
					}
				}
			}

			//
			// pointer receiver 方法集
			ptrType := types.NewPointer(named)
			ptrMethodSet := ctx.Prog.MethodSets.MethodSet(ptrType)
			if ptrMethodSet != nil {
				for i := 0; i < ptrMethodSet.Len(); i++ {
					sel := ptrMethodSet.At(i)
					if sel == nil || sel.Obj() == nil {
						continue
					}
					fn := ctx.Prog.MethodValue(sel)
					if fn != nil {
						// fmt.Printf("  [pointer] Method: %s\n", fn.String())
						// targetFn = append(targetFn, fn)
						typeTargetFns[fn.Name()] = append(typeTargetFns[fn.Name()], fn)

					}
				}
			}
		}
	}

	return typeTargetFns, nil
}
*/

func (scanner *RawScanner) findTargetFns() (map[string][]*ssa.Function, error) {
	ctx := scanner.Ctx
	typeTargetFns := make(map[string][]*ssa.Function)

	for _, pkg := range ctx.Prog.AllPackages() {
		if !strings.Contains(pkg.Pkg.Path(), "database/sql") {
			continue
		}

		for _, mem := range pkg.Members {
			ssaType, ok := mem.(*ssa.Type)
			if !ok {
				continue
			}

			named, ok := ssaType.Type().(*types.Named)
			if !ok {
				continue
			}

			typeName := named.Obj().Name()
			fnMap := make(map[*ssa.Function]bool) // 避免重复添加

			// 收集值接收者方法
			methodSet := ctx.Prog.MethodSets.MethodSet(named)
			if methodSet != nil {
				for i := 0; i < methodSet.Len(); i++ {
					sel := methodSet.At(i)
					if sel == nil || sel.Obj() == nil {
						continue
					}
					fn := ctx.Prog.MethodValue(sel)
					if fn != nil && !fnMap[fn] {
						typeTargetFns[typeName] = append(typeTargetFns[typeName], fn)
						fnMap[fn] = true
					}
				}
			}

			// 收集指针接收者方法
			ptrType := types.NewPointer(named)
			ptrMethodSet := ctx.Prog.MethodSets.MethodSet(ptrType)
			if ptrMethodSet != nil {
				for i := 0; i < ptrMethodSet.Len(); i++ {
					sel := ptrMethodSet.At(i)
					if sel == nil || sel.Obj() == nil {
						continue
					}
					fn := ctx.Prog.MethodValue(sel)
					if fn != nil && !fnMap[fn] {
						typeTargetFns[typeName] = append(typeTargetFns[typeName], fn)
						fnMap[fn] = true
					}
				}
			}
		}
	}

	return typeTargetFns, nil
}

// ssh://<EMAIL>:8235/baidu/netdisk/nd-recent

func (scanner *RawScanner) ScanFn() error {

	return nil
}
