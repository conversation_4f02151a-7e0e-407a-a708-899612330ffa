package gorm

import "golang.org/x/tools/go/ssa"

type DB struct {
}

func (db *DB) MatchCreateCall() error {
	return nil
}

func (db *DB) MatchCreateInBatches() error {
	return nil
}

func (db *DB) MatchSaveCall() error {
	return nil
}

func (db *DB) MatchFirstCall() error {
	return nil
}

func (db *DB) MatchTakeCall() error {
	return nil
}

func (db *DB) MatchLastCall() error {
	return nil
}

func (db *DB) MatchFindCall(call *ssa.Call) error {
	return nil
}

func (db *DB) MatchFirstOrInitCall() error {
	return nil
}

func (db *DB) MatchFirstOrCreateCall() error {
	return nil
}

func (db *DB) MatchUpdateCall() error {
	return nil
}

func (db *DB) MatchUpdatesCall() error {
	return nil
}

func (db *DB) MatchUpdateColumnCall() error {
	return nil
}

func (db *DB) MatchUpdateColumnsCall() error {
	return nil
}

func (db *DB) MatchDeleteCall() error {
	return nil
}

func (db *DB) MatchCountCall() error {
	return nil
}

func (db *DB) MatchRowCall() error {
	return nil
}
func (db *DB) MatchRowsCall() error {
	return nil
}

func (db *DB) MatchScanCall() error {
	return nil
}

func (db *DB) MatchScanRowsCall() error {
	return nil
}

func (db *DB) MatchExecCall() error {
	return nil
}
