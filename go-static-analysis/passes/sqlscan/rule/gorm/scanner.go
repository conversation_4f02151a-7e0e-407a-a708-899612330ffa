package gorm

import (
	"fmt"
	"strings"

	"golang.org/x/tools/go/callgraph"

	"golang.org/x/tools/go/ssa"

	"golang.org/x/tools/go/callgraph/cha"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
)

type GormScanner struct {
	Ctx       *base.RuleContext
	Graph     *callgraph.Graph
	TargetFns []*ssa.Function

	Results []*base.ResultInfo
}

func NewGormScanner(ctx *base.RuleContext) *GormScanner {
	return &GormScanner{
		Ctx: ctx,
	}
}

func (scanner *GormScanner) buildGraph() {
	g := cha.CallGraph(scanner.Ctx.Prog)
	//g := rta.CallGraph(scanner.Ctx.Prog)
	scanner.Graph = g
}

func (scanner *GormScanner) showProg() {
	pkgs := scanner.Ctx.Prog.AllPackages()
	for _, pkg := range pkgs {
		_ = pkg
	}
}

func (scanner *GormScanner) findTargetFns() {
	var targetFns = make(map[*ssa.Function]struct{})
	for fn := range scanner.Graph.Nodes {
		if fn == nil || fn.Pkg == nil || fn.Pkg.Pkg == nil {
			continue
		}

		if !strings.Contains(fn.Pkg.Pkg.Path(), pkgAdapter(scanner.Ctx.ICode)) {
			continue
		}

		if scanner.isMatchGormCall(fn) {
			targetFns[fn] = struct{}{}
		}
	}

	var res = make([]*ssa.Function, 0)
	for fn := range targetFns {
		_, ok := scanner.Graph.Nodes[fn]
		if !ok {
			fmt.Printf("node not found, fn: %v\n", fn.String())
			continue
		}

		res = append(res, fn)
	}

}
func (scanner *GormScanner) isMatchGormCall(fn *ssa.Function) bool {
	for _, b := range fn.Blocks {
		for _, instr := range b.Instrs {
			call, ok := instr.(*ssa.Call)
			if !ok {
				continue
			}
			args := call.Call.Args
			if len(args) == 0 {
				continue
			}
			arg := args[0]
			if strings.Contains(arg.Type().String(), "gorm") {
				return true
			}

		}
	}
	return false
}

/*
func (scanner *GormScanner) buildRootFns() {

	for fn, node := range scanner.Graph.Nodes {
		if fn == nil || fn.Pkg == nil || fn.Pkg.Pkg == nil {
			continue
		}

		if !strings.Contains(fn.Pkg.Pkg.Path(), pkgAdapter(scanner.Ctx.ICode)) {
			continue
		}

		if len(node.In) == 0 {
			scanner.RootFns = append(scanner.RootFns, fn)
		}
	}

	fmt.Println("scanner.Ctx.ICode: ", scanner.Ctx.ICode)
	fmt.Println("len(scanner.RootFns): ", len(scanner.RootFns))

	for _, rootFn := range scanner.RootFns {
		fmt.Println("rootFn: ", rootFn)

	}

}
*/
