package gorm

import (
	"strings"
)

type Statement struct {
	*DB
	Table        string
	Model        interface{}
	SQL          strings.Builder
	Vars         []interface{}
	Ski<PERSON><PERSON><PERSON>s    bool
	CurDestIndex int
}

type join struct {
	Name  string
	Conds []interface{}
}

// Write write string
func (stmt *Statement) WriteString(str string) (int, error) {
	return stmt.SQL.WriteString(str)
}

// Write write string
func (stmt *Statement) WriteByte(c byte) error {
	return stmt.SQL.WriteByte(c)
}
