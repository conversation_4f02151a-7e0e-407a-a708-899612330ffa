package clause

import "strings"

type Delete struct {
	Modifier string
}

type Clause struct {
	Name string
	// Expression Expression
}

func (d Delete) Name() string {
	return "DELETE"
}

func (d Delete) Build(builder strings.Builder) {
	builder.WriteString("DELETE")

	if d.Modifier != "" {
		builder.WriteByte(' ')
		builder.WriteString(d.Modifier)
	}
}

func (d Delete) MergeClause(clause *Clause) {
	clause.Name = ""
	// clause.Expression = d
}

/*
type SQLInfo struct {
	sql string
	icode string
	filename string
	line_start string
	line_end   string
	is_diff bool
}


*/
