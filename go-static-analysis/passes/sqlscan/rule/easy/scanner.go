package easy

import (
	"errors"
	"fmt"
	"go/types"
	"log"
	"strings"

	"golang.org/x/tools/go/ssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/callgraph"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/callgraph/static"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/utils"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

const (
	MaxDepth        = 32
	EasySDKPkg      = "icode.baidu.com/baidu/netdisk/easy-go-sdk"
	BaiduRepoSubStr = "8235/baidu"
)

type SQLEasyScanner struct {
	Ctx *base.RuleContext

	DFSMaxDepth     int
	RepoName        string
	ICodeName       string
	DaoNames        []string
	ControllerNames []string

	Graph             *callgraph.Graph
	APIFunctions      map[*ssa.Function]string        // api函数
	SQLEntryFunctions map[*ssa.Function]struct{}      // dao层函数
	TableNames        map[*ssa.Type]map[string]string // map[string]string中，结构体成员名称到 sql名称的映射，以及key:"sql_name", value:sqlNmae
	TypeMap           map[string]*ssa.Type
	FuncsVisit        map[*ssa.Function]struct{}
	ChainsVisit       map[string]struct{}
	ChainsToSQLMap    map[string]*SQLStruct // key: chains name, value: sql stmt
	IsDebug           bool

	Results []*base.ResultInfo

	ConfigData ConfigDataResponse
}

func NewSQLEasyScanner(maxDepth int, ctx *base.RuleContext) *SQLEasyScanner {
	return &SQLEasyScanner{
		DFSMaxDepth:       maxDepth,
		TypeMap:           make(map[string]*ssa.Type),
		TableNames:        make(map[*ssa.Type]map[string]string),
		APIFunctions:      make(map[*ssa.Function]string),
		SQLEntryFunctions: make(map[*ssa.Function]struct{}),
		FuncsVisit:        make(map[*ssa.Function]struct{}),
		ChainsVisit:       make(map[string]struct{}),
		ChainsToSQLMap:    make(map[string]*SQLStruct),
		Ctx:               ctx,
	}
}

func (r *SQLEasyScanner) InitAll() error {

	r.initGraph() // 初始化函数调用链
	r.initControllerNames()
	r.initDaoNames()
	r.initTablesNames()

	if err := r.initDaoFunctions(); err != nil {
		return err
	}
	if err := r.initRepoNameAndICodeName(); err != nil {
		return err
	}

	if err := r.initRepoNameAndICodeName(); err != nil {
		fmt.Println("init repo name and icode name error, err is %v", err)
		return err
	}

	r.initConfigData()

	return nil
}

func (r *SQLEasyScanner) initControllerNames() {
	var controllerNames []string

	const (
		actionPkgName = "action" // controller 所在包
		structSuffix  = "Controller"
	)
	for _, pkg := range r.Ctx.Prog.AllPackages() {
		if pkg.Pkg.Name() != actionPkgName {
			continue
		}
		for _, mem := range pkg.Members {
			ssaType, ok := mem.(*ssa.Type)
			if !ok {
				continue
			}
			if !strings.HasSuffix(ssaType.Name(), structSuffix) {
				continue
			}
			controllerName := strings.TrimSuffix(ssaType.Name(), structSuffix)
			controllerNames = append(controllerNames, controllerName)
		}
	}

	r.ControllerNames = controllerNames
}

func (r *SQLEasyScanner) initDaoNames() {
	var daoNames []string

	const (
		actionPkgName = "dao" // controller 所在包
		structSuffix  = "Dao"
	)
	for _, pkg := range r.Ctx.Prog.AllPackages() {
		if pkg.Pkg.Name() != actionPkgName {
			continue
		}
		for _, mem := range pkg.Members {
			ssaType, ok := mem.(*ssa.Type)
			if !ok {
				continue
			}
			if !strings.HasSuffix(ssaType.Name(), structSuffix) {
				continue
			}
			daoNames = append(daoNames, ssaType.Name())
		}
	}

	r.DaoNames = daoNames
}

func (r *SQLEasyScanner) initTablesNames() {
	tablePkgName := "entity" // controller 所在包
	for _, pkg := range r.Ctx.Prog.AllPackages() {
		if pkg.Pkg.Name() != tablePkgName {
			continue
		}

		for _, mem := range pkg.Members {
			ssaType, ok := mem.(*ssa.Type)
			if !ok {
				continue
			}

			if named, ok := ssaType.Type().(*types.Named); ok {
				if s, ok := named.Underlying().(*types.Struct); ok {
					if s.NumFields() != 0 && s.Field(0).Name() == "Entity" {
						r.TableNames[ssaType] = make(map[string]string)
						key := getShortType(ssaType.Type())
						if key == "" {
							continue
						}
						r.TypeMap[key] = ssaType
						for i := 0; i < s.NumFields(); i++ {
							tagMap := parseTag(s.Tag(i))
							if i == 0 {
								r.TableNames[ssaType]["table_name"] = tagMap["table_name"]
							}
							sqlName, ok := tagMap["sql"]
							if ok && sqlName != "table_name" {
								r.TableNames[ssaType][sqlName] = s.Field(i).Name()
							}
						}
					}
				}
			}
		}
	}
}

func parseTag(tag string) map[string]string {
	tagMap := make(map[string]string)

	tagPairs := strings.Split(tag, " ")
	for _, pair := range tagPairs {
		kv := strings.Split(pair, ":")
		if len(kv) != 2 {
			continue
		}

		key := strings.TrimSpace(kv[0])
		value := strings.TrimSpace(kv[1])

		tagMap[key] = value
	}

	return tagMap
}

func (r *SQLEasyScanner) getCotrollerObjects() (map[string]types.Type, error) {
	res := make(map[string]types.Type)
	for _, name := range r.ControllerNames {
		name := name + "Controller"
		typ, err := r.getCotorlllerObject(name)
		if err != nil {
			continue
		}

		if typ == nil {
			continue
		}

		realType := typ
		if _, ok := typ.(*types.Named); ok {
			realType = types.NewPointer(typ)
		}

		res[name] = realType
	}
	return res, nil
}

func (r *SQLEasyScanner) getCotorlllerObject(controllerName string) (types.Type, error) {
	for _, pkg := range r.Ctx.Prog.AllPackages() {
		if pkg.Pkg.Name() != "action" {
			continue
		}

		obj := pkg.Pkg.Scope().Lookup(controllerName)
		if obj != nil {
			return obj.Type(), nil
		}
	}
	return nil, fmt.Errorf("the controllerName %s not found", controllerName)
}

func (r *SQLEasyScanner) getDaoObjects() (map[string]types.Type, error) {
	res := make(map[string]types.Type)
	for _, name := range r.DaoNames {
		typ, err := r.getDaoObject(name)
		if err != nil {
			continue
		}

		if typ == nil {
			continue
		}

		realType := typ
		if _, ok := typ.(*types.Named); ok {
			realType = types.NewPointer(typ)
		}

		res[name] = realType
	}
	return res, nil
}

func (r *SQLEasyScanner) getDaoObject(daoName string) (types.Type, error) {
	for _, pkg := range r.Ctx.Prog.AllPackages() {
		if pkg.Pkg.Name() != "dao" {
			continue
		}
		obj := pkg.Pkg.Scope().Lookup(daoName)
		if obj != nil {
			return obj.Type(), nil
		}
	}
	return nil, fmt.Errorf("the controllerName %s not found", daoName)
}

func (r *SQLEasyScanner) initRepoNameAndICodeName() error {
	repoName, err := utils.ExecCommandWithTimeout("go list -m")
	if err != nil {
		return err
	}
	r.RepoName = strings.TrimSpace(repoName)

	icodeName, err := utils.ExecCommand(`git remote -v | head -1 | awk {'print $2'}`)
	if err != nil {
		return err
	}
	icodeName = strings.TrimSpace(icodeName)
	i := strings.Index(icodeName, BaiduRepoSubStr)
	if i == -1 || i+5 < 0 {
		return errors.New("get icode name err")
	}
	r.ICodeName = icodeName[i+5:]
	return nil
}

func (r *SQLEasyScanner) isEasyGoSDKFn(fn *ssa.Function) bool {
	if fn == nil {
		return false
	}
	if strings.Contains(fn.String(), EasySDKPkg) {
		return true
	}
	return false
}

func (r *SQLEasyScanner) isLocalFn(fn *ssa.Function) bool {
	if fn == nil {
		return false
	}
	if strings.Contains(fn.String(), r.RepoName) {
		return true
	}
	return false
}

func (r *SQLEasyScanner) initAPIFunctions() error {
	controllerNameMap, err := r.getCotrollerObjects()
	if err != nil {
		return err
	}

	const apiFunctionSuffix = "Core"
	// 遍历每个结构体
	for controller, typ := range controllerNameMap {
		methodSet := r.Ctx.Prog.MethodSets.MethodSet(typ)
		if methodSet == nil {
			continue
		}

		// groupName := strings.TrimSuffix(controllerName, "Controller")

		// 遍历该结构体的所有方法以获取所需的方法
		for i := 0; i < methodSet.Len(); i++ {
			selection := methodSet.At(i)
			if selection == nil || selection.Obj() == nil {
				continue
			}

			name := selection.Obj().Name()
			if strings.HasSuffix(name, apiFunctionSuffix) && selection.Obj().Exported() {
				fn := r.Ctx.Prog.MethodValue(selection)
				controller = strings.TrimSuffix(controller, "Controller")

				r.APIFunctions[fn] = controller
			}
		}
	}

	return nil
}

// dao包的函数
func (r *SQLEasyScanner) initDaoFunctions() error {
	fmt.Println("initDaoFunctions start")
	daoNameMap, err := r.getDaoObjects()
	if err != nil {
		log.Println(err)
		return err
	}

	// 遍历每个结构体
	for _, typ := range daoNameMap {
		methodSet := r.Ctx.Prog.MethodSets.MethodSet(typ)
		if methodSet == nil {
			continue
		}

		// 遍历该结构体的所有方法以获取所需的方法
		for i := 0; i < methodSet.Len(); i++ {
			selection := methodSet.At(i)
			if selection == nil || selection.Obj() == nil {
				continue
			}

			fmt.Println("selection: ", selection.String())

			fn := r.Ctx.Prog.MethodValue(selection)

			if selection.Obj().Exported() && fn.Name() != "Model" {
				fn := r.Ctx.Prog.MethodValue(selection)
				r.SQLEntryFunctions[fn] = struct{}{}
			}
		}
	}

	fmt.Println("initDaoFunctions end")

	return nil
}

func (r *SQLEasyScanner) initGraph() {
	graph := static.CallGraph(r.Ctx.Prog)
	r.Graph = graph
}

func (r *SQLEasyScanner) initConfigData() {
	url := "http://10.11.97.54/dbinfo?method=queryConfig"
	configData, err := queryConfigResponse(url, r.ICodeName)
	if err != nil {
		golog.Warn("queryConfigResponse err, err is %v", err)
		return
	}

	r.ConfigData = *configData

	return
}
