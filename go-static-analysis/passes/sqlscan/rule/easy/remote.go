package easy

import (
	"encoding/json"
	"io"
	"net/http"
	"time"
)

type ConfigDataItem struct {
	AddressType string `json:"address_type"`
	Database    string `json:"database"`
	GroupName   string `json:"group_name"`
	Instance    string `json:"instance"`
	To          string `json:"to"`
}

type ConfigDataResponse struct {
	Data struct {
		Data []*ConfigDataItem `json:"data"`
	} `json:"data"`

	Errno     int    `json:"errno"`
	Newno     string `json:"newno"`
	RequestID int64  `json:"request_id"`
}

func queryConfigResponse(url string, icode string) (*ConfigDataResponse, error) {
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	url = url + "&icode=" + icode
	response, err := client.Post(url, "application/json", nil)
	if err != nil {
		return nil, err
	}

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}

	defer response.Body.Close()

	var configDataResp ConfigDataResponse
	err = json.Unmarshal(body, &configDataResp)
	if err != nil {
		return nil, err
	}

	return &configDataResp, nil
}
