package easy

import (
	"fmt"

	"golang.org/x/tools/go/ssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/callgraph"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
)

func (r *SQLEasyScanner) Run() {
	if err := r.InitAll(); err != nil {
		fmt.Printf("init all failed, err: %v\n", err)
		return
	}
	r.initSQLFunctionForCallChain()
}

func (r *SQLEasyScanner) initSQLFunctionForCallChain() {
	g := r.Graph
	for entryFn := range r.SQLEntryFunctions {
		entryNode := g.Nodes[entryFn]
		chainsName := entryNode.Func.String()

		r.runSQLGenerationDFS(base.NewFuncContext(entryFn), chainsName, entryNode, 32)

		r.IsDebug = false
	}

}

func (r *SQLEasyScanner) runSQLGenerationDFS(funcContext *base.FuncContext, chainNames string, curNode *callgraph.Node, depth int) {
	if depth > r.DFSMaxDepth {
		return
	}

	if curNode == nil {
		return
	}

	if r.isSQLEntryFn(curNode) {
		r.initSQLStmt(funcContext, chainNames, curNode)
		return
	}

	for _, out := range curNode.Out {

		callee := out.Callee
		calleeName := callee.Func.Name()
		if !r.isLocalFn(callee.Func) && !r.isEasyGoSDKFn(callee.Func) {
			continue
		}

		chainNames = chainNames + "-" + calleeName
		if !r.isSQLEntryFn(callee) {
			r.runSQLGenerationDFS(funcContext, chainNames, callee, depth+1)
			continue
		}

		r.ChainsVisit[chainNames] = struct{}{}
		r.initSQLStmt(funcContext, chainNames, callee)

		// r.initSQLStmt(funcContext, chainNames, callee)

	}
}

func (r *SQLEasyScanner) isSQLEntryFn(node *callgraph.Node) bool {
	_, ok := r.SQLEntryFunctions[node.Func]
	return ok
}

func (r *SQLEasyScanner) initSQLStmt(funcContext *base.FuncContext, chainsName string, curNode *callgraph.Node) {
	fn := curNode.Func
	for _, blocks := range fn.DomPreorder() {
		for _, instr := range blocks.Instrs {
			call, ok := instr.(*ssa.Call)
			if !ok {
				continue
			}

			callFn := call.Call.StaticCallee()
			if callFn == nil {
				continue
			}

			if callFn.Name() != "Model" {
				continue
			}
			if len(call.Call.Args) < 2 {
				continue
			}

			daoInterface, ok := call.Call.Args[1].(*ssa.MakeInterface)
			if !ok {
				continue
			}
			x := daoInterface.X
			if x == nil {
				continue
			}

			typ := x.Type()
			key := getShortType(typ)
			if key == "" {
				continue
			}

			ssatype, ok := r.TypeMap[key]
			if !ok {
				continue
			}
			ms := NewSSAModuleStruct(r, ssatype, curNode, chainsName)
			if err := ms.MatchModel(funcContext, call); err != nil {
				fmt.Printf("match model err: %v ", err)
			}
		}
	}
}
