package easy

import (
	"fmt"
	"go/types"
	"reflect"
	"strings"

	"golang.org/x/tools/go/ssa"
)

const APIMsgCollectionURL = "http://10.138.34.59:8089/code/ssa/func"
const ConnectTimeout = 1000
const ReadWriteTimeout = 3000

const (
	// 会生成 sql 的函数
	FnTypeGet             = "Get"
	FnTypeAll             = "All"
	FnTypeCreate          = "Create"
	FnTypeDelete          = "Delete"
	FnTypeUpdate          = "Update"
	FnTypeLimit           = "Limit"
	FnTypeSetMSFlag       = "SetMSFlag"
	FnTypeCount           = "Count"
	FnTypeSum             = "Sum"
	FnTypeIgnoreCreate    = "IgnoreCreate"
	FnTypeReplaceCreate   = "ReplaceCreate"
	FnTypeCreateList      = "CreateList"
	FnTypeCreateListAsync = "CreateListAsync" // TODO

	// 用于拼接 sql 的函数
	FnTypeOrderby    = "OrderBy"
	FnTypeOffset     = "Offset"
	FnTypeWhere      = "Where"
	FnTypeSelect     = "Select"
	FnTypeIn         = "In"
	FnTypeOrIn       = "OrIn"
	FnTypeAndIn      = "AndIn"
	FnTypeForceIndex = "ForceIndex"
	FnTypeTableName  = "TableName"
	FnTypeHint       = "Hint"

	// 难以适配
	FnTypeTable    = "Table"    // 该函数逻辑由传入一个匿名函数去确认，ssa难以适配
	FnTypePage     = "Page"     // 暂不适配，该函数调用了 Count, Limit, All，这里没有对应的sql对应该函数的语义，不予生成。
	FnTypeRelation = "Relation" //
)

func getShortType(typ types.Type) string {
	slice := strings.Split(typ.String(), "/")
	if len(slice) == 0 {
		return ""
	}

	return slice[len(slice)-1]
}

func inSlice(k string, s []string) bool {
	for _, v := range s {
		if k == v {
			return true
		}
	}
	return false
}

func (b *SSAModelStruct) getSQLFields(entity *ssa.Type, zeroValues []string) []string {
	tablesNames := b.Scanner.TableNames
	dtoMap, ok := tablesNames[entity]
	if !ok {
		return nil
	}
	sqlFieldMap := copyMapWithoutTableName(dtoMap)

	var res []string
	for _, v := range zeroValues {
		if _, ok := sqlFieldMap[v]; ok {
			res = append(res, v)
		}
	}

	if len(res) == 0 {
		return nil
	}

	return res
}

func IsOptionalInstr(instr ssa.Instruction) bool {
	return false
	/*
		comment := instr.Block().Comment
		if comment == "if.then" || comment == "if.else" {
			return true
		}
		return false
	*/
}

func getConstString(value ssa.Value) string {
	switch value := value.(type) {
	case *ssa.Const:
		if value.Value == nil {
			return ""
		}
		return value.Value.ExactString()
	default:
		fmt.Println("get const string failed, value type is", reflect.TypeOf(value))
		return ""
	}
}
