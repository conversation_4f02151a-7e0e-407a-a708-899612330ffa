package easy

import (
	"errors"
	"fmt"
	"go/types"
	"reflect"
	"strconv"
	"strings"

	"golang.org/x/tools/go/ssa"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/easy/easycallgraph/pkg/callgraph"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
)

type DtoMap map[string]string

type SQLStruct struct {
	RequiredSQL []string `json:"required_sql,omitempty"`
	OptionalSQL []string `json:"optional_sql,omitempty"`

	DbName string
}

func NewSQLStruct() *SQLStruct {
	return &SQLStruct{
		RequiredSQL: make([]string, 0),
		OptionalSQL: make([]string, 0),
	}
}

// SQL构建器
type SQLBuilder struct {
	fields       string
	table        string
	forceIndex   string
	where        string
	order        string
	limit        string
	offset       string
	hint         string
	insertPrefix string
	prefix       string
	// Extra args to be substituted in the *where* clause

	OptionalField OptionalField
	IsOptionalSQL []string
	args          []interface{}

	dbName string
}

type OptionalField struct {
	forceIndex string
	table      string
	where      string // optional
	order      string // optional
	limit      string // optional
	offset     string // optional
	hint       string // optional
}

// 一个Dao结构体对应一个Entity, Dao.Model()调用对应一个SQL语句
type SSAModelStruct struct {
	DoFns      []*DoFn
	ChainsName string
	CurNode    *callgraph.Node
	VisitInstr map[string]struct{}
	Scanner    *SQLEasyScanner
	Entity     *ssa.Type

	SQLBuilder
}

type DoFn struct {
	Fn         func() string
	FnType     string
	IsOptional bool
}

func NewSSAModuleStruct(runner *SQLEasyScanner, entity *ssa.Type, curNode *callgraph.Node, chainsName string) *SSAModelStruct {
	return &SSAModelStruct{
		ChainsName: chainsName,
		CurNode:    curNode,
		Entity:     entity,
		Scanner:    runner,
		VisitInstr: map[string]struct{}{},
	}
}

// model调用
func (b *SSAModelStruct) MatchModel(funcContext *base.FuncContext, modelCall *ssa.Call) error {
	if modelCall.Common().StaticCallee().Name() != "Model" {
		return errors.New("not model call")
	}

	args := modelCall.Call.Args
	if len(args) != 3 {
		fmt.Println("args len", len(args), "args: ", args)
		return errors.New("model call args len isn`t 3")
	}

	arg := args[1]
	mi, ok := arg.(*ssa.MakeInterface)
	if !ok {
		return errors.New("arg make interface failed")
	}

	key := getShortType(mi.X.Type())
	if key == "" {
		return fmt.Errorf("get mi.X.Type() failed, mi.X.Type()=%v", mi.X.Type())
	}

	ssaType, ok := b.Scanner.TypeMap[key]
	if !ok {
		return errors.New("not exist, type is " + args[1].Type().String())
	}

	kvMap, ok := b.Scanner.TableNames[ssaType]
	if !ok {
		return errors.New("not exit ssa type")
	}

	b.table = kvMap["table_name"]
	b.dbName = kvMap["db_name"]

	b.ModelFnReferrers(modelCall)
	if len(b.DoFns) != 0 {
		sqlStruct := NewSQLStruct()
		for _, doFn := range b.DoFns {
			res := doFn.Fn()
			if res == "" {
				continue
			}

			if doFn.IsOptional {
				sqlStruct.OptionalSQL = append(sqlStruct.OptionalSQL, res)
			} else {
				sqlStruct.RequiredSQL = append(sqlStruct.RequiredSQL, res)
			}
		}

		if b.OptionalField.hint != "" {
			sqlStruct.OptionalSQL = append(sqlStruct.OptionalSQL, b.OptionalField.hint)
		}

		if b.OptionalField.table != "" {
			if b.OptionalField.forceIndex != "" {
				b.OptionalField.table += fmt.Sprintf(" force index(%s)", b.OptionalField.forceIndex)
			}
			sqlStruct.OptionalSQL = append(sqlStruct.OptionalSQL, "TABLE: "+b.OptionalField.table)
		}

		if b.OptionalField.where != "" {
			sqlStruct.OptionalSQL = append(sqlStruct.OptionalSQL, b.OptionalField.where)
		}

		if b.OptionalField.order != "" {
			sqlStruct.OptionalSQL = append(sqlStruct.OptionalSQL, b.optionalOrderFormat())
		}
		if b.OptionalField.limit != "" {
			sqlStruct.OptionalSQL = append(sqlStruct.OptionalSQL, b.optionalLimitFormat())
		}
		if b.OptionalField.offset != "" {

			sqlStruct.OptionalSQL = append(sqlStruct.OptionalSQL, b.optionalOffsetFormat())
		}

		b.Scanner.ChainsToSQLMap[b.ChainsName] = sqlStruct
		fileName := b.Scanner.Ctx.Prog.Fset.Position(b.CurNode.Func.Pos()).Filename
		fnName := b.CurNode.Func.String()
		sql := ""
		if len(sqlStruct.RequiredSQL) != 0 {
			sql = sqlStruct.RequiredSQL[0]
		}

		// result := base.BuildCallerInfo(fileName, fnName, fileName+fnName, sql)
		key := fmt.Sprintf("%s%s%d", fileName, fnName, funcContext.CallIndex)
		funcContext.CallIndex++
		result := base.BuildResultInfo(fileName, fnName, key, sql, "easy")

		extraInfo := b.getResultExtraInfo()
		instance := ""
		database := ""
		if extraInfo != nil {
			instance = extraInfo.Instance
			database = extraInfo.Database
		}
		result.AddExtraInfo(b.dbName, b.table, instance, database)

		b.Scanner.Results = append(b.Scanner.Results, result)
		b.Scanner.ChainsToSQLMap[b.ChainsName] = sqlStruct

	}

	return nil
}

func (b *SSAModelStruct) getResultExtraInfo() *ConfigDataItem {
	slice := b.Scanner.ConfigData.Data.Data
	for _, item := range slice {
		if item.GroupName == b.dbName {
			return item
		}
	}
	return nil
}

func (b *SSAModelStruct) ModelFnReferrers(value ssa.Value) {
	instrs := value.Referrers()
	if instrs == nil || *instrs == nil {
		return
	}

	for _, instr := range *instrs {
		if _, ok := b.VisitInstr[instr.String()]; ok {
			continue
		}

		b.VisitInstr[instr.String()] = struct{}{}

		switch instr := instr.(type) {
		case *ssa.Call:
			fn := instr.Call.StaticCallee()
			if fn == nil {
				return
			}

			switch fn.Name() {
			case FnTypeCreate:
				b.MatchCreateString(instr)
			case FnTypeWhere:
				b.MatchWhereCall(instr)
			case FnTypeSelect:
				b.MatchSelectCall(instr)
			case FnTypeUpdate:
				b.MatchUpdateString(instr)
			case FnTypeDelete:
				b.MatchDeleteString(instr)
			case FnTypeCreateList:
				b.MatchCreateListString(instr)
			case FnTypeAll:
				b.MatchAllString(instr)
			case FnTypeAndIn:
				b.MatchAndInCall(instr)
			case FnTypeLimit:
				b.MatchLimitCall(instr)
			case FnTypeSetMSFlag:
				b.MatchSetMsFlagCall(instr)
			case FnTypeCount:
				b.MatchCountString(instr)
			case FnTypeSum:
				b.MatchSumString(instr)
			case FnTypeIgnoreCreate:
				b.MatchIgnoreCreateString(instr)
			case FnTypeReplaceCreate:
				b.MatchReplaceCreateString(instr)
			case FnTypeCreateListAsync:
				// 该函数对应easy-go-sdk中用了事务，单条 mysql 没法体现, 用CreateList 代替（CreateList 和 Create的区别是Value会多一组）
				b.MatchCreateListString(instr)
			case FnTypeOrderby:
				b.MatchOrderbyeCall(instr)
			case FnTypeOffset:
				b.MatchOffsetCall(instr)
			case FnTypeForceIndex:
			case FnTypeIn:
				b.MatchInCall(instr)
			case FnTypeOrIn:
				b.MatchOrInCall(instr)
			case FnTypeTableName:
				b.MatchTableNameCall(instr)
			case FnTypeHint:
				b.MatchHintCall(instr)
			default:
				fmt.Println("call name: ", instr.String(), "\ncur node: ", b.CurNode.Func.String(), "chain name: ", b.ChainsName)
			}
		case *ssa.Phi:
			phi := instr
			b.ModelFnReferrers(phi)
		default:
			fmt.Println("typeof instr: ", reflect.TypeOf(instr))
		}
	}
}

func printValueString(prefix string, value ssa.Value) {
	fmt.Println(prefix, "typeof value: ", reflect.TypeOf(value), ", string: ", value.String())
}

// 函数是否只有一个返回值, 且是否为[]string
func getFnReturnValue(fn *ssa.Function) ([]string, bool) {
	if !checkFnReturnValid(fn) {
		return nil, false
	}

	if len(fn.Blocks) != 1 {
		return nil, false
	}

	block := fn.Blocks[0]
	if len(block.Instrs) == 0 {
		return nil, false
	}
	instr := block.Instrs[0]
	alloc, ok := instr.(*ssa.Alloc)
	if !ok {
		return nil, false
	}
	allocType := alloc.Type()
	ptr, ok := allocType.(*types.Pointer)
	if ok {
		allocType = ptr.Elem()
	}
	var isArrayType bool
	var size int64
	var typ string
	switch allocType := allocType.(type) {
	case *types.Array:
		array := allocType
		isArrayType = true
		typ = array.Elem().String()
		size = array.Len()
	default:
		fmt.Println("allocType reflect typeof : ", reflect.TypeOf(allocType))
	}

	if !isArrayType || size <= 0 || typ != "string" {
		return nil, false
	}
	array := make([]string, size, size)
	for _, instr := range *alloc.Referrers() {
		switch instr := instr.(type) {
		case *ssa.IndexAddr:
			indexStr := getConstString(instr.Index)
			if indexStr == "" {
				return nil, false
			}
			index, err := strconv.Atoi(indexStr)
			if err != nil {
				return nil, false
			}
			if int64(index) >= size {
				return nil, false
			}

			referrers := *instr.Referrers()
			if len(referrers) == 0 || len(referrers) >= 2 {
				fmt.Println("referrers >= 2 || == 0")
				return nil, false
			}

			storeInstr, ok := referrers[0].(*ssa.Store)
			if !ok {
				return nil, false
			}

			valStr := getConstString(storeInstr.Val)
			if valStr == "" {
				return nil, false
			}
			valStr = strings.Trim(valStr, "\"")
			array[index] = valStr
		}
	}

	return array, true
}

func sliceIsConst(value ssa.Value) ([]string, bool) {
	alloc, ok := value.(*ssa.Alloc)
	if !ok {
		return nil, false
	}
	allocType := alloc.Type()
	ptr, ok := allocType.(*types.Pointer)
	if ok {
		allocType = ptr.Elem()
	}
	var isArrayType bool
	var size int64
	var typ string
	switch allocType := allocType.(type) {
	case *types.Array:
		array := allocType
		isArrayType = true
		typ = array.Elem().String()
		size = array.Len()
	default:
		fmt.Println("allocType reflect typeof : ", reflect.TypeOf(allocType))
	}

	if !isArrayType || size <= 0 || typ != "string" {
		return nil, false
	}
	array := make([]string, size, size)
	for _, instr := range *alloc.Referrers() {
		switch instr := instr.(type) {
		case *ssa.IndexAddr:
			indexStr := getConstString(instr.Index)
			if indexStr == "" {
				return nil, false
			}
			index, err := strconv.Atoi(indexStr)
			if err != nil {
				return nil, false
			}
			if int64(index) >= size {
				return nil, false
			}

			referrers := *instr.Referrers()
			if len(referrers) == 0 || len(referrers) >= 2 {
				fmt.Println("referrers >= 2 || == 0")
				return nil, false
			}

			storeInstr, ok := referrers[0].(*ssa.Store)
			if !ok {
				return nil, false
			}

			valStr := getConstString(storeInstr.Val)
			if valStr == "" {
				return nil, false
			}
			array[index] = valStr
		}
	}
	return array, true
}

func checkFnReturnValid(fn *ssa.Function) bool {
	tuple := fn.Signature.Results()
	if tuple.Len() != 1 {
		return false
	}

	v := tuple.At(0)
	slice, ok := v.Type().(*types.Slice)
	if !ok {
		return false
	}
	if slice.Elem().String() != "string" {
		return false
	}
	return true
}

func copyMapWithoutTableName(input map[string]string) map[string]string {
	res := make(map[string]string)
	for k, v := range input {
		res[k] = v
	}
	delete(res, "table_name")
	return res
}

func (b *SSAModelStruct) getSQLAddMap(dto *ssa.Type, excludes []string) (DtoMap, error) {
	tablesNames := b.Scanner.TableNames
	dtoMap, ok := tablesNames[dto]
	if !ok {
		return nil, errors.New("not found dto map")
	}
	sqlFieldMap := copyMapWithoutTableName(dtoMap)

	for _, field := range excludes {
		delete(sqlFieldMap, field)
	}

	return sqlFieldMap, nil
}

func (b *SSAModelStruct) getSQLModMap(zeroValues []string) (DtoMap, error) {
	if len(zeroValues) == 0 {
		return nil, errors.New("return err failed")
	}

	dtoMap := make(DtoMap)
	for _, field := range zeroValues {
		dtoMap[field] = field
	}
	return dtoMap, nil
}

func (b *SSAModelStruct) getSliceForUpdate(slice *ssa.Slice) []string {
	var res []string
	sliceX := slice.X
	if sliceX == nil {
		return nil
	}

	sliceStr, ok := sliceIsConst(sliceX)
	if ok {
		return sliceStr
	}

	for _, instr := range *sliceX.Referrers() {
		switch instr := instr.(type) {
		case *ssa.FieldAddr:
			referrers := instr.Referrers()
			if len(*referrers) == 1 {
				referInstr := (*referrers)[0]
				store, ok := referInstr.(*ssa.Store)
				if !ok {
					continue
				}
				c, ok := store.Val.(*ssa.Const)
				if !ok {
					continue
				}
				res = append(res, c.Value.ExactString())
			}
		// 适配调用常数函数情形
		/*
				t0 = local UpdateExtra (extra)                             *UpdateExtra
				*t0 = extra
				t1 = meta == nil:*icode.baidu.com/baidu/netdisk/poms-meta-tangram/entity.PomsMetaBaseonUniqueKey bool


				t12 = &t10[0:int]                                               *uint32
				t13 = *t12                                                       uint32
				t14 = &t0.UpdateFields [#2]                                   *[]string
				t15 = *t14


				t74 = (*icode.baidu.com/baidu/netdisk/easy-go-sdk.ModelStruct).Where(t67, "id = ?":string, t73...) *icode.baidu.com/baidu/netdisk/easy-go-sdk.ModelStruct
				jump 11
			11:                                                             if.done P:2 S:2
				t75 = phi [9: t67, 10: t74] #model *icode.baidu.com/baidu/netdisk/easy-go-sdk.ModelStruct
				t76 = local time.Time (startTime)                            *time.Time
				t77 = time.Now()                                              time.Time
				*t76 = t77
				t78 = (*icode.baidu.com/baidu/netdisk/easy-go-sdk.ModelStruct).Update(t75, t15...) (affected int64, err error)
				t79 = extract t78 #0                                              int64
				t80 = extract t78 #1                                              error
				t81 = &t16.CostTime [#1]                                         *int64
				t82 = *t76                                                    time.Time
				t83 = time.Since(t82)                                     time.Duration
		*/
		case *ssa.UnOp: // 该对应的value为函数参数，暂不处理
			/*
				unOpX := instr.X
				switch unOpX := unOpX.(type) {
				case (*ssa.FieldAddr): // 对应 t14
					fieldAddrX := unOpX.X
					switch fieldAddrX := fieldAddrX.(type) {
					case *ssa.Alloc: // 对应 t0
						alloc := fieldAddrX
						for _, referrer := range *alloc.Referrers() {
							switch referrer := referrer.(type) {
							case *ssa.Store:
								store := referrer
								storeVal := store.Val
								fmt.Println("storeVal: ", storeVal, ", type: ", reflect.TypeOf(storeVal))
							}
						}
					}
				default:
					fmt.Println("unOpX type is ", reflect.TypeOf(unOpX))
				}
			*/
		default:
			fmt.Println("instr type is ", reflect.TypeOf(instr))
		}
	}

	return res
}

func (b *SSAModelStruct) getSlice(slice *ssa.Slice) []string {
	var res []string
	var ops []*ssa.Value
	ops = slice.Operands(ops)
	for _, opPtr := range ops {
		op := *opPtr
		if op == nil {
			continue
		}
		instrsPtr := op.Referrers()
		if instrsPtr == nil {
			continue
		}

		for _, instr := range *instrsPtr {
			switch instr := instr.(type) {
			case *ssa.FieldAddr:
				referrers := instr.Referrers()
				if len(*referrers) == 1 {
					referInstr := (*referrers)[0]
					store, ok := referInstr.(*ssa.Store)
					if !ok {
						continue
					}
					c, ok := store.Val.(*ssa.Const)
					if !ok {
						continue
					}
					res = append(res, c.Value.ExactString())
				}

			// 适配调用常数函数情形
			/*
					t0 = local UpdateExtra (extra)                             *UpdateExtra
					*t0 = extra
					t1 = meta == nil:*icode.baidu.com/baidu/netdisk/poms-meta-tangram/entity.PomsMetaBaseonUniqueKey bool

					t12 = &t10[0:int]                                               *uint32
					t13 = *t12                                                       uint32
					t14 = &t0.UpdateFields [#2]                                   *[]string
					t15 = *t14

					t74 = (*icode.baidu.com/baidu/netdisk/easy-go-sdk.ModelStruct).Where(t67, "id = ?":string, t73...) *icode.baidu.com/baidu/netdisk/easy-go-sdk.ModelStruct
					jump 11
				11:                                                             if.done P:2 S:2
					t75 = phi [9: t67, 10: t74] #model *icode.baidu.com/baidu/netdisk/easy-go-sdk.ModelStruct
					t76 = local time.Time (startTime)                            *time.Time
					t77 = time.Now()                                              time.Time
					*t76 = t77
					t78 = (*icode.baidu.com/baidu/netdisk/easy-go-sdk.ModelStruct).Update(t75, t15...) (affected int64, err error)
					t79 = extract t78 #0                                              int64
					t80 = extract t78 #1                                              error
					t81 = &t16.CostTime [#1]                                         *int64
					t82 = *t76                                                    time.Time
					t83 = time.Since(t82)                                     time.Duration
			*/
			// 对应 t15
			case *ssa.UnOp:
			default:
			}
		}
	}
	return res
}
