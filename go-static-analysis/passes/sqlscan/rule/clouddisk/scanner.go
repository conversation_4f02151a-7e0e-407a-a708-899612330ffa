package clouddisk

import (
	"errors"
	"fmt"
	"go/types"
	"strings"

	"golang.org/x/tools/go/ssa"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/sqlscan/base"
)

type ClouddiskScanner struct {
	Ctx *base.RuleContext

	Results []*base.ResultInfo
}

type FnContext struct {
	FnName   string
	Fn       *ssa.Function
	Args     []string
	ArgTypes []string
}

var (
	FnNameQuery                        = "Query"
	FnNameExec                         = "Exec"
	FnNameClose                        = "Close"
	FnNameSelect                       = "Select"
	FnNameUpdate                       = "Update"
	FnNameInsert                       = "Insert"
	FnNameBuildInsertSql               = "BuildInsertSql"
	FnNameBatchInsert                  = "BatchInsert"
	FnNameBuildBatchInsertSql          = "BuildBatchInsertSql"
	FnNameBatchInsertOnDuplicate       = "BatchInsertOnDuplicate"
	FnNameBatchInsertOnDuplicateVALUES = "BatchInsertOnDuplicateVALUES"
	FnNameDelete                       = "Delete"
)

const (
	DBCallDirect   = "direct"
	DBCallIndirect = "indirect"
)

func NewClouddiskScanner(ctx *base.RuleContext) *ClouddiskScanner {
	return &ClouddiskScanner{
		Ctx: ctx,
	}
}

func (scanner *ClouddiskScanner) Scan(ctx *base.RuleContext) {

	if !strings.Contains(ctx.Pkg.Pkg.Path(), ctx.RepoName) {
		return
	}

	fnMaps, err := scanner.findTargetFn(ctx)
	if err != nil {
		return
	}

	if len(fnMaps) == 0 {
		return
	}

	for className, fns := range fnMaps {
		for _, fn := range fns {
			scanner.scanFn(ctx, className, fn)
		}

	}

}

func (scanner *ClouddiskScanner) findTargetFn(ctx *base.RuleContext) (map[string][]*ssa.Function, error) {
	typeTargetFns := make(map[string][]*ssa.Function)
	for _, mem := range ctx.Pkg.Members {

		ssaType, ok := mem.(*ssa.Type)
		if !ok {
			continue
		}

		named, ok := ssaType.Type().(*types.Named)
		if !ok {
			continue // 忽略匿名类型
		}

		// value receiver 方法集
		methodSet := ctx.Prog.MethodSets.MethodSet(named)
		if methodSet != nil {
			for i := 0; i < methodSet.Len(); i++ {
				sel := methodSet.At(i)
				if sel == nil || sel.Obj() == nil {
					continue
				}
				fn := ctx.Prog.MethodValue(sel)
				if fn != nil {
					typeTargetFns[fn.Name()] = append(typeTargetFns[fn.Name()], fn)
				}
			}
		}

		//
		// pointer receiver 方法集
		ptrType := types.NewPointer(named)
		ptrMethodSet := ctx.Prog.MethodSets.MethodSet(ptrType)
		if ptrMethodSet != nil {
			for i := 0; i < ptrMethodSet.Len(); i++ {
				sel := ptrMethodSet.At(i)
				if sel == nil || sel.Obj() == nil {
					continue
				}
				fn := ctx.Prog.MethodValue(sel)
				if fn != nil {
					// fmt.Printf("  [pointer] Method: %s\n", fn.String())
					// targetFn = append(targetFn, fn)
					typeTargetFns[fn.Name()] = append(typeTargetFns[fn.Name()], fn)

				}
			}
		}
	}

	return typeTargetFns, nil
}

func (scanner *ClouddiskScanner) isLocalFn(fn *ssa.Function) bool {
	if fn == nil {
		return false
	}

	if strings.Contains(fn.String(), scanner.Ctx.RepoName) {
		return true
	}
	return false
}
func (scanner *ClouddiskScanner) isLocalPkg(pkg *ssa.Package) bool {
	if pkg == nil {
		return false

	}
	if strings.Contains(pkg.String(), scanner.Ctx.RepoName) {
		return true
	}
	return false
}

func (scanner *ClouddiskScanner) scanFn(ctx *base.RuleContext, class string, fn *ssa.Function) {
	blocks := fn.DomPreorder()
	for _, b := range blocks {
		for _, instr := range b.Instrs {
			call, ok := instr.(*ssa.Call)
			if !ok {
				continue
			}
			callType, ok := scanner.isDBCall(call)
			if !ok {
				continue
			}

			if callType == DBCallDirect {
				baseResultInfo := scanner.handleDirectDBCall(call, nil, "Direct")
				if baseResultInfo != nil {
					scanner.Results = append(scanner.Results, baseResultInfo)
				}
			}

			if callType == DBCallIndirect {
				if err := scanner.handleIndirectDBCall(call, 0, nil); err != nil {
					fmt.Println("handleIndirectDBCall error: ", err)
					continue
				}
			}

		}
	}
}

func (scanner *ClouddiskScanner) isDBCall(instr *ssa.Call) (string, bool) {
	if scanner.isDirectDBCall(instr) {
		return DBCallDirect, true
	}

	if scanner.isIndirectDBCall(instr) {
		return DBCallIndirect, true
	}

	return "", false
}

func (scanner *ClouddiskScanner) isDirectDBCall(instr *ssa.Call) bool {
	if len(instr.Call.Args) == 0 {
		return false
	}

	arg0 := instr.Call.Args[0]
	if arg0.Type() == nil {
		return false
	}

	if strings.Contains(arg0.Type().String(), "icode.baidu.com/baidu/netdisk/clouddisk-golib/db.DBConnInfo") {
		return true
	}

	return false
}

// 间接的 dbCall，即在上层构造 db.Where 层层传递
func (scanner *ClouddiskScanner) isIndirectDBCall(call *ssa.Call) bool {
	for _, arg := range call.Call.Args {
		if strings.Contains(arg.String(), "icode.baidu.com/baidu/netdisk/clouddisk-golib/db") {
			return true
		}
	}
	return false
}

func (scanner *ClouddiskScanner) handleIndirectDBCall(call *ssa.Call, depth int, parent *Caller) error {
	if depth > 4 {
		fmt.Printf("depth too deep, depth is %v\n", depth)
		return nil
	}
	// args := BuildArgs(call) // 不一定使用上
	caller := BuildCaller(call, parent)
	// parentCall := call

	fn := call.Call.StaticCallee()
	if fn == nil {
		return errors.New("handle db call error")
	}
	for _, block := range fn.DomPreorder() {
		for _, instr := range block.Instrs {
			call, ok := instr.(*ssa.Call)
			if !ok {

				continue
			}

			callType, ok := scanner.isDBCall(call)
			if !ok {
				continue
			}

			if callType == DBCallDirect {
				resultInfo := scanner.handleDirectDBCall(call, caller, "Indirect")
				if resultInfo != nil {
					scanner.Results = append(scanner.Results, resultInfo)
				}

				continue
			}

			// 需要通过链表串起来每个caller，以及其函数参数
			if callType == DBCallIndirect {
				scanner.handleIndirectDBCall(call, depth+1, caller)
				continue
			}

		}
	}

	return nil
}

func (scanner *ClouddiskScanner) handleDirectDBCall(dbCall *ssa.Call, parent *Caller, from string) *base.ResultInfo {
	fn := dbCall.Call.StaticCallee()
	if fn == nil {
		return nil
	}

	targetFn, ok := scanner.matchTargetFn(fn)
	if !ok {
		return nil
	}

	var sql string
	switch targetFn {
	case FnNameSelect:
		sql = scanner.handleSelectFn(dbCall, parent)

	case FnNameUpdate:
		sql = scanner.handleUpdateFn(dbCall, parent)

	case FnNameInsert:
		sql = scanner.handleInsertFn(dbCall, parent)

	default:
		fmt.Printf("fn need to be handled, fn: %v\n", fn)

	}
	fileName := scanner.Ctx.Prog.Fset.Position(fn.Pos()).Filename

	tmpParent := parent
	for tmpParent != nil && tmpParent.ParentCall != nil {
		if tmpParent.ParentCall.IsMatch {
			tmpParent = tmpParent.ParentCall
		}
	}
	key := fileName + fn.String()
	if tmpParent.Call != nil {
		key += tmpParent.Call.String()
	}

	resultInfo := base.BuildResultInfo(fileName, fn.String(), key, sql, "clouddisk-golib")

	return resultInfo
}

func (scanner *ClouddiskScanner) matchTargetFn(fn *ssa.Function) (string, bool) {
	fnName := fn.Name()
	if fnName == FnNameQuery ||
		fnName == FnNameExec ||
		fnName == FnNameClose ||
		fnName == FnNameSelect ||
		fnName == FnNameUpdate ||
		fnName == FnNameInsert ||
		fnName == FnNameBuildInsertSql ||
		fnName == FnNameBatchInsert ||
		fnName == FnNameBuildBatchInsertSql ||
		fnName == FnNameBatchInsertOnDuplicate ||
		fnName == FnNameBatchInsertOnDuplicateVALUES ||
		fnName == FnNameDelete {
		return fnName, true
	}
	return "", false

}

func (scanner *ClouddiskScanner) handleQueryFn(call *ssa.Call, fn *ssa.Function, caller *Caller) error {
	args := call.Call.Args
	if len(args) == 0 {
		return errors.New("handle query fn error")
	}

	return nil
}

func (scanner *ClouddiskScanner) getTable(table ssa.Value, parent *Caller) string {
	tableName := fmt.Sprintf("%v", resolveValueString(table, parent))
	tableName = strings.TrimSpace(tableName)
	if tableName == "" {
		return "table_name"
	}
	tableName = strings.Trim(tableName, "\"")

	return tableName

}
