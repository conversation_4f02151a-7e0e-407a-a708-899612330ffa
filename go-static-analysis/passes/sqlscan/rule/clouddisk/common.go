package clouddisk

import (
	"fmt"

	"golang.org/x/tools/go/ssa"
)

func (scanner *ClouddiskScanner) getDBData(val ssa.Value, parent *Caller, depth int) map[string]interface{} {
	// scanner.getDBWhere(val, parent, depth)

	if depth > 4 {
		return nil
	}

	switch val := val.(type) {

	case *ssa.Parameter:
		whereDSL, err := scanner.handlerDBDataParameters(val, parent, depth)
		if err != nil {
			fmt.Printf("handler select where parameters error, err: %v\n", err)
			return nil
		}
		return whereDSL
	case *ssa.MakeInterface: // having 嵌套map会走这里
		return scanner.handleDBDataMakeInterface(val, parent)
	case *ssa.MakeMap:
		return scanner.handleDBDataMakeMap(val, parent, depth)
	case *ssa.ChangeType:
		whereDSL := scanner.handlerDBDataChangeType(val, parent)
		return whereDSL
	case *ssa.Call:
		_, ok := scanner.isDBCall(val)
		if ok {
			return nil
		}
		fmt.Printf("getDBWhere call string: %#v", val)
	case *ssa.Slice:
	default:
		fmt.Printf("\nscanner get where type assertion, %T: %v\n", val, val)
	}
	return nil

}
func (scanner *ClouddiskScanner) getDBDatas(val ssa.Value, parent *Caller, depth int) map[string]interface{} {
	// scanner.getDBWhere(val, parent, depth)

	if depth > 3 {
		return nil
	}
	switch val := val.(type) {
	default:
		fmt.Printf("getDBDatas, val: %v\n", val)
	}

	switch val := val.(type) {

	case *ssa.Parameter:
		whereDSL, err := scanner.handlerDBDataParameters(val, parent, depth)
		if err != nil {
			fmt.Printf("handler select where parameters error, err: %v\n", err)
			return nil
		}
		return whereDSL
	case *ssa.MakeInterface: // having 嵌套map会走这里
		return scanner.handleDBDataMakeInterface(val, parent)
	case *ssa.MakeMap:
		return scanner.handleDBDataMakeMap(val, parent, depth)
	case *ssa.Phi:
		return scanner.handleDBDataPhi(val, parent, depth)
	case *ssa.ChangeType:
		whereDSL := scanner.handlerDBDataChangeType(val, parent)
		return whereDSL
	case *ssa.Call:
		_, ok := scanner.isDBCall(val)
		if ok {
			return nil
		}
		fmt.Printf("getDBWhere call string: %#v", val)

	default:
		fmt.Printf("\nscanner get where type assertion, %T: %v\n", val, val)
	}

	return nil

}

func (scanner *ClouddiskScanner) handlerDBDataParameters(val *ssa.Parameter, parent *Caller, depth int) (map[string]interface{}, error) {
	index := scanner.indexOfParams(val, parent)
	if index == -1 {
		return nil, fmt.Errorf("handler select where parameters error, index: %v", index)
	}
	if index >= len(parent.Args) {
		return nil, fmt.Errorf("handler select where parameters error, index: %v", index)
	}
	arg := parent.Args[index]

	dbData := scanner.getDBData(arg.Value, parent.ParentCall, depth+1)
	if dbData != nil {
		parent.ParentCall.IsMatch = true
	}
	return dbData, nil
}

func (scanner *ClouddiskScanner) extractDBDataFromValue(v ssa.Value, parent *Caller) map[string]interface{} {
	res := make(map[string]interface{})

	switch val := v.(type) {
	case *ssa.Alloc:
		// 可能是通过 alloc 方式创建 map
		for _, ref := range *val.Referrers() {
			switch instr := ref.(type) {
			case *ssa.Store:

				// store to map alloc — rarely happens for maps
			case *ssa.MapUpdate:
				key := resolveValueString(instr.Key, parent)
				res[key] = "?"
			}
		}

	case *ssa.MakeMap:
		// 初始化空 map 后可能会有 MapUpdate
		if val.Referrers() != nil {
			for _, ref := range *val.Referrers() {
				if mu, ok := ref.(*ssa.MapUpdate); ok {
					key := resolveValueString(mu.Key, parent)
					res[key] = "?"

				}
			}
		}

	case *ssa.UnOp:
		// 可能是 *map 的解引用（来自 Alloc），尝试递归
		return scanner.extractDBDataFromValue(val.X, parent)

	default:
		// 打印未知类型（可选调试）
		// fmt.Printf("Unknown value type in extractMapFromValue: %T\n", val)
	}

	return res
}

func (scanner *ClouddiskScanner) handleDBDataMakeInterface(val *ssa.MakeInterface, parent *Caller) map[string]interface{} {
	if val == nil || val.X == nil {
		return nil
	}

	// 如果来源是 make map，例如：t14 = make map[string]interface{}
	return scanner.extractDBDataFromValue(val.X, parent)
}

func (scanner *ClouddiskScanner) handleDBDataMakeMap(val *ssa.MakeMap, parent *Caller, depth int) map[string]interface{} {
	res := make(map[string]interface{})
	x := val
	referrers := x.Referrers()
	if *referrers == nil {
		return res
	}

	for _, refer := range *referrers {
		switch r := refer.(type) {
		case *ssa.MapUpdate:
			key := resolveValueString(r.Key, parent)
			res[key] = "?"
		default:
			fmt.Printf("handlerSelectWhereMakeMap default handler, %T: %v\n", r, r)

		}
	}

	return res

}

func (scanner *ClouddiskScanner) handlerDBDataChangeType(val *ssa.ChangeType, parent *Caller) map[string]interface{} {
	callName := ""
	if parent != nil {
		callName = parent.CallName
	}
	res := make(map[string]interface{})

	x := val.X
	referrers := x.Referrers()
	if *referrers == nil {
		return res
	}

	for _, refer := range *referrers {
		switch r := refer.(type) {
		case *ssa.MapUpdate:
			key := resolveValueString(r.Key, parent)
			res[key] = "?"
		default:
			fmt.Printf("handlerSelectWhereChangeType default callerName: %v, default handler, %T: %v\n", callName, r, r)

		}
	}

	return res

}

func (scanner *ClouddiskScanner) handleDBDataPhi(val *ssa.Phi, parent *Caller, depth int) map[string]interface{} {
	// var ops []*ssa.Value
	ops := val.Operands(nil)
	var res map[string]interface{}
	for _, e := range ops {
		item := scanner.getDBData(*e, parent, depth)
		fmt.Printf("\nhandleDBDataPhi, edge: %T: %v, item: %v\n", *e, *e, item)
		if item != nil {
			parent.IsMatch = true
			return item
		}
	}
	return res
}
