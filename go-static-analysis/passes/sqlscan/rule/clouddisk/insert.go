package clouddisk

import (
	"fmt"

	"github.com/didi/gendry/builder"
	"golang.org/x/tools/go/ssa"
)

func (scanner *ClouddiskScanner) handleInsertFn(call *ssa.Call, parent *Caller) string {
	if call == nil || len(call.Call.Args) <= 2 {
		return ""
	}
	args := call.Call.Args

	tableName := scanner.getTable(args[1], parent)
	dbData := scanner.getDBData(args[2], parent, 0)
	if dbData != nil {
		parent.IsMatch = true
	}

	var datas []map[string]interface{}
	datas = append(datas, dbData)

	insertSQL, vals, err := builder.BuildInsert(tableName, datas)
	if err != nil {
		fmt.Printf("handleInsertFn failed, err: %v", err)
		return ""
	}

	fmt.Printf("insertSQL: %v\n, vals: %v", insertSQL, vals)
	return insertSQL
}

func (scanner *ClouddiskScanner) handleBatchInsertFn(call *ssa.Call, parent *Caller) string {
	if call == nil || len(call.Call.Args) <= 2 {
		return ""
	}
	args := call.Call.Args

	tableName := scanner.getTable(args[1], parent)
	dbData := scanner.getDBDatas(args[2], parent, 0)
	if dbData != nil {
		parent.IsMatch = true
	}

	var datas []map[string]interface{}
	datas = append(datas, dbData)
	datas = append(datas, dbData)

	insertSQL, vals, err := builder.BuildInsert(tableName, datas)
	_ = vals
	_ = err
	return insertSQL
}
