package clouddisk

import (
	"fmt"
	"strings"

	"golang.org/x/tools/go/ssa"
)

func (scanner *ClouddiskScanner) handleUpdateFn(call *ssa.Call, parent *Caller) string {
	if call == nil || len(call.Call.Args) <= 3 {
		return ""
	}
	args := call.Call.Args

	tableName := scanner.getTable(args[1], parent)
	whereStruct := scanner.getDBWhere(args[2], parent, 0)
	dbData := scanner.getDBData(args[3], parent, 0)
	if dbData == nil {
		parent.IsMatch = true
	}

	sql := fmt.Sprintf("UPDATE %s", tableName)

	// 构造 SET 语句
	if len(dbData) > 0 {
		setClauses := make([]string, 0, len(dbData))
		for field := range dbData {
			setClauses = append(setClauses, fmt.Sprintf("%s = ?", field))
		}
		sql += " SET " + strings.Join(setClauses, ", ")
	}

	if whereStruct == nil {
		return sql
	}

	// 构造 WHERE 子句
	if len(whereStruct.Where) > 0 {
		whereClauses := make([]string, 0, len(whereStruct.Where))
		for k := range whereStruct.Where {
			whereClauses = append(whereClauses, fmt.Sprintf("%s = ?", k))
		}
		sql += " WHERE " + strings.Join(whereClauses, " AND ")
	}

	// 附加可选条件
	if whereStruct.OrderBy != "" {
		sql += " ORDER BY " + whereStruct.OrderBy
	}
	if whereStruct.GroupBy != "" {
		sql += " GROUP BY " + whereStruct.GroupBy
	}
	if len(whereStruct.Limit) > 0 {
		if len(whereStruct.Limit) == 1 {
			sql += fmt.Sprintf(" LIMIT %d", whereStruct.Limit[0])
		} else {
			sql += fmt.Sprintf(" LIMIT %d OFFSET %d", whereStruct.Limit[1], whereStruct.Limit[0])
		}
	}
	if whereStruct.LockMode != "" {
		sql += " FOR " + strings.ToUpper(whereStruct.LockMode)
	}

	// 递归 Having
	if whereStruct.Having != nil {
		havingClause := buildHavingClause(whereStruct.Having)
		if havingClause != "" {
			sql += " HAVING " + havingClause
		}
	}

	// 你可以记录或打印 SQL
	fmt.Println("Recovered SQL:", sql)

	return sql
}

func buildHavingClause(h *WhereStruct) string {
	if len(h.Where) == 0 {
		return ""
	}
	having := make([]string, 0, len(h.Where))
	for k := range h.Where {
		having = append(having, fmt.Sprintf("%s = ?", k))
	}
	// 可以递归构建嵌套 Having
	if h.Having != nil {
		having = append(having, "("+buildHavingClause(h.Having)+")")
	}
	return strings.Join(having, " AND ")
}
