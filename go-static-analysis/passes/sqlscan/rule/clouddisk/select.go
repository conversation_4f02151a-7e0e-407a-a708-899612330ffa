package clouddisk

import (
	"fmt"
	"go/constant"
	"go/types"
	"strings"

	"golang.org/x/tools/go/ssa"
)

var allowedLockMode = map[string]string{
	"share":     " LOCK IN SHARE MODE",
	"exclusive": " FOR UPDATE",
}

// where 的其他
type WhereStruct struct {
	Where    map[string]interface{}
	OrderBy  string       // order by
	GroupBy  string       // group by
	LockMode string       // _lockMode: share/exclusive
	Limit    []uint64     // limit
	Having   *WhereStruct // _having
}

func NewWhereStruct() *WhereStruct {
	return &WhereStruct{
		Where:  make(map[string]interface{}),
		Limit:  make([]uint64, 0),
		Having: &WhereStruct{},
	}
}

func (scanner *ClouddiskScanner) handleSelectFn(call *ssa.Call, parent *Caller) string {
	// 	func (conn *DBConnInfo) Select(table string, where Where, fields []string, autoEscapeArr ...bool) (rows *sql.Rows, err error) {
	args := call.Call.Args
	if len(args) < 4 {
		return ""

	}

	tableName := scanner.getTable(args[1], parent)
	whereDSL := scanner.getDBWhere(args[2], parent, 0)

	fields := scanner.getSelectFields(args[3], parent)
	sql := scanner.buildSelect(tableName, whereDSL, fields)
	if sql == "" {
		return ""
	}

	return sql
}

func (scanner *ClouddiskScanner) getDBWhere(val ssa.Value, parent *Caller, depth int) *WhereStruct {
	if depth > 3 {
		return nil
	}

	switch val := val.(type) {

	case *ssa.Parameter:
		whereDSL, err := scanner.handlerWhereParameters(val, parent, depth)
		if err != nil {
			return nil
		}
		return whereDSL
	case *ssa.MakeInterface: // having 嵌套map会走这里
		return scanner.handleWhereMakeInterface(val, parent)
	case *ssa.MakeMap:
		return scanner.handleWhereMakeMap(val, parent, depth)

	case *ssa.ChangeType:
		whereDSL := scanner.handlerWhereChangeType(val, parent)
		return whereDSL
	case *ssa.Call:
		_, ok := scanner.isDBCall(val)
		if ok {
			return nil
		}

	default:
		fmt.Printf("\nscanner get where type assertion, %T: %v\n", val, val)
	}
	return nil
}

func (scanner *ClouddiskScanner) handlerWhereParameters(val *ssa.Parameter, parent *Caller, depth int) (*WhereStruct, error) {
	index := scanner.indexOfParams(val, parent)
	if index == -1 {
		return nil, fmt.Errorf("handler select where parameters error, index: %v", index)
	}
	if index >= len(parent.Args) {
		return nil, fmt.Errorf("handler select where parameters error, index: %v", index)
	}
	arg := parent.Args[index]

	wehreDSL := scanner.getDBWhere(arg.Value, parent.ParentCall, depth+1)
	return wehreDSL, nil
}

func (scanner *ClouddiskScanner) indexOfParams(val *ssa.Parameter, parent *Caller) int {
	if parent == nil {
		return -1
	}

	fn := parent.Call.Call.StaticCallee()
	if fn == nil {
		return -1
	}
	for i, v := range fn.Params {
		if v.Name() == val.Name() && v.Type() == val.Type() {
			return i
		}
	}

	return -1

}

func (scanner *ClouddiskScanner) handleWhereMakeInterface(val *ssa.MakeInterface, parent *Caller) *WhereStruct {
	if val == nil || val.X == nil {
		return nil
	}

	// 如果来源是 make map，例如：t14 = make map[string]interface{}
	return scanner.extractWhereStructFromValue(val.X, parent)
}

func (scanner *ClouddiskScanner) extractWhereStructFromValue(v ssa.Value, parent *Caller) *WhereStruct {
	whereDSL := NewWhereStruct()
	switch val := v.(type) {
	case *ssa.Alloc:
		// 可能是通过 alloc 方式创建 map
		for _, ref := range *val.Referrers() {
			switch instr := ref.(type) {
			case *ssa.Store:
				// store to map alloc — rarely happens for maps
			case *ssa.MapUpdate:
				key := resolveValueString(instr.Key, parent)
				if key == "_orderby" {
					whereDSL.OrderBy = scanner.resolveWhereOrderBy(instr.Value, parent)
				} else if key == "_groupby" {
					whereDSL.GroupBy = scanner.resolveWhereGroupBy(instr.Value, parent)
				} else if key == "_having" {
					whereDSL.Having = scanner.resolveWhereHaving(instr.Value, parent)
				} else if key == "_limit" {
					whereDSL.Limit = scanner.resolveWhereLimit(instr.Value, parent)
				} else {
					whereDSL.Where[key] = "?"
				}

			}
		}

	case *ssa.MakeMap:
		// 初始化空 map 后可能会有 MapUpdate
		if val.Referrers() != nil {
			for _, ref := range *val.Referrers() {
				if mu, ok := ref.(*ssa.MapUpdate); ok {
					key := resolveValueString(mu.Key, parent)
					if key == "_orderby" {
						whereDSL.OrderBy = scanner.resolveWhereOrderBy(mu.Value, parent)
					} else if key == "_groupby" {
						whereDSL.GroupBy = scanner.resolveWhereGroupBy(mu.Value, parent)
					} else if key == "_having" {
						whereDSL.Having = scanner.resolveWhereHaving(mu.Value, parent)
					} else if key == "_limit" {
						whereDSL.Limit = scanner.resolveWhereLimit(mu.Value, parent)
					} else {
						whereDSL.Where[key] = "?"
					}
				}
			}
		}

	case *ssa.UnOp:
		// 可能是 *map 的解引用（来自 Alloc），尝试递归
		return scanner.extractWhereStructFromValue(val.X, parent)

	default:
		// 打印未知类型（可选调试）
		// fmt.Printf("Unknown value type in extractMapFromValue: %T\n", val)
	}

	return whereDSL
}

func (scanner *ClouddiskScanner) handleWhereMakeMap(val *ssa.MakeMap, parent *Caller, depth int) *WhereStruct {
	whereDSL := NewWhereStruct()

	x := val
	referrers := x.Referrers()
	if *referrers == nil {
		return whereDSL
	}

	for _, refer := range *referrers {
		switch r := refer.(type) {
		case *ssa.MapUpdate:
			key := resolveValueString(r.Key, parent)
			if key == "_orderby" {
				whereDSL.OrderBy = scanner.resolveWhereOrderBy(r.Value, parent)
			} else if key == "_groupby" {
				whereDSL.GroupBy = scanner.resolveWhereGroupBy(r.Value, parent)
			} else if key == "_having" {
				whereDSL.Having = scanner.resolveWhereHaving(r.Value, parent)
			} else if key == "_limit" {
				whereDSL.Limit = scanner.resolveWhereLimit(r.Value, parent)
			} else {
				whereDSL.Where[key] = "?"
			}

		default:
			fmt.Printf("handlerSelectWhereMakeMap default handler, %T: %v\n", r, r)

		}
	}

	return whereDSL

}

func (scanner *ClouddiskScanner) handlerWhereChangeType(val *ssa.ChangeType, parent *Caller) *WhereStruct {
	callName := ""
	if parent != nil {
		callName = parent.CallName
	}
	whereDSL := NewWhereStruct()

	x := val.X
	referrers := x.Referrers()
	if *referrers == nil {
		return whereDSL
	}

	for _, refer := range *referrers {
		switch r := refer.(type) {
		case *ssa.MapUpdate:
			key := resolveValueString(r.Key, parent)
			if key == "_orderby" {
				whereDSL.OrderBy = scanner.resolveWhereOrderBy(r.Value, parent)
			} else if key == "_groupby" {
				whereDSL.GroupBy = scanner.resolveWhereGroupBy(r.Value, parent)
			} else if key == "_having" {
				whereDSL.Having = scanner.resolveWhereHaving(r.Value, parent)
			} else if key == "_limit" {
				whereDSL.Limit = scanner.resolveWhereLimit(r.Value, parent)
			} else {
				whereDSL.Where[key] = "?"
			}

		default:
			fmt.Printf("handlerSelectWhereChangeType default callerName: %v, default handler, %T: %v\n", callName, r, r)

		}
	}

	return whereDSL

}

func (scanner *ClouddiskScanner) resolveWhereLimit(val ssa.Value, parent *Caller) []uint64 {
	switch val := val.(type) {
	case *ssa.MakeInterface:
		res, ok := ExtractUintSliceFromInterface(val)
		if ok {
			return res
		}
	default:
	}
	return []uint64{}
}

func (scanner *ClouddiskScanner) resolveWhereHaving(val ssa.Value, parent *Caller) *WhereStruct {
	having := scanner.getDBWhere(val, parent, 0)
	return having
}

func (scanner *ClouddiskScanner) resolveWhereGroupBy(val ssa.Value, parent *Caller) string {
	switch val := val.(type) {
	case *ssa.MakeInterface:
		res, ok := ExtractStringFromInterface(val)
		if ok {
			return res
		}
	default:
		fmt.Printf("resolveWhereOrderBy, %T: %v\n", val, val)
	}

	return ""
}

func (scanner *ClouddiskScanner) resolveWhereOrderBy(val ssa.Value, parent *Caller) string {
	switch val := val.(type) {
	case *ssa.MakeInterface:
		res, ok := ExtractStringFromInterface(val)
		if ok {
			return res
		}
	default:
		fmt.Printf("resolveWhereOrderBy, %T: %v\n", val, val)
	}
	return ""
}

func (scanner *ClouddiskScanner) getSelectFields(val ssa.Value, parent *Caller) []string {

	slice, ok := recoverStringSlice(val, parent)
	if ok {
		return slice
	}

	return make([]string, 0)
}

func (scanner *ClouddiskScanner) resolveSlice(val *ssa.Slice) {

	// 模拟还原 slice
	switch x := val.X.(type) { // t17
	case *ssa.Alloc: // new [1]string t15
		referres := x.Referrers()
		var slice []string
		for _, refer := range *referres {
			switch refer := refer.(type) {
			case *ssa.IndexAddr:
				index, ok := scanner.resolveInt(refer.Index)
				if ok {
					if index >= len(slice) {
						slice = copySlice(slice, index+1)
					}
				}

			}

		}
	default:
		fmt.Printf("\nClouddiskScanner resolveSlice, %T: %v\n", x, x)
	}
}

func (scanner *ClouddiskScanner) buildSelect(tableName string, dsl *WhereStruct, fields []string) string {
	if tableName == "" {
		return ""
	}
	if len(fields) == 0 {
		fields = []string{"*"}
	}

	if dsl == nil {
		return ""
	}

	var sb strings.Builder
	sb.WriteString("SELECT ")
	sb.WriteString(strings.Join(fields, ", "))
	sb.WriteString(" FROM ")
	sb.WriteString(tableName)

	// WHERE
	whereSQL := buildCondition(dsl.Where)
	if whereSQL != "" {
		sb.WriteString(" WHERE ")
		sb.WriteString(whereSQL)
	}

	// GROUP BY
	if dsl.GroupBy != "" {
		sb.WriteString(" GROUP BY ")
		sb.WriteString(dsl.GroupBy)
	}

	// HAVING
	if dsl.Having != nil && len(dsl.Having.Where) > 0 {
		havingSQL := buildCondition(dsl.Having.Where)
		if havingSQL != "" {
			sb.WriteString(" HAVING ")
			sb.WriteString(havingSQL)
		}
	}

	// ORDER BY
	if dsl.OrderBy != "" {
		sb.WriteString(" ORDER BY ")
		sb.WriteString(dsl.OrderBy)
	}

	// LIMIT
	if len(dsl.Limit) == 1 {
		sb.WriteString(fmt.Sprintf(" LIMIT %d", dsl.Limit[0]))
	} else if len(dsl.Limit) == 2 {
		sb.WriteString(fmt.Sprintf(" LIMIT %d, %d", dsl.Limit[0], dsl.Limit[1]))
	}

	// LOCK MODE
	if dsl.LockMode != "" {
		switch strings.ToLower(dsl.LockMode) {
		case "share":
			sb.WriteString(" LOCK IN SHARE MODE")
		case "exclusive":
			sb.WriteString(" FOR UPDATE")
		}
	}

	return sb.String()
}

func buildCondition(conds map[string]interface{}) string {
	if len(conds) == 0 {
		return ""
	}
	clauses := []string{}
	for key, val := range conds {
		switch v := val.(type) {
		case string:
			clauses = append(clauses, fmt.Sprintf("%s = '%s'", key, v))
		case int, int64, uint64, float64:
			clauses = append(clauses, fmt.Sprintf("%s = %v", key, v))
		case []interface{}:
			items := []string{}
			for _, item := range v {
				switch item := item.(type) {
				case string:
					items = append(items, fmt.Sprintf("'%s'", item))
				default:
					items = append(items, fmt.Sprintf("%v", item))
				}
			}
			clauses = append(clauses, fmt.Sprintf("%s IN (%s)", key, strings.Join(items, ", ")))
		default:
			clauses = append(clauses, fmt.Sprintf("%s = '%v'", key, v))
		}
	}
	return strings.Join(clauses, " AND ")
}

func (scanner *ClouddiskScanner) resolveInt(val ssa.Value) (int, bool) {
	switch val := val.(type) {
	case *ssa.Const:
		res, ok := (constant.Int64Val(val.Value))
		return int(res), ok
	default:
		return -1, false
	}
}

func copySlice[T any](s []T, newCap int) []T {
	if newCap < len(s) {
		return s
	}
	newSlice := make([]T, len(s), newCap)
	copy(newSlice, s)
	return newSlice
}

func recoverStringSlice(val ssa.Value, parent *Caller) (elements []string, ok bool) {
	switch v := val.(type) {
	case *ssa.Slice:
		return recoverFromSliceAlloc(v, parent)

	case *ssa.Call:
		return recoverFromCallReturningSlice(v, parent)

	default:
		return nil, false
	}
}

func recoverFromSliceAlloc(sliceInstr *ssa.Slice, parent *Caller) ([]string, bool) {
	alloc, ok := sliceInstr.X.(*ssa.Alloc)
	if !ok || alloc.Referrers() == nil {
		return nil, false
	}

	indexedValues := make(map[int]string)

	for _, ref := range *alloc.Referrers() {
		indexAddr, ok := ref.(*ssa.IndexAddr)
		if !ok || indexAddr.Referrers() == nil {
			continue
		}

		index := resolveValueInt(indexAddr.Index)

		for _, storeRef := range *indexAddr.Referrers() {
			store, ok := storeRef.(*ssa.Store)
			if !ok {
				continue
			}

			strVal := resolveSSAConstString(store.Val)
			indexedValues[index] = strVal
		}
	}

	if len(indexedValues) == 0 {
		return nil, false
	}

	// 保证顺序
	maxIndex := 0
	for idx := range indexedValues {
		if idx > maxIndex {
			maxIndex = idx
		}
	}

	result := make([]string, maxIndex+1)
	for i := 0; i <= maxIndex; i++ {
		result[i] = indexedValues[i] // 若无值，默认为 ""
	}

	return result, true
}

func recoverFromCallReturningSlice(call *ssa.Call, parent *Caller) ([]string, bool) {
	fn := call.Call.StaticCallee()
	if fn == nil || len(fn.Blocks) == 0 {
		return nil, false
	}

	for _, block := range fn.Blocks {
		for _, instr := range block.Instrs {
			ret, ok := instr.(*ssa.Return)
			if !ok || len(ret.Results) == 0 {
				continue
			}

			// 返回值是否是 slice 指令
			switch res := ret.Results[0].(type) {
			case *ssa.Slice:
				return recoverFromSliceAlloc(res, parent)
			default:
				continue
			}
		}
	}
	return nil, false
}

func RecoverMapWithUintSliceUpdate(update *ssa.Store) (mapKey string, sliceValues []uint64, ok bool) {
	// 检查是否是对 map 的更新
	instruction, ok := update.Addr.(ssa.Instruction)
	if !ok {
		return "", nil, false

	}

	indexAddr, ok := instruction.(*ssa.MapUpdate)
	if !ok {
		return "", nil, false
	}

	// 检查 key 是常量字符串
	keyConst, ok := indexAddr.Key.(*ssa.Const)
	if !ok || keyConst.Value.Kind() != constant.String {
		return "", nil, false
	}
	mapKey = constant.StringVal(keyConst.Value)

	// 检查值是 interface{} 封装的 []uint
	makeIface, ok := update.Val.(*ssa.MakeInterface)
	if !ok {
		return "", nil, false
	}

	// 解包到 Slice 指令
	sliceInstr, ok := makeIface.X.(*ssa.Slice)
	if !ok {
		return "", nil, false
	}

	// Slice 的底层必须是 *[N]uint 分配
	alloc, ok := sliceInstr.X.(*ssa.Alloc)
	if !ok {
		return "", nil, false
	}

	// 检查是否是 *[N]uint 类型
	arrayPtr, ok := alloc.Type().(*types.Pointer)
	if !ok {
		return "", nil, false
	}
	arrayType, ok := arrayPtr.Elem().(*types.Array)
	if !ok {
		return "", nil, false
	}
	elemType, ok := arrayType.Elem().(*types.Basic)
	if !ok || elemType.Kind() != types.Uint {
		return "", nil, false
	}

	// 收集 Store 中的值
	indexToValue := make(map[int]uint64)
	for _, ref := range *alloc.Referrers() {
		idxAddr, ok := ref.(*ssa.IndexAddr)
		if !ok {
			continue
		}

		// 获取索引常量
		idxConst, ok := idxAddr.Index.(*ssa.Const)
		if !ok {
			continue
		}
		index := int(idxConst.Int64())

		// 找 Store 指令并提取值
		for _, use := range *idxAddr.Referrers() {
			store, ok := use.(*ssa.Store)
			if !ok {
				continue
			}
			valConst, ok := store.Val.(*ssa.Const)
			if !ok || valConst.Value.Kind() != constant.Int {
				continue
			}
			uval, _ := constant.Uint64Val(valConst.Value)
			indexToValue[index] = uval
		}
	}

	if len(indexToValue) == 0 {
		return mapKey, nil, false
	}

	// 保证顺序
	maxIdx := 0
	for i := range indexToValue {
		if i > maxIdx {
			maxIdx = i
		}
	}
	sliceValues = make([]uint64, maxIdx+1)
	for i := 0; i <= maxIdx; i++ {
		sliceValues[i] = indexToValue[i]
	}

	return mapKey, sliceValues, true
}

func ExtractUintSliceFromInterface(v ssa.Value) ([]uint64, bool) {
	// make interface{} <- []uint
	makeIface, ok := v.(*ssa.MakeInterface)
	if !ok {
		return nil, false
	}

	// slice 指令：slice t10[:]
	sliceInstr, ok := makeIface.X.(*ssa.Slice)
	if !ok {
		return nil, false
	}

	//alloc 指令（t10 = new [2]uint）
	alloc, ok := sliceInstr.X.(*ssa.Alloc)
	if !ok {
		return nil, false
	}

	// 检查 alloc 类型必须是 *[N]uint
	ptrType, ok := alloc.Type().(*types.Pointer)
	if !ok {
		return nil, false
	}
	arrayType, ok := ptrType.Elem().(*types.Array)
	if !ok {
		return nil, false
	}
	elemType, ok := arrayType.Elem().(*types.Basic)
	if !ok || elemType.Kind() != types.Uint {
		return nil, false
	}

	// 分析 alloc 的 referrers，找所有 IndexAddr 和对应的 Store
	indexToValue := map[int]uint64{}
	for _, ref := range *alloc.Referrers() {
		idxAddr, ok := ref.(*ssa.IndexAddr)
		if !ok {
			continue
		}

		// 取 index 索引值
		constIdx, ok := idxAddr.Index.(*ssa.Const)
		if !ok {
			continue
		}
		index := int(constIdx.Int64())

		// 找 store 指令
		for _, use := range *idxAddr.Referrers() {
			store, ok := use.(*ssa.Store)
			if !ok {
				continue
			}
			valConst, ok := store.Val.(*ssa.Const)
			if !ok || valConst.Value.Kind() != constant.Int {
				continue
			}
			uval, _ := constant.Uint64Val(valConst.Value)
			indexToValue[index] = uval
		}
	}

	if len(indexToValue) == 0 {
		return nil, false
	}

	// 还原为有序 slice
	maxIdx := 0
	for idx := range indexToValue {
		if idx > maxIdx {
			maxIdx = idx
		}
	}
	values := make([]uint64, maxIdx+1)
	for i := 0; i <= maxIdx; i++ {
		values[i] = indexToValue[i]
	}
	return values, true
}
func ExtractStringFromInterface(v ssa.Value) (string, bool) {
	makeIface, ok := v.(*ssa.MakeInterface)
	if !ok {
		return "", false
	}
	cstr, ok := makeIface.X.(*ssa.Const)
	if !ok || cstr.Value.Kind() != constant.String {
		return "", false
	}
	return constant.StringVal(cstr.Value), true
}
