package clouddisk

import (
	"fmt"
	"go/constant"
	"go/token"
	"strings"

	"golang.org/x/tools/go/ssa"
)

// resolveValue 尝试还原常量或返回表达式
func resolveValueString(v ssa.Value, parent *Caller) string {
	// fmt.Printf("resolveValue, %T: %v\n", v, v)

	var res string
	switch val := v.(type) {
	case *ssa.Const:
		res = val.Value.ExactString()
	case *ssa.Parameter:
		res = val.Name() ///  fmt.Sprintf("参数: %s", val.Name())
	case *ssa.Field:
		res = val.Name() // fmt.Sprintf("字段: %s", val.Name())
	case *ssa.BinOp:
		res = resolveBinOP(val, parent)
	case *ssa.UnOp:
		res = resolveValueString(val.X, parent) // fmt.Sprintf("解引用: %v", resolveValue(val.X))
	case *ssa.Call:
		res = resolveCallValue(val, parent) // fmt.Sprintf("调用: %v", val.Call)
	case *ssa.Extract:
		res = resolveExtractValue(val, parent)
	default:
		res = fmt.Sprintf("resolveValue %T: %v", val, val)
	}

	return res
}

func resolveExtractValue(extract *ssa.Extract, parent *Caller) string {
	tuple := extract.Tuple
	fmt.Printf("tuple type: %T, val: %v", tuple, tuple)
	return ""

}

func resolveCallValue(call *ssa.Call, parent *Caller) string {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return ""
	}
	switch fn.Name() {
	case "GetTableBySub":
		if len(call.Call.Args) >= 2 {
			return resolveValueString(call.Call.Args[1], parent)
		}
		return ""
	default:
		res := resolveDefaultCallValue(call, parent)
		fmt.Println("resolveDefaultCallValue: ", res)
		return res

	}
}

func resolveDefaultCallValue(call *ssa.Call, parent *Caller) string {
	fn := call.Call.StaticCallee()
	if fn == nil {
		return ""
	}
	for _, block := range fn.Blocks {
		for _, instr := range block.Instrs {
			switch instr := instr.(type) {
			case *ssa.BinOp:
				return resolveBinOP(instr, parent)
			case *ssa.Return:
				return resolveValueString(instr.Results[0], parent)
			}
		}
	}

	return ""
}

func resolveBinOP(instr *ssa.BinOp, parent *Caller) string {
	x := resolveValueString(instr.X, parent)
	y := resolveValueString(instr.Y, parent)
	if instr.Op == token.ADD {
		return x + y
	}

	return ""

}

func resolveWhereLimit(val *ssa.Value) []int {
	// TODO
	return nil
}

// ###########################

func resolveValueSlice(v ssa.Value, parent *Caller) []string {
	switch val := v.(type) {
	case *ssa.Slice:
		return resolveSliceFromArray(val.X, parent)

	case *ssa.Call:
		if fn := val.Call.StaticCallee(); fn != nil && fn.Name() == "append" && len(val.Call.Args) >= 2 {
			base := resolveValueSlice(val.Call.Args[0], parent)
			add := resolveValueSlice(val.Call.Args[1], parent)
			return append(base, add...)
		}
	case *ssa.Phi:
		var result []string
		for _, edge := range val.Edges {
			result = append(result, resolveValueSlice(edge, parent)...)
		}
		return result

	case *ssa.Const:
		if val.IsNil() {
			return nil
		}
		return []string{val.Value.ExactString()}

	default:
		s := resolveValueString(val, parent)
		if s != "" {
			return []string{s}
		}
	}
	return nil
}

func resolveSliceFromArray(array ssa.Value, parent *Caller) []string {
	alloc, ok := array.(*ssa.Alloc)
	if !ok {
		return nil
	}

	// 从 alloc 找到所在函数
	fn := getEnclosingFunction(alloc)
	if fn == nil {
		return nil
	}

	elemMap := map[int]string{}

	for _, block := range fn.Blocks {
		for _, instr := range block.Instrs {
			store, ok := instr.(*ssa.Store)
			if !ok {
				continue
			}
			addr, ok := store.Addr.(*ssa.IndexAddr)
			if !ok {
				continue
			}
			if addr.X != alloc {
				continue
			}
			indexConst, ok := addr.Index.(*ssa.Const)
			if !ok {
				continue
			}
			indexInt := resolveValueInt(indexConst)
			if !ok {
				continue
			}
			strVal := resolveValueString(store.Val, parent)
			elemMap[int(indexInt)] = strVal
		}
	}

	// 构造有序数组
	maxIdx := -1
	for i := range elemMap {
		if i > maxIdx {
			maxIdx = i
		}
	}
	res := make([]string, maxIdx+1)
	for i := 0; i <= maxIdx; i++ {
		res[i] = elemMap[i]
	}
	return res
}

// 从任意 ssa.Value 获取所在函数
func getEnclosingFunction(v ssa.Value) *ssa.Function {
	if v == nil {
		return nil
	}
	if instr, ok := v.(ssa.Instruction); ok {
		return instr.Parent()
	}
	if valWithPos, ok := v.(interface{ Parent() *ssa.Function }); ok {
		return valWithPos.Parent()
	}
	return nil
}

// 安全解析常量字符串
func resolveSSAConstString(v ssa.Value) string {
	switch c := v.(type) {
	case *ssa.Const:
		return strings.Trim(c.Value.ExactString(), `"`)
	default:
		return fmt.Sprintf("%v", v)
	}
}

func resolveValueInt(v ssa.Value) int {
	c, ok := v.(*ssa.Const)
	if !ok {
		return 0 // 或者 panic / 返回 error
	}

	// 只处理整数
	if c.Value.Kind() == constant.Int {
		val, _ := constant.Int64Val(c.Value)
		return int(val)
	}

	// 其他类型（如 float, string），可根据需要加处理
	return 0
}
