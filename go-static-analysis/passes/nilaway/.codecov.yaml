coverage:
  range: 80..100
  round: down
  precision: 2

  status:
    project:                   # measuring the overall project coverage
      default:                 # context, you can create multiple ones with custom titles
        enabled: yes           # must be yes|true to enable this status
        target: auto           # specify the target coverage for each commit status
                               #   option: "auto" (compare against parent commit or pull request base)
                               #   option: "X%" a static target percentage to hit
        threshold: 1%          # allow the coverage drop by 1% before marking as failure (to allow some flakiness)
        if_not_found: success  # if parent is not found report status as success, error, or failure
        if_ci_failed: error    # if ci fails report status as success, error, or failure
    patch:
      default:
        enabled: no
