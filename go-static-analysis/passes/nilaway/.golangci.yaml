version: "2"
linters:
  enable:
    - goheader
    - nolintlint
    - paralleltest
    - revive
  settings:
    staticcheck:
      checks:
        - all
        # disable the rule QF1001: "could apply <PERSON>'s law" since it does not lead to clarity
        # in most cases.
        - '-QF1001'
    goheader:
      values:
        regexp:
          any-year: \d{4}
      template: |-
        Copyright (c) {{ ANY-YEAR }} Uber Technologies, Inc.

        Licensed under the Apache License, Version 2.0 (the "License");
        you may not use this file except in compliance with the License.
        You may obtain a copy of the License at

            http://www.apache.org/licenses/LICENSE-2.0

        Unless required by applicable law or agreed to in writing, software
        distributed under the License is distributed on an "AS IS" BASIS,
        WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
        See the License for the specific language governing permissions and
        limitations under the License.

issues:
  max-issues-per-linter: 0
  max-same-issues: 0

formatters:
  enable:
    - gci
    - gofmt
    - goimports
