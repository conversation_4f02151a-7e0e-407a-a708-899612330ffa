//  Copyright (c) 2024 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package upstream

// This tests the export of contracts from the upstream package.

//contract(nonnil -> nonnil)
func ExportedManual(p *int) *int { //want ExportedManual:"&\\[{\\[nonnil\\] \\[nonnil\\]}\\]"
	if p != nil {
		a := 1
		return &a
	}
	return nil
}

func ExportedInferred(p *int) *int { //want ExportedInferred:"&\\[{\\[nonnil\\] \\[nonnil\\]}\\]"
	if p != nil {
		a := 1
		return &a
	}
	return nil
}

//contract(nonnil -> nonnil)
func unexportedManual(p *int) *int { // Notice here we do not want to export the contracts for it.
	if p != nil {
		a := 1
		return &a
	}
	return nil
}

func unexportedInferred(p *int) *int { // Notice here we do not want to export the contracts for it.
	if p != nil {
		a := 1
		return &a
	}
	return nil
}
