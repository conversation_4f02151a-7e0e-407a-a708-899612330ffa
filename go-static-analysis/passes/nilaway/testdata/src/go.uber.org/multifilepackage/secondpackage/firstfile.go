//  Copyright (c) 2023 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// <nilaway no inference>
package secondpackage

import "go.uber.org/multifilepackage/firstpackage"

type C struct {
	B bool
}

// nilable(Ptr)
type CBox struct {
	Ptr *C
}

// nilable(ptr)
func (c CBox) Unbox() (ptr *C) {
	return c.Ptr
}

// nilable(ptr)
func (c CBox) Box(ptr *C) {
	c.Ptr = ptr
}

func (c C) Branch(a *firstpackage.A, b *firstpackage.B) *firstpackage.B {
	if c.B {
		return a.GetMyB()
	} else {
		return b
	}
}
