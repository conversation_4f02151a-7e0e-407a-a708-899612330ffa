//  Copyright (c) 2023 Uber Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Upstream package
package upstream

type Callback interface {
	GetErrorObj() error
}

// note: Unexported
func problem(cb Callback) (*int, error) {
	err := cb.GetErrorObj()
	return nil, err
}

func EntryPoint(cb Callback) *int {
	v, err := problem(cb)
	if err != nil {
		i := 0
		return &i
	}
	return v
}
