# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IntelliJ files
.idea/

# Coverage files
cover.out
cover.html

# Local binary folder
bin/

# Local build folder for storing build artifacts
build/

# Mac OS files
**/.DS_Store
