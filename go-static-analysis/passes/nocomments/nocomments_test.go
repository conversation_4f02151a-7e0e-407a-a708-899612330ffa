package nocomments_test

import (
	"log"
	"testing"

	"golang.org/x/tools/go/analysis/analysistest"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/nocomments"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/nocomments/config"
)

func TestRun(t *testing.T) {
	config.Conf.LenParams = 4
	log.SetFlags(log.Lshortfile)
	testdata := analysistest.TestData()
	nocomments.TestMode = true
	analysistest.Run(t, testdata, nocomments.Analyzer, "nocomments/...")
}
