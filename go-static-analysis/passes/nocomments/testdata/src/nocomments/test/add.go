package test

import "fmt"

/*
* @description:the max of two number
* @params:
*    - c: one number of compare
*    - d: another number of compare
* @returns:
*    - int:dfsfdf
 */
func max(c, d int) int { // wsant `注释缺少returns或返回类型或返回数量不匹配`
	if c > d {
		return c
	}
	return d
}

/*
* @description:the max of two number
* @params:
*    - e:   - e: 1---（:dasdas：）IM数据
*    - f: another number of compare
 */
func min(e, f int) int { // want `注释缺少returns或返回类型不匹配, 格式请参考下方无注释检测文档.`
	fmt.Println(max(e, f))
	return max(e, f)
}

func exp_avg(a, b int) int { // want `注释缺少description, 缺少params, 缺少returns, 格式请参考下方无注释检测文档.`
	return (a + b) / 2
}

//@description:the max of two number
//@parsams:
//    - c: one number of compare
//    - d: another number of compare/
//@returns:
//    - int:dfsfdf
func getAddend4(sum int) (int, int) { //want `注释缺少params或参数名不匹配, 缺少returns或返回类型不匹配, 格式请参考下方无注释检测文档.`
	a := sum - 1
	b := 1
	return a, b
}

//@params:
//    - sum: one number of compare
//    - d: another number of compare/
//@returns:
//    - int:dfsfdf
func getAddend3(sum int) (int, int) { //want `注释缺少description, 缺少returns或返回类型不匹配, 格式请参考下方无注释检测文档.`
	a := sum - 1
	b := 1
	return a, b
}

//@params:
//    - c: one number of compare
//    - d: another number of compare/
//@returns:
//    - int:dfsfdf
//    - int:fasdsa
func getAddend2(sum int) (int, int) { //want `注释缺少description, 缺少params或参数名不匹配, 格式请参考下方无注释检测文档.`
	a := sum - 1
	b := 1
	return a, b
}

//@description:ins
//@params:
//    - c: one number of compare
//    - d: another number of compare/
//@returns:
//    - int:dff
//    - int:fasdsa
func getAddend1(sum int) (int, int) { //want `注释缺少params或参数名不匹配,  description和returns长度小于4, 格式请参考下方无注释检测文档.`
	a := sum - 1
	b := 1
	return a, b
}

func getAddend5(sum int) (int, int) { //want `注释缺少description, 缺少params, 缺少returns, 格式请参考下方无注释检测文档.`
	a := sum - 1
	b := 1
	return a, b
}
