package test

// @描述/description:(字符长度大于4)
// @参数/params:
//	  - 参数1：参数1说明(字符长度大于4)
//    - 参数2：参数2说明(字符长度大于4)
//		...
// @返回/returns:
//    - 返回1类型：返回1说明(字符长度大于4)
//	  - 返回2类型：返回2说明(字符长度大于4)
//		...

// @描述：数据相加
// @参数：
//    - a：加数
//    - b：被加数
// @返回：
//    - int：被加数
func Add(a, b int) int { //wsant `missing params comments`
	return a + b
}

// @描aaa述：数据相加
// @参数：
//    - a：加数
//    - b：被加数
// @返回：
//    - int：被加数
func Add1(a, b int) int { //want `注释缺少description, 格式请参考下方无注释检测文档.`
	return a + b
}

// @参aaa数：
//    - a：加数
//    - b：被加数
// @返回：
func Add2(a, b int) int { //want `注释缺少description, 缺少params或参数名不匹配, 缺少returns或返回类型不匹配, 格式请参考下方无注释检测文档.`
	return a + b
}

// @描述：数据相加
// @参数：
//    - a：加数
// @返sss回：
//    - int：被加数
func Add3(a, b int) int { //want `注释缺少params或参数名不匹配, 缺少returns或返回类型不匹配, 格式请参考下方无注释检测文档.`
	return a + b
}

//@描述：数据相加
//@参aaa数：
//    - a：加数
//    - b：被加数
//@返回：
//    - int：被加数
func Add4(a, b int) int { // want `注释缺少params或参数名不匹配, 格式请参考下方无注释检测文档.`
	return a + b
}

//@描述：数据相加
//@参数：
//    - a：加数
//    - b：被加数
//@返回：
//    - int：被加数
func Add5(a, b int) int { //waznt `missing params comments, 格式请参考下方无注释检测文档.`
	return a + b
}

//@返回：
//    - int：被加数
func Add6() int { // want `注释缺少description, 格式请参考下方无注释检测文档.`
	return 0
}

//@描述：数据相加
func Add7() { // wasnt `注释缺少params, 格式请参考下方无注释检测文档.`
	return
}

//@description:ins
//@params:
//    - c: onnn
//    - d: another number of compare/
//@returns:
//    - int:dff
//    - int:fasdsa
func rm(num1, num2 int) int { // want `注释缺少params或参数名不匹配,  description和returns长度小于4, 格式请参考下方无注释检测文档.`
	return num1 + num2
}
