package nocomments

import (
	"fmt"
	"go/ast"
	"strings"
	"sync"

	"golang.org/x/tools/go/analysis"
	"golang.org/x/tools/go/analysis/passes/inspect"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/passes/nocomments/config"
)

type CommentMap struct {
	Mp       map[*ast.FuncDecl]string
	Comments map[*ast.FuncDecl]string
	Pass     map[*ast.FuncDecl]*analysis.Pass
	Mutex    *sync.Mutex
	Count    int
}

var commentMap = &CommentMap{
	Mp:       make(map[*ast.FuncDecl]string),
	Comments: make(map[*ast.FuncDecl]string),
	Pass:     make(map[*ast.FuncDecl]*analysis.Pass),
	Mutex:    new(sync.Mutex),
	Count:    0,
}

var TestMode bool //测试标记
var inRepo bool

func init() {
	config.InitConf()
	config.GetCommit()
	inRepo = config.JudgeCodeRepository()
}

var Analyzer = &analysis.Analyzer{
	Name: "nocomments",
	Doc:  "check the function comments is exist or not",
	Requires: []*analysis.Analyzer{
		inspect.Analyzer,
	},
	Run: run,
}

func run(pass *analysis.Pass) (interface{}, error) {
	if !inRepo && !TestMode {
		return nil, nil
	}
	packageName := ""
	for _, f := range pass.Files {
		ast.Inspect(f, func(x ast.Node) bool {
			s, ok := x.(*ast.FuncDecl)
			if !ok {
				s, ok := x.(*ast.File)
				if !ok {
					return true
				}
				packageName = s.Name.String()
				return true
			}

			if s.Doc == nil {
				commentMaps := &NoCommentMap{LackComment: []string{"缺少description, 缺少params, 缺少returns"}}
				commentMap.loadLockMap(s, packageName+"-"+getNote(s), getCommentsParams(commentMaps), pass)
				return false
			}

			param, flag := resolveComments(s)
			if !flag {
				commentMap.loadLockMap(s, packageName+"-"+getNote(s), getCommentsParams(param), pass)
			}

			return false
		})
	}

	doRule(pass)

	return nil, nil
}

func doRule(pass *analysis.Pass) {
	commentMap.Mutex.Lock()
	defer commentMap.Mutex.Unlock()
	if TestMode { //模式判断
		commentMap.noteReport()
		return
	}

	commentMap.filterFunc(config.FuncMap) //函数过滤
	if commentMap.Count >= config.Num {
		commentMap.noteReport()
	}
}

func getNote(node *ast.FuncDecl) string { //获取方法
	recv := node.Recv
	if recv == nil {
		return ""
	}

	for _, filed := range recv.List {
		a := filed.Type
		s, ok := a.(*ast.StarExpr)
		if !ok {
			continue
		}

		if ss, ok := s.X.(*ast.Ident); ok {
			rec := ss.Name
			return rec
		}

	}

	return ""
}

func (this *CommentMap) noteReport() { //生成报告
	for node, _ := range this.Mp {
		this.Pass[node].Reportf(node.Pos(), `[netdisk-sa][NoComments]: 注释%s, 格式请参考无注释检测文档: http://agroup.baidu.com/share/md/aa96e76ab6a547f79cec7d552c26e095.`, this.Comments[node])
		delete(this.Mp, node)
	}
}

func (this *CommentMap) filterFunc(funcDiff map[string]struct{}) { //函数过滤
	for node, packageMethod := range this.Mp {
		funcName := node.Name.Name
		split := strings.Split(packageMethod, "-")
		if len(split) < 2 {
			continue
		}
		pack := split[0]
		method := split[1]
		key := config.NameString(pack, method, funcName)
		_, isFuncNoCommet := funcDiff[key]
		if !isFuncNoCommet {
			delete(this.Mp, node)
			delete(this.Pass, node)
		}
		this.Count++
	}
}

func (this *CommentMap) loadLockMap(s *ast.FuncDecl, value, comment string, pass *analysis.Pass) {
	this.Mutex.Lock()
	defer this.Mutex.Unlock()
	this.Mp[s] = value
	this.Pass[s] = pass
	this.Comments[s] = comment
}

// 获取注释确实部分，空则表示注释全缺，1-缺描述，2-缺参数，3-缺返回值
func getCommentsParams(arr *NoCommentMap) string {
	strComment := ""
	lackComment := strings.Join(arr.LackComment, ", ")
	lenComment := strings.Join(arr.ShorterComment, "和")

	if lackComment != "" {
		strComment += lackComment
	}
	if lenComment != "" {
		if strComment != "" {
			strComment += ",  "
		}
		strComment += lenComment + fmt.Sprintf("长度小于%d", config.Conf.LenParams)
	}

	return strComment
}

type NoCommentMap struct {
	LackComment    []string
	ShorterComment []string
}

func resolveComments(node *ast.FuncDecl) (*NoCommentMap, bool) {
	commentMaps := &NoCommentMap{}
	flag := true

	params, results := getParamAndResult(node)

	comment := node.Doc
	comText := comment.Text()

	//英文
	comText = strings.Replace(comText, " ", "", -1)
	if comText == "" {
		commentMaps.LackComment = append(commentMaps.LackComment, "description", "params", "returns")
		return commentMaps, false
	}
	comText = strings.Replace(comText, "：", ":", -1)
	comText = strings.Replace(comText, "\t", "", -1)
	comText = strings.ToLower(comText)
	//处理注释
	var (
		desc string
		para string
		retu string
	)

	stSplit := strings.Split(comText, "@")
	for _, value := range stSplit {
		for _, v := range config.CommentMap {
			if strings.Contains(value, v+":") {
				switch v {
				case "description":
					desc = value
				case "params":
					para = value
				case "returns":
					retu = value
				case "描述":
					desc = value
				case "参数":
					para = value
				case "返回":
					retu = value
				default:
					continue
				}
			}
		}
	}

	des := strings.Split(desc, "\n")[0] // 描述
	split := strings.Split(des, ":")
	if len(split) != 2 { //描述越界
		commentMaps.LackComment = append(commentMaps.LackComment, "缺少description")
		flag = false
	} else if (split[0] != "description" && split[0] != "描述") || len(split[1]) < config.Conf.LenParams {
		if len(split[1]) < config.Conf.LenParams {
			commentMaps.ShorterComment = append(commentMaps.ShorterComment, "description")
		} else {
			commentMaps.LackComment = append(commentMaps.LackComment, "缺少description")
		}
		flag = false
	}

	if num := judgeComments(params, para); num != 0 {
		if num == 1 { //缺少注释
			commentMaps.LackComment = append(commentMaps.LackComment, "缺少params或参数名不匹配")
		} else { //num==2 注释长度太短
			commentMaps.ShorterComment = append(commentMaps.ShorterComment, "params")
		}
		flag = false
	}

	if num := judgeComments(results, retu); num != 0 {
		if num == 1 { //缺少注释
			commentMaps.LackComment = append(commentMaps.LackComment, "缺少returns或返回类型不匹配")
		} else { //num==2 注释长度太短
			commentMaps.ShorterComment = append(commentMaps.ShorterComment, "returns")
		}
		flag = false
	}

	return commentMaps, flag
}

func judgeComments(arr []string, str string) int { //0-注释OK，1-缺少注释，2-注释长度太短
	if len(arr) > 0 {
		resultInfo, flag := reloveParamAndReult(str)
		if flag != 0 {
			return flag
		}

		for _, value := range arr {
			v, ok := resultInfo[value]
			if !ok || v == 0 {
				return 1
			}
			resultInfo[value]--
		}
	}

	return 0
}

func getParamAndResult(node *ast.FuncDecl) ([]string, []string) {
	params := []string{}
	results := []string{}

	paramsList := node.Type.Params
	resultsList := node.Type.Results
	if paramsList != nil {
		for _, param := range paramsList.List { //获取params
			idents := param.Names
			for _, value := range idents {
				params = append(params, strings.ToLower(value.Name))
			}
		}
	}

	if resultsList != nil {
		for _, result := range resultsList.List { //获取返回
			idents := result.Names
			resType := result.Type
			ss, ok := resType.(*ast.Ident)
			if !ok {
				continue
			}

			if len(idents) == 0 {
				results = append(results, strings.ToLower(ss.Name))

			} else {
				for i := 0; i < len(idents); i++ {
					results = append(results, strings.ToLower(ss.Name))
				}
			}
		}
	}

	return params, results
}

// 0表示，注释无误，1表示缺注释，2表示注释长度问题
func reloveParamAndReult(str string) (map[string]int, int) {
	info := make(map[string]int)
	split := strings.Split(str, "\n")
	if len(split) < 2 {
		return info, 1
	}

	for i := 1; i < len(split); i++ {
		paramInfo := strings.Split(split[i], "-")
		if len(paramInfo) < 2 {
			continue
		}

		//若关键字符在注释内容中，被切分为多个，则重新组合为原注释内容
		//case:
		//	-a:max-min
		//paramInfo为：["  ","a:max","min"]
		//注释内容应该为：a:max-min
		//paramContent则恢复为：paramInfo[1]+"-"+paramInfo[2]
		paramContent := strings.Join(append([]string{}, paramInfo[1:]...), "-")
		s := strings.Split(paramContent, ":")
		if len(s) < 2 { //越界处理
			return info, 1
		}
		paramName := s[0]

		//注释为：a:Min(a:b)
		//paramName:a
		//content内容为：Min(a:b)
		//content内容即：len(paramName)+1
		content := paramContent[len(paramName)+1:]
		if len(content) < config.Conf.LenParams {
			return info, 2
		}

		info[paramName]++
	}

	return info, 0
}
