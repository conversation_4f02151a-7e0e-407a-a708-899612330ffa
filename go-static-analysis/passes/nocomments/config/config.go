package config

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

type Config struct {
	CodeRepository map[string]int `json:"codeRepository"`
	LenParams      int            `json:"lenParams"`
}

var FuncMap = map[string]struct{}{}
var Conf Config
var Path string

var Num = 0
var CommentMap = map[int]string{
	1: "description",
	2: "params",
	3: "returns",
	4: "描述",
	5: "参数",
	6: "返回",
}

func InitConf() {
	configFile := "nocomments.json"
	err := util.LoadConf(configFile, &Conf)
	if err != nil {
		setConf(&Conf)
	}
}

func setConf(c *Config) {
	c.CodeRepository = map[string]int{
		"baidu/netdisk/nd-go-clouddisk":            0,
		"baidu/netdisk/clouddisk-note":             0,
		"baidu/netdisk/clouddisk-share-worker":     0,
		"baidu/netdisk/nd-content-keywords":        0,
		"baidu/netdisk/data-platform":              0,
		"baidu/netdisk/pimworker":                  0,
		"baidu/netdisk/nd-es-store":                0,
		"baidu/netdisk/nd-go-tickets":              0,
		"baidu/netdisk/platform-idproducter":       0,
		"baidu/netdisk/nd-go-mbox":                 0,
		"baidu/netdisk/platform-kiss":              0,
		"baidu/netdisk/nd-go-monitor":              0,
		"baidu/netdisk/nd-go-relation":             0,
		"baidu/netdisk/nd-search-userctl":          0,
		"baidu/netdisk/nd-strategy-worker":         0,
		"baidu/netdisk/nd-rfs-sysiphus":            0,
		"baidu/netdisk/nd-traces":                  0,
		"baidu/netdisk/nd-c2c-copyright":           0,
		"baidu/netdisk/minosbeat":                  0,
		"baidu/netdisk/nd-recent-worker":           0,
		"baidu/netdisk/nd-sharedir-worker":         0,
		"baidu/netdisk/clouddisk-share":            0,
		"baidu/netdisk/nd-antispam-new":            0,
		"baidu/netdisk/backups":                    0,
		"baidu/netdisk/nd-go-cloudconfig":          0,
		"baidu/netdisk/clouddisk-config":           0,
		"baidu/netdisk/nd-connectorproxy":          0,
		"baidu/netdisk/nd-go-dss":                  0,
		"baidu/netdisk/nd-go-pcode":                0,
		"baidu/netdisk/platform-services":          0,
		"baidu/netdisk/nd-go-search":               0,
		"baidu/netdisk/nd-recent":                  0,
		"baidu/netdisk/nd-sharedir":                0,
		"baidu/netdisk/clouddisk-subscribe":        0,
		"baidu/netdisk/nd-wechat-mina":             0,
		"baidu/netdisk/nd-go-pim":                  0,
		"baidu/netdisk/nd-searchdata":              0,
		"baidu/netdisk/nd-torrent-detect":          0,
		"baidu/netdisk/xpan-audio":                 0,
		"baidu/netdisk/xpan-dms":                   0,
		"baidu/xpan/open-platform":                 0,
		"baidu/xpan/panlink":                       0,
		"baidu/xpan/strategy":                      0,
		"baidu/xpan/union-worker":                  0,
		"baidu/netdisk/xpan-audiometa":             0,
		"baidu/netdisk/xpan-audioworker":           0,
		"baidu/xpan/audit":                         0,
		"baidu/netdisk/xpan-multimedia":            0,
		"baidu/netdisk/xpan-polling-worker":        0,
		"baidu/xpan/wps":                           0,
		"baidu/netdisk/nd-go-clouddisk-lite":       0,
		"baidu/netdisk/nd-clouddisk-optimization":  0,
		"baidu/netdisk/nd-coupon":                  0,
		"baidu/netdisk/nd-go-flow":                 0,
		"baidu/netdisk/nd-go-flow-worker":          0,
		"baidu/netdisk/nd-mbox-groupsearch-worker": 0,
		"baidu/netdisk/nd-search-groupstore":       0,
		"baidu/netdisk/nd-gs-tskmanage":            0,
		"baidu/netdisk/nd-mirror":                  0,
		"baidu/netdisk/nd-mirror-front":            0,
		"baidu/netdisk/nd-mbox-multimsg-worker":    0,
		"baidu/netdisk/nd-go-antiproxy":            0,
		"baidu/clouddisk/clouddisk-pim":            0,
		"baidu/netdisk/nd-config-center":           0,
		"baidu/netdisk/nd-pointmall-platform":      0,
		"baidu/netdisk/nd-go-quotamall":            0,
		"baidu/netdisk/nd-recent-timeline-worker":  0,
		"baidu/netdisk/platform_risk":              0,
		"baidu/netdisk/queryintent":                0,
		"baidu/netdisk/sug-offline":                0,
		"baidu/netdisk/trans-file-worker":          0,
		"baidu/netdisk/unamecache-backup":          0,
		"baidu/netdisk/xpan-console":               0,
		"baidu/netdisk/xpan-trans":                 0,
		"baidu/netdisk/xpan-trans-worker":          0,
		"baidu/netdisk/xpan-whitebox":              0,
		"baidu/netdisk/xpan-whitebox-worker":       0,
		"baidu/netdisk/zk-gray-config":             0,
	}

	c.LenParams = 4
}

//判定代码库以及函数num
func JudgeCodeRepository() bool {
	pwd, err := os.Getwd()
	if err != nil {
		return false
	}

	return getRunDirName(pwd)

}

func getRunDirName(path string) bool {
	for repository, num := range Conf.CodeRepository {
		if isTail := strings.HasSuffix(path, repository); isTail {
			Num = num
			return true
		}
	}

	return false
}

func GetCommit() { //增量信息拉取
	commitId, err := getCommitInfo("git rev-parse HEAD") //获取ID
	if err != nil {
		panic(err)
	}
	commitInfo, err := getCommitInfo("git show " + string(commitId)) //获取diff
	if err != nil {
		panic(err)
	}

	getDiffFunc(string(commitInfo))
}

func getCommitInfo(url string) ([]byte, error) { //拉取
	ctxt, cancel := context.WithTimeout(context.Background(), 3000*time.Millisecond)
	defer cancel()
	cmd := exec.CommandContext(ctxt, "/bin/bash", "-c", url)

	res, err := cmd.Output()
	if err != nil {
		if ctxt.Err() != nil && ctxt.Err() == context.DeadlineExceeded {
			return res, fmt.Errorf("command timeout")
		}
	}

	return res, nil
}

func getDiffFunc(diffInfo string) { //增量信息处理
	infoSplit := strings.Split(diffInfo, "diff")
	for _, diffSplit := range infoSplit {
		getFuncName(diffSplit)
	}
}

func getFuncName(info string) { //增量数据处理
	rowsDiff := strings.Split(info, "\n")
	if isContains := strings.Contains(rowsDiff[0], ".go "); !isContains {
		return
	}

	packageName := ""
	for i := range rowsDiff {
		if isAdd := strings.HasPrefix(rowsDiff[i], "+++"); isAdd {
			location := rowsDiff[i][5:]
			locSplit := strings.Split(location, "/")
			if len(locSplit) > 1 {
				packageName = locSplit[len(locSplit)-2]
			}
		}

		if diffFunc := strings.HasPrefix(rowsDiff[i], "+func"); diffFunc {
			recv, funcName := getRecvAndFunc(rowsDiff[i])
			FuncMap[NameString(packageName, recv, funcName)] = struct{}{}
		}
	}
}

func getRecvAndFunc(str string) (string, string) {
	recv := "" //若是方法，则获取接收者
	funcName := ""

	str = strings.Join(strings.Fields(str), " ")
	rowInfo := str[5:]

	rowSplit := strings.Split(rowInfo, ")")
	for i := range rowSplit {
		split := strings.Split(rowSplit[i], "(")

		if split[0] == " " {
			recvSplit := strings.Split(split[1], " ")
			if recvSplit[1][0] == '*' {
				recv = recvSplit[1][1:]
			} else {
				recv = recvSplit[1]
			}

		} else {
			if split[0][0] == ' ' {
				split[0] = strings.Replace(split[0], " ", "", 1)
			}
			funcName = split[0]
			break
		}
	}

	return recv, funcName
}

func NameString(pack, recv, funcName string) string {
	return fmt.Sprintf("%s-%s-%s", pack, recv, funcName)
}
