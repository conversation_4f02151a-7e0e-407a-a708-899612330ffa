package config

import (
	"testing"
)

func TestGetRecvAndFunc(t *testing.T) {
	str := "+func (ad *abvc) ThemisDelete(id string) error {"
	str1 := "+func (ad abvc) ThemisDelete(id string) error {"
	str2 := "+func (ad abvc)   ThemisDelete(id string) error {"
	str3 := "+func (ad abvc)   ThemisDelete(id string)error {"
	str4 := "+func (ad *abvc)ThemisDelete(id string)error {"
	str5 := "+func ThemisDelete(id string)error {"
	arr := []string{str, str1, str2, str3, str4}
	for i, s := range arr {
		a, b := getRecvAndFunc(s)
		if a == "abvc" && b == "ThemisDelete" {
			t.Log("测试通过 ", i)
		} else {
			t.<PERSON><PERSON><PERSON>("wrong %d %s %s", i, a, b)
		}
	}

	a, b := getRecvAndFunc(str5)
	if a == "" && b == "ThemisDelete" {
		t.Log("测试通过 ")
	} else {
		t.<PERSON><PERSON><PERSON>("wrong %s %s", a, b)
	}
}
