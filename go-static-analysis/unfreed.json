{"rules": [{"rule_name": "FdClose", "resource": "*os.File", "y": [{"type": "io.Closer", "fn": "(io.Closer).Close", "params": "x"}, {"type:": "*os.File", "fn": "(*os.File).Close", "params": "x"}], "max_depth": 3, "escape": true}, {"rule_name": "BodyClose", "resource": "*net/http.Response", "y": [{"type": "io.Closer", "fn": "(io.Closer).Close", "params": "x"}], "escape": true, "max_depth": 3}, {"rule_name": "RowsClose", "resource": "*database/sql.Rows", "y": [{"type": "*database/sql.Rows", "fn": "(*database/sql.Rows).Close", "params": "x"}, {"type": "io.Closer", "fn": "(io.Closer).Close", "params": "x"}], "escape": true, "max_depth": 3}]}