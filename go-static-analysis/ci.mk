# init project path
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

# init command params
GO      := $(shell which go)
GOROOT  := $(shell $(GO) env GOROOT)
GOPATH  := $(shell $(GO) env GOPATH)
GOMOD   := $(GO) mod
GOBUILD := $(GO) build
GOTEST  := $(GO) test -gcflags="-N -l"
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")

# test cover files
COVPROF := $(HOMEDIR)/covprof.out  # coverage profile
COVFUNC := $(HOMEDIR)/covfunc.txt  # coverage profile information for each function
COVHTML := $(HOMEDIR)/covhtml.html # HTML representation of coverage profile

# make, make all
all: prepare compile package

# set proxy env
set-env:
	$(GO) env -w GO111MODULE=on
	$(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com
	$(GO) env -w GONOSUMDB=\*

#make prepare, download dependencies
prepare: gomod
gomod: set-env
	$(GOMOD) download

#make compile
compile: build
build:
	$(GOBUILD) -trimpath -o followedby $(HOMEDIR)/cmd/followedby/main.go
	$(GOBUILD) -trimpath -o unfreed $(HOMEDIR)/cmd/unfreed/main.go
	$(GOBUILD) -trimpath -o nocomments $(HOMEDIR)/cmd/nocomments/main.go
	$(GOBUILD) -trimpath -o report $(HOMEDIR)/cmd/report/main.go
	$(GOBUILD) -trimpath -o pre $(HOMEDIR)/cmd/pre/main.go
	$(GOBUILD) -trimpath -o reverse  $(HOMEDIR)/cmd/reverse/main.go
	$(GOBUILD) -trimpath -o ifnil  $(HOMEDIR)/cmd/ifnil/main.go
	$(GOBUILD) -trimpath -o parameter  $(HOMEDIR)/cmd/parameter/main.go

# make test, test your code
test: prepare test-case
test-case:
	$(GOTEST) -v -cover $(GOPKGS)

# make package
package: package-bin
package-bin:
	mkdir -p $(OUTDIR)
	mv pre reverse ifnil parameter parameter.json followedby unfreed nocomments followedby.json unfreed.json nocomments.json report $(OUTDIR)/

# make clean
clean:
	$(GO) clean
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/followedby
	rm -rf $(HOMEDIR)/unfreed
	rm -rf $(HOMEDIR)/nocomments
	rm -rf $(GOPATH)/pkg/darwin_amd64

# avoid filename conflict and speed up build
.PHONY: all prepare compile test package clean build
