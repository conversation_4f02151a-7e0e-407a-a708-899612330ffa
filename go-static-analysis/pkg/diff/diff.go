package diff

import (
	"fmt"
	"go/token"
	"os"
	"strings"
	"sync"

	"golang.org/x/tools/go/analysis"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/revgrep"
	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

var easyEnabledDiff = os.Getenv("EASY_ENABLED_DIFF") // 该环境变量由report或者单个easy规则传入，report由flag控制是否传入

var Differ = NewDiff("HEAD~", false)
var onceGetProgramName sync.Once
var ProjectPath string

// 处理包增量
type Diff struct {
	Checker           *revgrep.Checker
	CheckerPrepareErr error

	Pwd      string
	PwdError error
	Once     sync.Once
}

type Issue struct {
	Fset       *token.FileSet
	Diagnostic *analysis.Diagnostic
}

func (i *Issue) FilePath() string {
	onceGetProgramName.Do(func() {
		pwd, err := os.Getwd()
		if err != nil {
			Differ.PwdError = err
			return
		}

		Differ.Pwd = pwd
	})

	filePath := i.Fset.Position(i.Diagnostic.Pos).Filename
	if Differ.PwdError != nil {
		return filePath
	}

	pwd := Differ.Pwd
	res := strings.TrimPrefix(filePath, pwd)
	res = strings.TrimLeft(res, "/")

	return res
}

func (i *Issue) Line() int {
	return i.Fset.Position(i.Diagnostic.Pos).Line
}

func NewDiff(fromRev string, wholeFiles bool) *Diff {
	checker := &revgrep.Checker{
		RevisionFrom: fromRev,
		WholeFiles:   wholeFiles,
	}

	return &Diff{
		Checker: checker,
	}
}

func (p *Diff) IssueNeedReport(issue *Issue) bool {
	if easyEnabledDiff == "true" { // 此时开启增量检测
		return p.IsNewIssue(issue)
	}

	return true
}

func (p *Diff) IsNewIssue(issue *Issue) bool {
	p.Once.Do(
		func() {
			err := p.Checker.Prepare()
			if err != nil {
				p.CheckerPrepareErr = err
				msg := fmt.Sprintf("IsNewIssue CheckerPrePareErr: %v ", p.CheckerPrepareErr)
				utils.SendWarnMessageToHiForDailyMessage(msg)
			}
		})

	if p.CheckerPrepareErr != nil {
		return false
	}

	_, ok := p.Checker.IsNewIssue(issue)
	return ok
}

func NewIssue(fset *token.FileSet, pos token.Pos, format string, args ...interface{}) *Issue {
	msg := fmt.Sprintf(format, args...)
	return &Issue{
		Fset: fset,
		Diagnostic: &analysis.Diagnostic{
			Pos:     pos,
			Message: msg,
		},
	}
}
