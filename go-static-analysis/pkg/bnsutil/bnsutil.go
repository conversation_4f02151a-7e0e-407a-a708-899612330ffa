package bnsutil

import (
	"fmt"
	"math/rand"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/cmd/nd-gocheck/pkg/utils"
)

type BnsInfo struct {
	ContainerName string
	IP            string
	Port          string
}

func GetBnsInfoGroups(bnsName string) ([]*BnsInfo, error) {
	command := fmt.Sprintf("get_instance_by_service -ips %s", bnsName)
	output, err := utils.ExecCommandWithRetry(command)
	if err != nil {
		return nil, err
	}

	var res []*BnsInfo
	slice := strings.Split(output, "\n")

	for _, item := range slice {
		item := strings.TrimSpace(item)
		if item == "" {
			continue
		}

		subItems := strings.Split(item, " ")
		if len(subItems) != 4 {
			return nil, fmt.Errorf("subItems len is not equal 4, sub items is %v", subItems)
		}

		if strings.TrimSpace(subItems[3]) != "0" { // 0 mean normal
			continue
		}

		containerName := strings.TrimSpace(subItems[0])
		ip := strings.TrimSpace(subItems[1])
		port := strings.TrimSpace(subItems[2])

		bnsInfo := &BnsInfo{
			ContainerName: containerName,
			IP:            ip,
			Port:          port,
		}
		res = append(res, bnsInfo)
	}

	return res, nil
}

func GetOneAddrByRandom(bnsName string) (string, error) {
	infos, err := GetBnsInfoGroups(bnsName)
	if err != nil {
		return "", err
	}

	infosLen := len(infos)
	if infosLen == 0 {
		return "", fmt.Errorf("can not found normal instance for bns %s", bnsName)
	}

	if infosLen == 1 {
		addr := fmt.Sprintf("%s:%s", infos[0].IP, infos[0].Port)
		return addr, nil
	}

	rand.Seed(time.Now().Unix())
	index := rand.Int() % infosLen

	addr := fmt.Sprintf("%s:%s", infos[index].IP, infos[index].Port)
	return addr, nil
}
