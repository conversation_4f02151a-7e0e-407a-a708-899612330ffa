package utils

import (
	"encoding/json"
	"fmt"
	"os"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const WFStatURL = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d9d00bea8cbe8b7d6c74d12bda373a70e"
const OfflineToolURL = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d9d00bea8cbe8b7d6c74d12bda373a70"

// 该报警群无QA，收集些详细信息
func SendWarnMessageToHiForDailyMessage(msg string) {
	pwd, err := os.Getwd()
	if err != nil {
		pwd = "获取代码库所在目录失败"
	}
	realMsg := fmt.Sprintf("pwd: %s\nmsg: %s", pwd, msg)
	sendHIMsgInner(OfflineToolURL, realMsg)
}

func SendWFStatMessageToHi(msg string) {
	sendHIMsgInner(WFStatURL, msg)
}

func sendHIMsgInner(url string, msg string) {
	headerMap := map[string]string{}
	headerMap["Content-Type"] = "application/json"

	conMaps := []map[string]interface{}{}
	conMap := map[string]interface{}{}
	conMap["type"] = "TEXT"
	conMap["content"] = msg
	conMaps = append(conMaps, conMap)
	bodyMap := map[string]interface{}{}
	bodyMap["body"] = conMaps
	messageMap := map[string]interface{}{}
	messageMap["message"] = bodyMap
	bodyJSON, _ := json.Marshal(messageMap)

	for i := 0; i < 3; i++ {
		_, err := httpclient.Post(url, headerMap, 1000, 3000, string(bodyJSON))
		if err == nil {
			break
		}
	}
}
