package utils

import (
	"errors"
	"fmt"
	"os"
	"os/exec"
	"strings"
)

func IsDir(path string) bool {
	s, err := os.Stat(path)
	if err != nil {
		return false
	}
	return s.IsDir()
}

func GetProjectName() (string, error) {
	output, err := ExecScript(`git remote -v | head -1 | awk {'print $2'}`)
	if err != nil {
		return "", err
	}
	output = strings.TrimSpace(output)
	i := strings.Index(output, "8235/baidu")
	if i == -1 {
		return "", errors.New("8235 is not found")
	}
	return output[i+5:], nil
}

func GetCommitUserName() (string, error) {
	cmd := "git log -1 --pretty=format:'%ae'"
	output, err := ExecScript(cmd)
	if err != nil {
		// 这里把output也输出，具体的err信息在output
		return output, err
	}

	nameWithMail := strings.TrimSpace(output)
	return strings.TrimSuffix(nameWithMail, "@baidu.com"), nil
}

func ExecScript(script string, args ...string) (string, error) {
	cmd := exec.Command("/bin/bash", append([]string{"-s", "-"}, args...)...)
	wc, err := cmd.StdinPipe()
	if err != nil {
		return "", err
	}

	go func() {
		defer wc.Close()
		fmt.Fprintf(wc, "%s", script)
	}()

	output, err := cmd.CombinedOutput()
	return string(output), err
}
