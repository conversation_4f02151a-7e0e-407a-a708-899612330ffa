package report

var tmpl = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
	<style>
		.list-group {
			display: flex;
			flex-direction: column;
			padding-left: 0;
			margin-bottom: 0;
			border-radius: .25rem
		}

		.list-group-item {
			position:relative;
			display:block;
			padding:.5rem 1rem;
			color:#212529;
			text-decoration:none;
			background-color:#fff;
			border:1px solid rgba(0,0,0,.125)
		}
		.list-group-item-action {
			width:100%;
			color:#495057;
			text-align:inherit
		}
		.bg-secondary {
			--bs-bg-opacity:1;
			background-color:rgba(108,117,125,var(--bs-bg-opacity))!important
		}

		.bg-opacity-50 {
			--bs-bg-opacity:0.5
		}

		.d-md-flex {
			display:flex!important
		}

		.w-100 {
			width: 100%!important
		}
		.justify-content-between {
			justify-content: space-between!important
		}

		.mb-1 {
			margin-bottom: .25rem!important
		}

		.text-dark {
			--bs-text-opacity: 1;
			color: rgba(33,37,41,var(--bs-text-opacity))!important
		}

		.badge {
			display: inline-block;
			padding: .35em .65em;
			font-size: .75em;
			font-weight: 700;
			line-height: 1;
			color: #fff;
			text-align: center;
			white-space: nowrap;
			vertical-align: baseline;
			border-radius: .25rem
		}

		.bg-danger {
			--bs-bg-opacity: 1;
			background-color: rgba(220,53,69,var(--bs-bg-opacity))!important
		}

		.d-flex {
			display: flex!important
		}
		.w-100 {
			width: 100%!important
		}

		.justify-content-between {
			justify-content: space-between!important
		}
	</style>

    <style>
        #container {
            text-align: center;
        }

        #container>div {
            width: 100%;
            max-width: 1200px;
            display: inline-block;
            text-align: left;
        }
    </style>
</head>

<body>
	<div id="container">
		{{template "saOutput" .}}
		{{template "document" .}}
		{{template "summary" .}}
	</div>
</body>

</html>

{{define "saOutput"}}
	{{if .SAOutput}}
		<div>
			<h2>Diagnostics</h2>
		</div>
		{{range .SAOutput}}
			{{template "saOutputPerFile" .}}
		{{end}}
	{{end}}
{{end}}

{{define "document"}}
	<div>
	</div>
	<div>
		<h2>Document</h2>
	</div>
	<div class="border">
		<pre style="margin: 0; max-height: 200vh; color: red; font-size: 180%"><code>{{.PluginDocument}}</code></pre>
		<pre style="margin: 0; max-height: 200vh; color: red; font-size: 150%"><code>{{.PluginLink}}</code></pre>
		<pre style="margin: 0; max-height: 75vh;"><code>{{.Document}}</code></pre>
	</div>
{{end}}

{{define "summary"}}
	<div>
	</div>
	<div>
		<h2>Summary</h2>
	</div>
	<div class="border">
		<pre style="margin: 0; max-height: 75vh;"><code>{{.Summary}}</code></pre>
	</div>
{{end}}

{{define "saOutputPerFile"}}
	<div class="list-group">
		<a class="list-group-item list-group-item-action bg-secondary bg-opacity-50" id="{{incAnchor}}" href="#{{getAnchor}}">
			<div class="d-flex w-100 justify-content-between">
				<p class="mb-1 text-dark">{{.Filename}}</p>
				<small><span class="badge bg-danger">{{len .Diagnostics}}</span></small>
			</div>
		</a>
		{{range .Diagnostics}}
			{{template "diagnostic" .}}
		{{end}}
	</div>
{{end}}

{{define "diagnostic"}}
	<a id="{{incAnchor}}" href="{{if .URL}}{{.URL}}{{else}}#{{getAnchor}}{{end}}" class="list-group-item list-group-item-action">
		<div class="d-flex w-100 justify-content-between">
			<p class="mb-1" style="font-style: italic;">第{{.Line}}行, 第{{.Col}}列</p>
			<small style="font-style: italic;">{{.Analyzer}}</small>
		</div>
		<p class="mb-1">{{.Message}}</p>
	</a>
{{end}}
`
