package report

var scriptMoveBinFile = `
set -ex
cp $1 .
tar xzf go-static-analysis.tar.gz
`

var scriptMoveBinFileForRegularMode = `
set -e
cp -f $1 .
`

var scriptRunErrcheckRuleForRegularMode = `
set -e
export GOPATH=$1 					
export GOMODCACHE=$1/pkg/mod		
export GO111MODULE=on
export GONOPROXY='*.baidu.com*'
export GONOSUMDB='*'
export GOPROXY=https://goproxy.baidu-int.com
export GOPRIVATE='*.baidu.com'

./nd-errcheck -blank ./... > errcheck.sa.json 
`

var scriptRunkRuleSingleMode = `
set -e
export GOPATH=$1 					
export GOMODCACHE=$1/pkg/mod		
export GO111MODULE=on
export GONOPROXY='*.baidu.com*'
export GONOSUMDB='*'
export GOPROXY=https://goproxy.baidu-int.com
export GOPRIVATE='*.baidu.com'

./$2  -json ./... > "$2.singlemode.json" 2>&1
exit 0
`

// ./golangci-lint run --no-config --max-issues-per-linter=0 --max-same-issues=0 --disable-all -E errcheck  > errcheck.sa.json

var scriptAnalysis = `
# set -e

go_mod=$1
export GOPATH=$2
export GOMODCACHE=$2/pkg/mod

git config http.sslVerify false

# download dependencies

if [[ ${go_mod} == "Y" ]] ; then
	export GO111MODULE=on
	export GONOPROXY='*.baidu.com*'
	export GONOSUMDB='*'
	export GOPROXY=https://goproxy.baidu-int.com
	export GOPRIVATE='*.baidu.com'
	
	go mod tidy
	go mod download

else

	export GO111MODULE=off
	export GOROOT=$(go env GOROOT) # it seems that god needs this

	godown()
	{
		god get "$1" || true

		if [[ -n "$2" && ! -e "$GOPATH/src/$2" ]] ; then
			rm -rf "$GOPATH/src/$2"
			ln -s -T "$GOPATH/src/$1" "$GOPATH/src/$2"
		fi
	}

	# some dependencies might NOT be managed
	# godown icode.baidu.com/baidu/netdisk/pcs-go-lib golib

	cwd=$(pwd)
	cwd_base=$(basename "$cwd")
	link_name=${GOPATH}/src/icode.baidu.com/baidu/netdisk/${cwd_base}
	mkdir -p $(dirname "${link_name}")
	rm -rf "${link_name}"
	ln -s -T "$cwd" "${link_name}"

	cd "$link_name"
	if [[ -f Godeps/Godeps.json ]] ; then
		god restore
	fi
fi

echo -n > ignored.txt

# ignore *_test.go
find . -name '*_test.go' \
	-exec mv '{}' '{}'.ignored ';' >>ignored.txt \
	-exec echo '{}' ';' 

# ignore doc.go
find . -name 'doc.go' \
	-exec mv '{}' '{}'.ignored ';'  >>ignored.txt \
	-exec echo '{}' ';' 

ignore_other_language() 
{
	find .  \( -name "*.h" -o -name "*.c" -o -name "*.cpp" -o -name "*.java" -o -name "*.php" -o -name "*.py" \) \
	-exec mv '{}' '{}'.ignored ';' \
	-exec echo '{}' ';'
}

ignore_script()
{
	find . -name "*script*" | while read dirname; do
		if [[ -d "$dirname" ]] ; then
			find "$dirname" -name '*.go' \
				-exec echo '{}' ';' \
				-exec mv '{}' '{}'.ignored ';' >>ignored.txt
		fi
	done
}

ignore_other_language
ignore_script

ignore_unused_dir()
{
	./pre ./... 2>&1 | sed -n -E \
		's/^-: code in directory ([^ ]+) expects import .*$/\1/p' | while read dirname ; do
		if [[ -d "$dirname" ]] ; then
			find "$dirname" -name '*.go' \
				-exec echo '{}' ';' \
				-exec mv '{}' '{}'.ignored ';' >>ignored.txt
		fi
	done
}

# ignore "unused" files
ignore_unused()
{
	while true ; do
		echo N >tmp.out

		# !!! the "while" is in another shell
		./pre ./... 2>&1 | sed -n -E \
			-e 's/^([^:]*\.go):[0-9]+:[0-9]+: .*$/\1/p' | while read filename ; do
			if [[ -f "$filename" ]] ; then
				echo Y >tmp.out
				echo "$filename" >>ignored.txt
				mv -f "$filename" "${filename}.ignored"
			fi
		done

		if [[ "$(cat tmp.out)" == "N" ]] ; then
			break
		fi
	done
}

ignore_unused_dir
ignore_unused

`

var preEasyCommand string = `
export GOPATH=$1 					
export GOMODCACHE=$1/pkg/mod		
export GO111MODULE=on
export GONOPROXY='*.baidu.com*'
export GONOSUMDB='*'
export GOPROXY=https://goproxy.baidu-int.com
export GOPRIVATE='*.baidu.com'
EASY_IS_ENABLE_DIFF=$2

run()
{
	chmod +x $1
	EASY_ENABLED_DIFF=$EASY_IS_ENABLE_DIFF ./$1 -json ./... > $1.easy.json
}

`

var preSACommand string = `
export GOPATH=$1
export GOMODCACHE=$1/pkg/mod		
export GO111MODULE=on
export GONOPROXY='*.baidu.com*'
export GONOSUMDB='*'
export GOPROXY=https://goproxy.baidu-int.com
export GOPRIVATE='*.baidu.com'

run()
{
	chmod +x $1
	./$1 -json ./... > $1.sa.json
}

`

var ScriptCheckGoMod = `
set -e

check_go_mod()
{
	if [[ ! -f go.mod ]] ; then
		return 1
	fi

	n=$(cat go.mod | grep -E '^require ' | wc -l)
	if [[ $n -gt 0 ]] ; then
		return 0
	fi

	if [[ ! -f Godeps/Godeps.json ]] ; then
		return 0
	fi

	n=$(cat Godeps/Godeps.json | grep '"ImportPath": ' | wc -l)
	if [[ $n -gt 0 ]] ; then
		return 1
	fi

	return 0
}

if check_go_mod ; then
	echo -n Y
else
	echo -n N
fi
`

var scriptEasyCodeMetric = `
set -e
./codemetric 
`

var scriptRunAPICollection = `
set -e
./apicollection 
`

var scriptRunAPICallgraph = `
set -e 
./callgraph ./... > callgraph.out
`
