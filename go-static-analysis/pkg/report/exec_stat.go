package report

import (
	"encoding/json"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

type EasyStatDataReqDto struct {
	Username    string                    `json:"username"`
	ProjectName string                    `json:"project_name"`
	CreateAt    int64                     `json:"create_at"`
	Info        []SimpleEasyOutputPerfile `json:"info"`
}

type SimpleEasyOutputPerfile struct {
	Filename         string             `json:"filename"`
	SimpleDiagnostic []SimpleDiagnostic `json:"diagnostics"`
}

type SimpleDiagnostic struct {
	Posn     string `json:"posn"`
	Message  string `json:"message"`
	Analyzer string `json:"analyzer"`
	URL      string `json:"url"`
}

func NewEasyStatDataReqDto(originData []SimpleEasyOutputPerfile) (*EasyStatDataReqDto, error) {
	userName, err := utils.GetCommitUserName()
	if err != nil {
		return nil, err
	}

	project, err := utils.GetProjectName()
	if err != nil {
		return nil, err
	}

	createAt := time.Now().Unix()

	res := &EasyStatDataReqDto{
		Username:    userName,
		ProjectName: project,
		CreateAt:    createAt,
		Info:        originData,
	}

	return res, nil
}

func SendEasyDataSever(easyOutputs []*EasyOuputPerFile) error {
	// test env
	url := "http://10.27.35.93:8057/easyrule?method=add"

	res := simplifyOutputFiles(easyOutputs)

	if len(res) == 0 {
		res = []SimpleEasyOutputPerfile{}
	}

	dto, err := NewEasyStatDataReqDto(res)
	if err != nil {
		return err
	}

	realData, err := json.Marshal(dto)
	if err != nil {
		return err
	}

	var errPost error
	for i := 0; i < 3; i++ {
		_, errPost = httpclient.Post(url, map[string]string{}, 1000, 3000, string(realData))
		if errPost == nil {
			break
		}
	}

	if errPost != nil {
		return errPost
	}

	return nil
}

func simplifyOutputFiles(inputs []*EasyOuputPerFile) []SimpleEasyOutputPerfile {
	var res []SimpleEasyOutputPerfile

	for _, input := range inputs {
		input := input
		item := simplifyOutputFile(input)
		item.Filename = input.Filename
		res = append(res, *item)
	}

	return res
}

func simplifyOutputFile(input *EasyOuputPerFile) *SimpleEasyOutputPerfile {
	res := &SimpleEasyOutputPerfile{}
	for _, item := range input.Diagnostics {
		i := &SimpleDiagnostic{}
		i.Posn = item.Posn
		i.Message = item.Message
		i.Analyzer = item.Analyzer
		i.URL = item.URL

		res.SimpleDiagnostic = append(res.SimpleDiagnostic, *i)
	}

	return res
}
