package report

import (
	"encoding/json"
	"errors"
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"io"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"text/template"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

const (
	RuleNoComment  = "nocomments"
	RuleReverse    = "reverse"
	RuleIfNil      = "ifnil"
	RuleFollowedby = "followedby"
	RuleUnfreed    = "unfreed"
	RuleParameter  = "parameter"
	RuleNilAway    = "nilaway"
	RuleSQLScan    = "sqlscan"

	RuleNameEasyCheckDiff  = "checkdiff"
	RuleNameEasyCheckErr   = "checkerr"
	RuleNameEasyCheckMain  = "checkmain"
	RuleNameEasyGoLibCheck = "golibcheck"
	RuleNameEasyNacCheck   = "naccheck"
	RuleNameEasyPortCheck  = "portcheck"
	RuleNameEasyRetry      = "retry"
	RuleNameEasySlogCheck  = "slogcheck"
	RuleNameEasyEasyLogger = "easylogger"

	RuleNameEasyCheckParameters = "checkparameters"
	RuleNameEasyCheckComment    = "checkcomment"

	// 非规则，得特殊处理
	APICollection = "apicollection"
	APICallGraph  = "callgraph"
)

// 执行的所有规则
var saRuleNames = []string{
	RuleNoComment,
	RuleReverse,
	RuleIfNil,
	RuleFollowedby,
	RuleUnfreed,
	RuleParameter,
	RuleNilAway,
	RuleSQLScan,
}

// easy框架额外执行的规则
var easyRuleNames = []string{
	RuleNameEasyCheckDiff,
	RuleNameEasyCheckErr,
	RuleNameEasyCheckMain,
	RuleNameEasyGoLibCheck,
	RuleNameEasyNacCheck,
	RuleNameEasyPortCheck,
	RuleNameEasyRetry,
	RuleNameEasySlogCheck,
	RuleNameEasyEasyLogger,

	APICollection,
	APICallGraph,

	RuleNameEasyCheckParameters,
	RuleNameEasyCheckComment,
}

var forbidEasyRule = map[string]struct{}{ // 检测但不展示的easy规则列表
	RuleNameEasyCheckDiff: {},
	APICollection:         {},
}

// 检测但不上报的非easy规则
var forbidSARule = map[string]struct{}{
	RuleNilAway: {},
	RuleSQLScan: {},
}

// 需要上报收集的 SA 规则
var saReportRule = map[string]struct{}{
	RuleNilAway: {},
}

type RunConfig struct {
	EasyEnableDiff     bool
	RegularMode        bool
	SingleMode         bool
	SingleModeRuleName string
	BinDir             string
	TarAddr            string
}

func NewRunConfig(easyEnableDiff bool, regularMode bool, tarAddr string, binDir string, singleMode bool, ruleName string) *RunConfig {
	return &RunConfig{
		EasyEnableDiff:     easyEnableDiff,
		RegularMode:        regularMode,
		SingleMode:         singleMode,
		SingleModeRuleName: ruleName,
		BinDir:             binDir,
		TarAddr:            tarAddr,
	}
}

type Diagnostic struct {
	Posn    string `json:"posn"`
	Message string `json:"message"`

	Filename string
	Line     int
	Col      int

	Pack     string
	Analyzer string

	URL string
}

type SAOutputPerFile struct {
	Filename    string
	Diagnostics []*Diagnostic
}

type EasyOuputPerFile = SAOutputPerFile

func (s *SAOutputPerFile) add(d *Diagnostic) {
	s.Diagnostics = append(s.Diagnostics, d)
}

func (d *Diagnostic) compare(other *Diagnostic) int {
	if d.Filename < other.Filename {
		return -1
	}
	if d.Filename > other.Filename {
		return 1
	}
	if d.Line < other.Line {
		return -1
	}
	if d.Line > other.Line {
		return 1
	}
	if d.Col < other.Col {
		return -1
	}
	if d.Col > other.Col {
		return 1
	}
	return 0
}

func (d *Diagnostic) cook(pack, analyzer string) (err error) {
	d.Pack = pack
	d.Analyzer = analyzer

	parts := strings.Split(d.Posn, ":")
	d.Filename = parts[0]
	if len(parts) > 1 {
		d.Line, err = strconv.Atoi(parts[1])
		if err != nil {
			return
		}
	}
	if len(parts) > 2 {
		d.Col, err = strconv.Atoi(parts[2])
		if err != nil {
			return
		}
	}

	filename, err := filepath.EvalSymlinks(d.Filename)
	if err != nil {
		return
	}
	cwd, err := filepath.EvalSymlinks(ctx.cwd)
	if err != nil {
		return
	}
	if cwd[len(cwd)-1] != '/' {
		cwd += "/"
	}
	if strings.HasPrefix(ctx.mod, "icode.baidu.com") && strings.HasPrefix(filename, cwd) {
		d.Filename = filename[len(cwd):]
		d.URL = fmt.Sprintf("https://%s/blob/%s/%s#L%d", ctx.mod, ctx.commitID, d.Filename, d.Line)
		d.URL = strings.Replace(d.URL, "https://icode.baidu.com/", "https://console.cloud.baidu-int.com/devops/icode/repos/", 1)
	}

	return nil
}

type Context struct {
	cwd      string
	mod      string
	commitID string

	goMod  bool
	goPath string
}

var ctx *Context = &Context{}

func (c *Context) initialize() (string, error) {
	output, err := utils.ExecScript(`pwd`)
	if err != nil {
		return output, err
	}
	c.cwd = strings.TrimSpace(string(output))
	if c.cwd[len(c.cwd)-1] != '/' {
		c.cwd += "/"
	}

	// c.mod = x.Module.Path
	repository := filepath.Base(c.cwd)
	if len(c.cwd)-len(repository)-1 < 0 {
		return "", errors.New("the path is error")
	}
	c.mod = fmt.Sprintf("icode.baidu.com/baidu/%s/%s", filepath.Base(c.cwd[:len(c.cwd)-len(repository)-1]), repository)
	output, err = utils.ExecScript(`git rev-parse --short HEAD`)
	if err != nil {
		return output, err
	}

	c.commitID = strings.TrimSpace(string(output))

	output, err = utils.ExecScript(ScriptCheckGoMod)
	if err != nil {
		return output, err
	}
	c.goMod = strings.TrimSpace(string(output)) == "Y"

	if c.goMod {
		c.goPath = "/home/<USER>/go19/gopath"
	} else {
		output, err = utils.ExecScript(`mktemp -d /tmp/go-static-analysis-XXXXXX`)
		if err != nil {
			return output, err
		}
		c.goPath = strings.TrimSpace(string(output))
	}

	return "", nil
}

var anchor int

func incAnchor() int {
	anchor++
	return anchor
}

func getAnchor() int {
	return anchor
}

type Recorder struct {
	Title  string
	GoMod  bool
	GoPath string // 给普通逻辑用的

	IsEasyProject bool

	Duration time.Duration
	Err      error // 普通执行err
	EasyErr  error
	Output   string

	Ignored []string

	SAOutput   []*SAOutputPerFile
	EasyOutput []*EasyOuputPerFile

	Summary string

	PluginDocument string // 该文档标红
	PluginLink     string

	ProjectName string

	CommitID   string
	CommitUser string

	Document string
}

func (r *Recorder) GenHTML() {
	r.GenSummay()

	t := template.Must(template.New("report").Funcs(template.FuncMap{
		"incAnchor": incAnchor,
		"getAnchor": getAnchor,
	}).Parse(tmpl))

	f, err := os.OpenFile("report.html", os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		panic(err)
	}
	defer f.Close()

	err = t.Execute(f, r)
	if err != nil {
		panic(err)
	}
}

func (r *Recorder) Init() error {
	projectName, err := utils.GetProjectName()
	if err != nil {
		return err
	}

	r.ProjectName = projectName

	if err := r.initCommitIDsAndCommitUser(); err != nil {
		return err
	}

	return nil
}

func (r *Recorder) GenSummay() {
	res := []string{}
	if r.Duration != time.Duration(0) {
		res = append(res, fmt.Sprintf("<<<Duration>>>\n%ds", int64(r.Duration.Seconds())))
	}
	if r.GoPath != "" {
		res = append(res, fmt.Sprintf("<<<GOPATH>>>\n%s", r.GoPath))
	}
	if len(r.Ignored) > 0 {
		res = append(res, fmt.Sprintf("<<<Ignored>>>\n%s", strings.Join(r.Ignored, "\n")))
	}
	if r.Err != nil {
		utils.SendWarnMessageToHiForDailyMessage("r.Err: \n" + r.Err.Error())
		res = append(res, fmt.Sprintf("<<<Error>>>\n%s", r.Err))
	}
	if r.Output != "" {
		res = append(res, fmt.Sprintf("<<<Output>>>\n%s", r.Output))
	}

	r.PluginDocument = "已有线下代码检测工具啦！！！ \n旨在降低等待流水线分析时间 & 降低多次提交不通过。线下装插件, 一键修完再CI.\n"
	r.PluginLink = "https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/8MCddZCpuH/SX81HQH0s5/aKDNe-wFI3aulq\n\n"
	// nolint:lll
	r.Document = "<<<无注释检测文档>>>\nhttp://agroup.baidu.com/share/md/aa96e76ab6a547f79cec7d552c26e095\n\n<<<SA检测文档>>>\nhttp://agroup.baidu.com/share/md/e61f690e3f954564b204ac2fedb0927d"
	r.Summary = strings.Join(res, "\n\n")
}

func Analysis(r *Recorder) (string, error) {
	return utils.ExecScript(scriptAnalysis, map[bool]string{false: "N", true: "Y"}[r.GoMod], r.GoPath)
}

func readLines(filename string) ([]string, error) {
	f, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	b, err := io.ReadAll(f)
	if err != nil {
		return nil, err
	}

	res := []string{}
	for _, line := range strings.Split(string(b), "\n") {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		res = append(res, line)
	}
	return res, nil
}

func gatherOne(filename string) (map[string]map[string][]*Diagnostic, error) {
	f, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	b, err := io.ReadAll(f)
	if err != nil {
		return nil, err
	}

	res := map[string]map[string][]*Diagnostic{}
	err = json.Unmarshal(b, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func gatherEasyOutput() ([]*EasyOuputPerFile, error) {
	arr := []*Diagnostic{}

	filenames, err := filepath.Glob("*.easy.json")
	if err != nil {
		return nil, err
	}

	for _, filename := range filenames {
		fileNameSplits := strings.Split(filename, ".")
		if len(fileNameSplits) != 0 {
			ruleName := fileNameSplits[0]
			if _, ok := forbidEasyRule[ruleName]; ok { // 过滤不展示的规则
				continue
			}
		}

		m, err := gatherOne(filename)
		if err != nil {
			continue // 某一个规则结果读取不到，应不妨碍其他规则产出
		}

		for pack := range m {
			for analyzer := range m[pack] {
				for _, d := range m[pack][analyzer] {
					err = d.cook(pack, analyzer)
					if err != nil {
						return nil, err
					}
					arr = append(arr, d)
				}
			}
		}
	}

	sort.Slice(arr, func(i, j int) bool {
		return arr[i].compare(arr[j]) < 0
	})

	res := []*EasyOuputPerFile{}
	for _, d := range arr {
		if len(res) == 0 || d.Filename != res[len(res)-1].Filename {
			res = append(res, &EasyOuputPerFile{Filename: d.Filename, Diagnostics: []*Diagnostic{}})
		}
		res[len(res)-1].add(d)
	}

	return res, nil
}

func gatherSAOutput() ([]*SAOutputPerFile, error) {
	arr := []*Diagnostic{}

	filenames, err := filepath.Glob("*.sa.json")
	if err != nil {
		return nil, err
	}

	for _, filename := range filenames {
		fileNameSplits := strings.Split(filename, ".")
		if len(fileNameSplits) != 0 {
			ruleName := fileNameSplits[0]
			if _, ok := forbidSARule[ruleName]; ok { // 过滤不展示的规则
				continue
			}
		}

		m, err := gatherOne(filename)
		if err != nil {
			continue // 某一个规则结果读取不到，应不妨碍其他规则产出
		}

		for pack := range m {
			for analyzer := range m[pack] {
				for _, d := range m[pack][analyzer] {
					err = d.cook(pack, analyzer)
					if err != nil {
						return nil, err
					}
					arr = append(arr, d)
				}
			}
		}
	}

	sort.Slice(arr, func(i, j int) bool {
		return arr[i].compare(arr[j]) < 0
	})

	res := []*SAOutputPerFile{}
	for _, d := range arr {
		if len(res) == 0 || d.Filename != res[len(res)-1].Filename {
			res = append(res, &SAOutputPerFile{Filename: d.Filename, Diagnostics: []*Diagnostic{}})
		}
		res[len(res)-1].add(d)
	}
	return res, nil
}

func Run(runConfig *RunConfig) (r *Recorder) {
	r = &Recorder{}

	defer func() {
		if x := recover(); x != nil {
			s := fmt.Sprintf("%#v\n%s\n", x, string(debug.Stack()))
			log.Printf("%s", s)

			if err, ok := x.(error); ok {
				r.Err = err
			} else {
				r.Err = errors.New("unexpected")
				r.Output = s
			}
		}
	}()

	if err := r.Init(); err != nil {
		utils.SendWarnMessageToHiForDailyMessage(err.Error())
		fmt.Printf("recorder Init() failed, err: %v\n", err)
		return
	}

	if err := moveBinFilesToCurDir(runConfig); err != nil {
		utils.SendWarnMessageToHiForDailyMessage(err.Error())
		fmt.Printf("moveBinFilesToCurDir failed, err: %v\n", err)
		return
	}

	// easy记录代码行数，该逻辑需要放在ignored策略之前
	r.IsEasyProject = IsEasyProject()

	// 设置代码库路径等
	r.Output, r.Err = ctx.initialize()
	if r.Err != nil {
		fmt.Printf("ctx.initialize() failed, output: %s, err: %v", r.Output, r.Err)
		return
	}

	if !ctx.goMod {
		defer os.RemoveAll(ctx.goPath)
	}

	r.Title = fmt.Sprintf("%s - %s", ctx.mod, ctx.commitID)

	r.GoPath, r.GoMod = ctx.goPath, ctx.goMod

	t := time.Now()

	// 恢复ignored文件
	defer func() {
		if output, err := recoverIgnoredFileInScript(); err != nil {
			fmt.Printf("recover ignored file failed. output: %s, err: %s\n", output, err)
		}
	}()

	// 下载依赖 & ignored
	output1 := ""
	output1, r.Err = Analysis(r)
	if r.Err != nil {
		msg := fmt.Sprintf("analysis(r) failed, output: %s\nerr: %v\n", output1, r.Err)
		fmt.Println(msg)
		utils.SendWarnMessageToHiForDailyMessage(msg)
		r.Output = output1
		return
	}

	if r.IsEasyProject {
		utils.SendWarnMessageToHiForDailyMessage("this is a easy project")
		easyOutput, err := execEasyRules(r, runConfig)
		if err != nil {
			msg := fmt.Sprintf("存在一个easy规范执行失败\n output: %s\nerr:%s", easyOutput, err.Error())
			// 不影响下面 gatherEasyOutput 函数的执行，此处不用 return
			utils.SendWarnMessageToHiForDailyMessage(msg)
		}

		r.EasyOutput, r.EasyErr = gatherEasyOutput()
	}

	if runConfig.RegularMode {
		output, err := execErrCheckRuleForRegularMode(r)
		if err != nil {
			e, ok := err.(*exec.ExitError)
			if !ok {
				msg := fmt.Sprintf("在每周定期跑模式中，errcheck执行失败执行失败, err is not exec.ExitError. \n output: %s\nerr:%s", output, err.Error())
				fmt.Println(msg)
			}

			if e.ExitCode() != 0 && e.ExitCode() != 1 {
				// 当errcheck检测有结果时，将会走到这个分支, 这个分支不能退出
				msg := fmt.Sprintf("在每周定期跑模式中，errcheck执行失败执行失败\n output: %s\nerr:%s, exitcode: %d", output, err.Error(), e.ExitCode())
				fmt.Println(msg)
				utils.SendWarnMessageToHiForDailyMessage(msg)
			}
			// 这里不能退出，errcheck的检测和自研规则的检测是并列的，为避免结果少于预期，此处不应提前退出
		}
	}

	if runConfig.SingleMode {
		fmt.Printf("[in single mode] [rule nmame: %s]\n", runConfig.SingleModeRuleName)
		output, err := execSingleRuleForSingleMode(r, runConfig.SingleModeRuleName)
		if err != nil {
			e, ok := err.(*exec.ExitError)
			if !ok {
				msg := fmt.Sprintf("在每周定期跑模式中，errcheck执行失败执行失败, err is not exec.ExitError. \n output: %s\nerr:%s", output, err.Error())
				fmt.Println(msg)
				return
			}

			if e.ExitCode() != 0 && e.ExitCode() != 1 {
				// 当errcheck检测有结果时，将会走到这个分支, 这个分支不能退出
				msg := fmt.Sprintf("在每周但规则模式中模式中，errcheck执行失败执行失败\n output: %s\nerr:%s, exitcode: %d", output, err.Error(), e.ExitCode())
				fmt.Println(msg)
				utils.SendWarnMessageToHiForDailyMessage(msg)
			}
			// 这里不能退出，errcheck的检测和自研规则的检测是并列的，为避免结果少于预期，此处不应提前退出
		}
		return
	}

	output2 := ""
	output2, r.Err = execSARules(r)
	if r.Err != nil {
		// 执行出错
		msg := fmt.Sprintf("execSARules failed, output: %s\nerr: %v", output2, r.Err)
		fmt.Println(msg)
		utils.SendWarnMessageToHiForDailyMessage(msg)
		r.Output = output1 + "\n" + output2
		return
	}

	r.Duration = time.Since(t)

	r.Ignored, r.Err = readLines("ignored.txt")
	if r.Err != nil {
		msg := fmt.Sprintf("readlines ignored.txt failed, err: %v", r.Err)
		fmt.Println(msg)
		utils.SendWarnMessageToHiForDailyMessage(msg)
		return
	}

	r.SAOutput, r.Err = gatherSAOutput()
	if r.Err != nil {
		msg := fmt.Sprintf("gatherSAoutput failed, err: %v", r.Err)
		fmt.Println(msg)
		utils.SendWarnMessageToHiForDailyMessage(msg)
	}

	// 上报部分规则的检测结果到数据库
	if err := r.ReportRulesToDB(); err != nil {
		msg := fmt.Sprintf("report rules to db failed, err: %v", err)
		fmt.Println(msg)
		utils.SendWarnMessageToHiForDailyMessage(msg)
	}

	return
}

func recoverIgnoredFileInScript() (string, error) {
	const script = `
		while read -r name
		do
		name_with_ignored=$name".ignored"
		if [ -f $name_with_ignored ]; then
			mv $name_with_ignored $name
		fi
		done < ignored.txt

		mv ignored.txt ignored.txt-backup
	`

	return utils.ExecScript(script)
}

func IsEasyProject() bool {
	fileSet := token.NewFileSet()
	f, err := parser.ParseFile(fileSet, "main.go", nil, 0)
	if err != nil {
		return false
	}

	var hasEasyStart bool
	var hasTangramMode bool
	ast.Inspect(f, func(node ast.Node) bool {
		// 找到 EasyStart 函数就不再调 oundEasyStart
		if !hasEasyStart && foundEasyStart(node) {
			hasEasyStart = true
		}
		// 找到 TangramMode 函数就不再调foundEasyStart
		if !hasTangramMode && foundEasyModule(node) {
			hasTangramMode = true
		}

		return true
	})

	return hasEasyStart && hasTangramMode
}

func foundEasyStart(node ast.Node) bool {
	callExpr, ok := node.(*ast.CallExpr)
	if !ok {
		return false
	}
	selectorExpr, ok := callExpr.Fun.(*ast.SelectorExpr)
	if !ok {
		return false
	}
	x, ok := selectorExpr.X.(*ast.Ident)
	if !ok {
		return false
	}
	sel := selectorExpr.Sel
	if x.Name == "easy" && sel.Name == "Start" {
		return true
	}

	return false
}

func foundEasyModule(node ast.Node) bool {
	callExpr, ok := node.(*ast.CallExpr)
	if !ok {
		return false
	}
	selectorExpr, ok := callExpr.Fun.(*ast.SelectorExpr)
	if !ok {
		return false
	}
	x, ok := selectorExpr.X.(*ast.Ident)
	if !ok {
		return false
	}
	sel := selectorExpr.Sel
	if x.Name == "tangram_module" && sel.Name == "NewModule" {
		return true
	}
	return false
}

type EasyData struct {
	UserName string
	RepoName string
	RuleList []string

	Output []*EasyOuputPerFile
}

func NewEasyData() (*EasyData, error) {
	arr := []*Diagnostic{}

	filenames, err := filepath.Glob("*.easy.json")
	if err != nil {
		return nil, err
	}

	ruleList := []string{}
	for _, filename := range filenames {
		m, err := gatherOne(filename)
		if err != nil {
			continue // 某一个规则结果读取不到，应不妨碍其他规则产出
		}

		ruleName := strings.TrimSuffix(filename, ".easy.json")
		ruleList = append(ruleList, ruleName)

		for pack := range m {
			for analyzer := range m[pack] {
				for _, d := range m[pack][analyzer] {
					err = d.cook(pack, analyzer)
					if err != nil {
						return nil, err
					}
					arr = append(arr, d)
				}
			}
		}
	}

	sort.Slice(arr, func(i, j int) bool {
		return arr[i].compare(arr[j]) < 0
	})

	easyOutputs := []*EasyOuputPerFile{}
	for _, d := range arr {
		if len(easyOutputs) == 0 || d.Filename != easyOutputs[len(easyOutputs)-1].Filename {
			easyOutputs = append(easyOutputs, &EasyOuputPerFile{Filename: d.Filename, Diagnostics: []*Diagnostic{}})
		}
		easyOutputs[len(easyOutputs)-1].add(d)
	}

	repoName, err := utils.GetProjectName()
	if err != nil {
		repoName = fmt.Sprintf("get repo name err, error: %v", err)
	}

	user, err := utils.GetCommitUserName()
	if err != nil {
		user = fmt.Sprintf("get commit user name err, error: %v", user)
	}

	res := &EasyData{}
	res.Output = easyOutputs
	res.UserName = user
	res.RepoName = repoName
	res.RuleList = ruleList

	return res, nil
}

func isFileExist(fileName string) bool {
	info, err := os.Stat(fileName)
	if err != nil {
		return false
	}

	if info.IsDir() {
		return false
	}

	return true
}
