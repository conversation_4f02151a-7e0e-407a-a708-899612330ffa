package report

import (
	"fmt"
	"path/filepath"
	"strings"
	"sync"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/utils"
)

func moveBinFilesToCurDir(runConfig *RunConfig) error {
	tarAddr := runConfig.TarAddr
	if err := validateTarAddr(tarAddr); err != nil {
		return err
	}

	output, err := utils.ExecScript(scriptMoveBinFile, tarAddr)
	if err != nil {
		return fmt.Errorf("move bin files to work directory failed, output is %s, err is %w", output, err)
	}

	if runConfig.RegularMode {
		// 该模式下需要拷贝 golangci-lint bin 到代码库所在目录
		golangCILintBinFile := filepath.Join(runConfig.BinDir, "nd-errcheck")
		if ok := isFileExist(golangCILintBinFile); !ok {
			return fmt.Errorf("the binfile is not exist, binfile is  %s", golangCILintBinFile)
		}

		output, err := utils.ExecScript(scriptMoveBinFileForRegularMode, golangCILintBinFile)
		if err != nil {
			return fmt.Errorf("move binfile errcheck to work directory failed, output is %s, err is %w", output, err)
		}
	}

	if runConfig.SingleMode {
		binFile := filepath.Join(runConfig.BinDir, runConfig.SingleModeRuleName)
		output, err := utils.ExecScript(scriptMoveBinFileForRegularMode, binFile)
		if err != nil {
			return fmt.Errorf("move binfile errcheck to work directory failed, output is %s, err is %w", output, err)
		}
	}

	return nil
}

func validateTarAddr(tarAddr string) error {
	if !strings.HasSuffix(tarAddr, "go-static-analysis.tar.gz") {
		return fmt.Errorf("the tar addr not have suffix go-static-analysis.tar.gz, and addr is: %s", tarAddr)
	}
	return nil
}

func MeasureEasyCode() {
	output, err := utils.ExecScript(scriptEasyCodeMetric) //内部会把错误产出到文件中
	if err != nil {
		fmt.Printf("measure easy code failed, output is: %s, err is: %s\n", output, err.Error())
	}
}

func execEasyRules(r *Recorder, runConfig *RunConfig) (string, error) {
	var wg sync.WaitGroup
	var output string
	var err error
	for i := range easyRuleNames {
		wg.Add(1)
		rule := easyRuleNames[i]
		go func() {
			defer wg.Done()
			output, err = execEasyRule(r, rule, runConfig) // 只收集其中一个规则执行出错即可
		}()
	}
	wg.Wait()

	return output, err
}

func execEasyRule(r *Recorder, rule string, runConfig *RunConfig) (string, error) {
	switch rule {
	case APICollection:
		exec := scriptRunAPICollection
		return utils.ExecScript(exec)
	case APICallGraph:
		return "", nil
		// 待对方接口上线后再跑这个
		// exec := scriptRunAPICallgraph
		//return utils.ExecScript(exec)
	default:
		realExec := preEasyCommand + "run " + rule
		easyEnableDiffStr := fmt.Sprintf("%v", runConfig.EasyEnableDiff)
		return utils.ExecScript(realExec, r.GoPath, easyEnableDiffStr)
	}
}

func execSARules(recorder *Recorder) (string, error) {
	var wg sync.WaitGroup

	var output string
	var err error
	for i := range saRuleNames {
		wg.Add(1)
		rule := saRuleNames[i]
		go func() {
			defer wg.Done()
			output, err = execSARule(recorder, preSACommand, rule) // 只收集其中一个规则执行出错即可
		}()
	}
	wg.Wait()

	return output, err
}

func execSARule(r *Recorder, pre string, rule string) (string, error) {
	var realExec string
	exec := ""
	switch rule {
	case "ifnil":
		exec =
			`chmod +x ifnil
			 ./ifnil > ifnil.sa.json`
	case "unfreed":
		exec =
			`chmod +x unfreed
			CGO_ENABLED=0 ./unfreed > unfreed.sa.json`
	default:
		exec = "run " + rule
	}
	realExec = pre + exec
	return utils.ExecScript(realExec, r.GoPath)
}

func execErrCheckRuleForRegularMode(r *Recorder) (string, error) {
	return utils.ExecScript(scriptRunErrcheckRuleForRegularMode, r.GoPath)
}

func execSingleRuleForSingleMode(r *Recorder, rule string) (string, error) {
	return utils.ExecScript(scriptRunkRuleSingleMode, r.GoPath, rule)
}
