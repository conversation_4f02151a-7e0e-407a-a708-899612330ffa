package report

import (
	"bufio"
	"bytes"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/pkg/easyutil"
)

const (
	StatServerAddress = "***********:80"
)

// 把检测结果落库到 db
func (r *Recorder) ReportRulesToDB() error {
	results := r.CollectReportRules()
	for _, result := range results {
		if err := r.SendStatData(result); err != nil {
			return err
		}
	}

	return nil
}

func (r *Recorder) SendStatData(result *ReportRuleResultItem) error {
	url := fmt.Sprintf("http://%s/rule/nilaway?method=saveList", StatServerAddress)

	resultByte, err := json.Marshal(result)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", url, strings.NewReader(string(resultByte)))
	if err != nil {
		return err
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	resBodyByte, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	fmt.Println("resBodyByte, body:", string(resBodyByte))

	if resp.StatusCode != 200 {
		return errors.New("http status code is not 200")
	}
	return nil

}

func (r *Recorder) CollectReportRules() []*ReportRuleResultItem {
	results := make([]*ReportRuleResultItem, 0)
	for rule := range saReportRule {
		item, err := r.CollectReportRule(rule)
		if err != nil {
			fmt.Printf("collect report rule failed, rule: %s, err: %v", rule, err)
			continue
		}
		results = append(results, item)
	}

	return results
}

func (r *Recorder) CollectReportRule(ruleName string) (*ReportRuleResultItem, error) {
	if ruleName == RuleNilAway {
		return r.CollectNilAwayResult()
	}

	return nil, fmt.Errorf("not support rule, rule name is %s", ruleName)
}

func (s *Recorder) initCommitIDsAndCommitUser() error {
	// get last long commit id for git command
	getCommitIDCmd := `git rev-parse HEAD`
	commitID, err := easyutil.ExecScript(getCommitIDCmd)
	if err != nil {
		return fmt.Errorf("get commit id failed, err is %w, output is  %s", err, commitID)
	}

	getCommitUserCmd := `git log -1 --format="%an"`
	commitUser, err := easyutil.ExecScript(getCommitUserCmd)
	if err != nil {
		return fmt.Errorf("get last commit user failed, err is %w, output is %s", err, commitUser)
	}

	getCommitMsgCmd := `git log -1 --pretty=format:'%s'`
	commitMsg, err := easyutil.ExecScript(getCommitMsgCmd)
	if err != nil {
		return fmt.Errorf("get last commit message failed, err is %w, output is %s", err, commitMsg)
	}

	s.CommitID = commitID
	s.CommitUser = commitUser
	return nil
}

func (r *Recorder) CollectNilAwayResult() (*ReportRuleResultItem, error) {
	fileName := "nilaway.sa.json"
	fileByte, err := os.ReadFile(fileName)
	if err != nil {
		return nil, err
	}

	var resultMap LintResult
	if err := json.Unmarshal(fileByte, &resultMap); err != nil {
		return nil, err
	}

	res := &ReportRuleResultItem{}

	res.RepoName = r.ProjectName
	str := fmt.Sprintf("%s%d", r.ProjectName, time.Now().UnixNano())
	res.BatchUID = fmt.Sprintf("%v", SHA512Hash(str))
	res.ScanTime = time.Now().Format("2006-01-02 15:04:05")
	res.CommitUser = r.CommitUser

	commitURL := fmt.Sprintf("https://console.cloud.baidu-int.com/devops/icode/repos/%s/commits/%s", r.ProjectName, r.CommitID)
	res.CommitURL = commitURL

	for _, pkgPathMap := range resultMap {
		for _, slice := range pkgPathMap {
			for _, item := range slice {
				lintResultInfo := item.FormatLintResultInfo(r.ProjectName)
				if lintResultInfo == nil {
					fmt.Printf("lintResultInfo is nil, item: %v\n", item)
					continue
				}

				res.Issues = append(res.Issues, lintResultInfo)

			}
		}
	}

	return res, nil
}

type ReportRuleResultItem struct {
	RepoName   string            `json:"repo_name"`
	BatchUID   string            `json:"batch_uid"`
	CommitURL  string            `json:"commit_url"`
	CommitUser string            `json:"commit_user"`
	ScanTime   string            `json:"scan_time"`
	Issues     []*LintResultInfo `json:"issues"`
}

type LintResult map[string]map[string][]LintResultItem

type LintResultItem struct {
	Posn    string `json:"posn"`
	Message string `json:"message"`
}

type LintResultInfo struct {
	RelFilePath string `json:"rel_file_path" validate:""`
	Fingerprint string `json:"fingerprint" validate:""`
	Message     string `json:"message" validate:""`
	Line        int    `json:"line" validate:""`
	Col         int    `json:"col" validate:""`
	CodeContext string `json:"code_context" validate:""`
	Username    string `json:"username" validate:""`
}

func (item *LintResultItem) FormatLintResultInfo(projectName string) *LintResultInfo {
	slice := strings.Split(item.Posn, ":")
	if len(slice) != 3 {
		return nil
	}

	absPath := slice[0]
	line, _ := strconv.Atoi(slice[1])
	col, _ := strconv.Atoi(slice[2])

	relPath, ok := extractSubPath(absPath, projectName)
	if !ok {
		relPath = absPath
	}

	res := &LintResultInfo{
		RelFilePath: relPath,
		Line:        line,
		Col:         col,
		Message:     item.Message,
	}

	fileByte, err := os.ReadFile(absPath)
	if err != nil {
		return nil
	}

	fielSlice := strings.Split(string(fileByte), "\n")

	var codeContextSlice []string
	if line-1 >= 0 && line-1 < len(fielSlice) {
		codeContextSlice = append(codeContextSlice, fielSlice[line-1])
	}
	if line >= 0 && line < len(fielSlice) {
		codeContextSlice = append(codeContextSlice, fielSlice[line])
	}
	if line+1 >= 0 && line+1 < len(fielSlice) {
		codeContextSlice = append(codeContextSlice, fielSlice[line+1])
	}

	res.CodeContext = strings.TrimSpace(strings.Join(codeContextSlice, "\n"))

	fgStr := fmt.Sprintf("%s:%s", res.RelFilePath, res.CodeContext)
	fingerPrint := fmt.Sprintf("%v", SHA512Hash((fgStr)))
	res.Fingerprint = fingerPrint
	username, err := getGitBlameAuthor(relPath, line)
	if err != nil {
		res.Username = "unknown"
	} else {
		res.Username = username
	}

	return res

}

func SHA512Hash(text string) string {
	hash := sha512.Sum512([]byte(text))
	res := hex.EncodeToString(hash[:])
	if len(res) > 36 {
		res = res[:36]
	}
	return res
}

// extractSubPath 从 fullPath 中提取出 anchorPath 后面的部分（去掉 anchorPath 及其前缀）
// 返回提取后的子路径，如果 anchorPath 不在 fullPath 中则返回空字符串和 false
func extractSubPath(fullPath, anchorPath string) (string, bool) {
	index := strings.Index(fullPath, anchorPath)
	if index == -1 {
		return "", false
	}
	// 跳过 anchorPath 和后面的 '/'
	start := index + len(anchorPath)
	if start < len(fullPath) && fullPath[start] == '/' {
		start++
	}
	return fullPath[start:], true
}

// getGitBlameAuthor 获取某个文件某一行的作者信息
func getGitBlameAuthor(filePath string, lineNumber int) (string, error) {
	// 构造命令：git blame -L <line>,<line> <file>
	cmd := exec.Command("git", "blame", "-L", fmt.Sprintf("%d,%d", lineNumber, lineNumber), filePath)

	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("git blame failed: %v, %s", err, stderr.String())
	}

	// 输出示例：
	// a1b2c3d4 (Author Name 2024-05-10 12:34:56 +0800 42)     fmt.Println("Hello, world!")
	line := out.String()
	scanner := bufio.NewScanner(strings.NewReader(line))
	if scanner.Scan() {
		text := scanner.Text()
		// 提取括号中的内容
		start := strings.Index(text, "(")
		end := strings.Index(text, ")")
		if start != -1 && end != -1 && end > start {
			authorInfo := strings.TrimSpace(text[start+1 : end])
			authorParts := strings.SplitN(authorInfo, " ", 2)
			return authorParts[0], nil // 返回作者名字
		}
	}

	return "", fmt.Errorf("failed to parse git blame output")
}
