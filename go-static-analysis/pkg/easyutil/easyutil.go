package easyutil

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

type EasyCode struct {
	Code        string `json:"code"`
	FileName    string `json:"fileName"`
	IsHotReload int    `json:"isHotReload"`
	Key         string `json:"key"`
	Path        string `json:"path"`
	Rewrite     bool   `json:"rewrite"`
	Type        int    `json:"type"`
}

func GetProjectName() (string, error) {
	output, err := ExecScript(`git remote -v | head -1 | awk {'print $2'}`)
	if err != nil {
		return "", err
	}
	i := strings.Index(output, "8235/baidu")
	if i == -1 {
		return "", errors.New("project name not found in output dir")
	}

	return output[i+5:], nil
}

func GetRemoteMsg(projectName string) (map[string]EasyCode, []EasyCode, error) {
	url := fmt.Sprintf("http://************:8089/code/loadCode?icode=%s", projectName)

	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	body := make(map[string]string)
	bodyJSON, err := json.Marshal(body)
	if err != nil {
		return nil, nil, err
	}

	var resp httpclient.HttpResponse
	for i := 0; i < 3; i++ {
		resp, err = httpclient.Post(url, header, 1000, 30000, string(bodyJSON))
		if err == nil {
			break
		}
	}

	if err != nil {
		return nil, nil, err
	}

	respMap := make(map[string]interface{})
	err = json.Unmarshal(resp.Body, &respMap)
	if err != nil {
		return nil, nil, err
	}

	data, ok := respMap["data"].(map[string]interface{})
	if !ok {
		return nil, nil, errors.New("data is not map[string]interface{}")
	}

	res := make(map[string]EasyCode)
	wfCodes := make([]EasyCode, 0)
	for typ, valueSlice := range data {
		v, ok := valueSlice.([]interface{})
		if !ok {
			continue
		}

		for _, item := range v {
			ec := EasyCode{}
			if err := mapstructure.Decode(item, &ec); err != nil {
				continue
			}

			// 第一版 wf 数据统计,Web版，非IDE版
			if typ == "wf" {
				wfCodes = append(wfCodes, ec)
			}

			key := filepath.Join(ec.Path, ec.FileName)
			res[key] = ec
		}
	}
	return res, wfCodes, nil
}

type EasyIdeWFCodeInfo struct {
	ID       int    `json:"id"`
	GID      int    `json:"gid"`
	Username string `json:"username"`
	FilePath string `json:"file_path"`
	FuncURI  string `json:"func_uri"`
	CodeID   string `json:"code_id"`
	DSL      string `json:"dsl"`
	Code     string `json:"code"`
}

type IdeWFCodeInfoMap map[string]EasyIdeWFCodeInfo

func GetRemoteWFIdeCodeMap(projectName string, userName string) (IdeWFCodeInfoMap, error) {
	url := fmt.Sprintf("http://************:8089/vscode/stat/code/getList?gname=%s&username=%s", projectName, userName)
	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	body := make(map[string]string)
	bodyJSON, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	var resp httpclient.HttpResponse
	for i := 0; i < 3; i++ {
		resp, err = httpclient.Post(url, header, 1000, 30000, string(bodyJSON))
		if err == nil {
			break
		}
	}

	if err != nil {
		return nil, err
	}

	respMap := make(map[string]interface{})
	err = json.Unmarshal(resp.Body, &respMap)
	if err != nil {
		return nil, err
	}

	data, ok := respMap["data"].([]interface{})
	if !ok {
		return nil, errors.New("data is not map[string]interface{}")
	}

	resMap := make(IdeWFCodeInfoMap)
	for _, item := range data {
		ec := EasyIdeWFCodeInfo{}
		itemByte, ok := item.([]byte)
		if !ok {
			continue
		}
		if err := json.Unmarshal((itemByte), &ec); err != nil {
			continue
		}

		resMap[ec.FilePath] = ec
	}

	return resMap, nil
}

func ExecScript(script string, args ...string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "/bin/bash", append([]string{"-s", "-"}, args...)...)
	wc, err := cmd.StdinPipe()
	if err != nil {
		return "", err
	}

	go func() {
		defer wc.Close()
		fmt.Fprintf(wc, "%s", script)
	}()

	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}
