#!/bin/bash
# 该脚本用于在 branch 流水线进行代码统计
# 前提：
# 1. 机器：bjyz-ps-beehive-agent147396.bjyz.baidu.com
# 2. 把该文件放到 /home/<USER>/easy-statistics 路径下
# 3. 把 cmd/easy/codemetric 重命名为 easy-statistics, 并放到 /home/<USER>/easy-statistics 路径下
# 4. 拷贝 /home/<USER>/gocheck 的 download_module.py、checkoutGitbranch.py到 /home/<USER>/easy-statistics 路径下

set +e
source /etc/profile


go version
echo $1
modulename=$1
#分支名
echo $2
branchname=$2
lastcommitid=$3
# 流水线build id
commit_url=$4

# 变量定义
EASY_STATISTICS_ROOT_PATH="/home/<USER>/easy-statistics"
EASY_STATISTICS_BIN="$EASY_STATISTICS_ROOT_PATH/easy-statistics"
BIN_NAME="easy-statistics"

WHITE_LIST_APP="baidu/netdisk/nd-go-tickets;baidu/netdisk/nd-org-consult;baidu/netdisk-global/cd-martix-business;baidu/netdisk/easy-go-lib"

# 检查是否为easy模块
function check_is_easy_project() {
    local url="10.138.34.59:8089/code/icode/info?start=1667355342"
    local module_name="$1"

    # 字符串数组，用换行符分隔
    IFS=$';' read -ra array <<< "$WHITE_LIST_APP"
    for word in "${array[@]}"; do
        if  [ "$word" = "$module_name" ]; then
            IS_EASY_PROJECT="false"
            return 
        fi
    done

    echo "module_name: $module_name"
    # 发送HTTP GET请求并将响应存储在变量response中
    response=$(curl -s --location --request GET "$url")
    # 检查HTTP请求是否成功
    if [ $? -ne 0 ]; then
        warn_msg="请求 easy API获取 easy模块列表时出错"
        echo "$warn_msg"
        send_hi_msg "$warn_msg"
        exit 0
    fi

    # 使用jq解析JSON响应
    slice=($(echo "$response" | jq -r '.data[]'))

    for item in "${slice[@]}"; do
        if  [[ "$item" == "$module_name" ]]; then
            IS_EASY_PROJECT="true"
            break 
        fi
    done
}

function send_hi_msg() {
    local send_hi_message="$1"

    curl --location --request GET 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d9d00bea8cbe8b7d6c74d12bda373a70e' \
    --header 'Content-Type: text/plain' \
    --data '{
        "message":{
            "header":{

            },
            "body":[
                {
                    "content":"'"$send_hi_message"'",
                    "type":"TEXT"

                }
            ]
        }
    }'
}

function run_easy_statistics() {
    go version
    echo $1
    local modulename=$1
    #分支名
    echo $2
    local branchname=$2
    # 流水线build id
    #运行目录
    #workspace=$4
    cd $EASY_STATISTICS_ROOT_PATH/newcode
    # 下载module
    module=$(/home/<USER>/.jumbo/bin/python3 $EASY_STATISTICS_ROOT_PATH/download_module.py  $modulename)
    branch=$(/home/<USER>/.jumbo/bin/python3 $EASY_STATISTICS_ROOT_PATH/checkoutGitbranch.py $branchname)

    module_path="$EASY_STATISTICS_ROOT_PATH/newcode/$modulename"
    if [[ -d "$module_path" ]]; then
        module_path_backup="$module_path-backup"
        if [[ -d "$module_path_backup" ]]; then
            rm -rf $module_path_backup
        fi 
        # 做备份 
        mv $module_path $module_path_backup
    fi


    cd $EASY_STATISTICS_ROOT_PATH/newcode
    eval $module  >/dev/null 2>&1

    pwd
    cd  $EASY_STATISTICS_ROOT_PATH/newcode/$1
    echo "cd $EASY_STATISTICS_ROOT_PATH/newcode/$1 111"
    pwd

    # 切换分支  
    cd $EASY_STATISTICS_ROOT_PATH/newcode/$1
    eval $branch  >/dev/null 2>&1
  
  
    if [ ! -f $EASY_STATISTICS_BIN ]
    then
    # 加报警
        warn_msg="The easy stat bin file doesn't exist"
        send_hi_msg "$warn_msg"
        exit 0
    fi

    cd $EASY_STATISTICS_ROOT_PATH/newcode/$1
    pwd

    cp -f $EASY_STATISTICS_BIN $BIN_NAME

    echo "===========================================start====================================================================="
    pwd

    timeout 60s  ./easy-statistics -last_commit_id=$lastcommitid -commit_url=$commit_url
    stat_result=$?
    if [ $stat_result -eq 124 ] 
    then 
        echo "easy stat timeout"
        send_hi_msg "[easy-stat] easy stat timeout. module is $1"
        exit 0
    fi 

    echo "easy stat success"
    send_hi_msg "[easy-stat] easy stat success, module is $1, branch is $2, lastcommitid is $lastcommitid"
    exit 0
}


IS_EASY_PROJECT="false"
# 检查好是否为 easy 模块
check_is_easy_project "$1"
echo "is easy project: $IS_EASY_PROJECT"

# 如果不为 easy 模块则退出
if [[ "$IS_EASY_PROJECT" != "true" ]]; then 
    warn_msg="$1 is not easy project"
    echo "$warn_msg"
    send_hi_msg "$warn_msg"
    exit 0 
fi

# 如果为 easy 模块则跑数据统计
run_easy_statistics $1 $2 $3 $4
exit 0



