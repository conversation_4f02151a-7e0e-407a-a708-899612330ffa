#!/bin/bash
#rm -rf  /home/<USER>/gocheck/newcode/baidu
# 切换代码分支
#模块名
#

#使用方式:
# 把相应的 tar.gz包 rz 到 /home/<USER>/gocheck/test/release, 把 report 和 golangci-lint rz 到 /home/<USER>/gocheck/test 路径下
# 1. cd /home/<USER>/gocheck
# 2. nohup /home/<USER>/.jumbo/bin/python3 /home/<USER>/gocheck/run_rule_in_regularly.py > run_rule_in_regularly.log 2>&1 & 
# 3. 等待执行完毕后，到/home/<USER>/gocheck/newnewnewcode下grep查看结果
source /etc/profile
go version
echo $1
modulename=$1
#分支名
echo $2
branchname=$2
# 流水线build id
echo $3
build_id=$3
cd /home/<USER>/gocheck/newsavecode

# 下载module
module=$(/home/<USER>/.jumbo/bin/python3 /home/<USER>/gocheck/download_module.py  $modulename)
branch=$(/home/<USER>/.jumbo/bin/python3 /home/<USER>/gocheck/checkoutGitbranch.py $branchname)

cd /home/<USER>/gocheck/newnewnewcode/
rm -rf  /home/<USER>/gocheck/newnewnewcode/$1
echo "git pull module"

eval $module
git pull
git reset --merge
echo "git checkout branch"
cd /home/<USER>/gocheck/newnewnewcode/$1
eval $branch
# 切换分支

rm -rf /home/<USER>/gocheck/newnewnewcode/$1/report
cp /home/<USER>/gocheck/test/report report --regular=true

timeout 120s ./report
if [ $? -eq 0 ]
then
    echo "00000000000000000000000"
else
    echo "11111111111111111111111"
fi
echo  "final end"