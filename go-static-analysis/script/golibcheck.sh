#!/bin/bash

# 用于对所有easy模块执行单个easy静态规则
# 变量定义
EASY_STATISTICS_ROOT_PATH="/home/<USER>/gocheck/newcode"
EASY_STATISTICS_BIN="$EASY_STATISTICS_ROOT_PATH/report"
BIN_NAME="report"

WHITE_LIST_APP="baidu/netdisk/nd-go-tickets;baidu/netdisk/nd-org-consult;baidu/netdisk-global/cd-martix-business;baidu/netdisk/easy-go-lib"
IS_EASY_PROJECT="false"


# 检查是否为easy模块
function check_is_easy_project() {
    local url="10.138.34.59:8089/code/icode/info?start=1667355342"
    local module_name="$1"

    # 字符串数组，用换行符分隔
    IFS=$';' read -ra array <<< "$WHITE_LIST_APP"
    for word in "${array[@]}"; do
        if  [ "$word" = "$module_name" ]; then
            IS_EASY_PROJECT="false"
            return
        fi
    done

    echo "module_name: $module_name"
    # 发送HTTP GET请求并将响应存储在变量response中
    response=$(curl -s --location --request GET "$url")
    # 检查HTTP请求是否成功
    if [ $? -ne 0 ]; then
        warn_msg="请求 easy API获取 easy模块列表时出错"
        echo "$warn_msg"
        send_hi_msg "$warn_msg"
        return 
    fi

    # 使用jq解析JSON响应
    slice=($(echo "$response" | jq -r '.data[]'))

    for item in "${slice[@]}"; do
        if  [[ "$item" == "$module_name" ]]; then
            IS_EASY_PROJECT="true"
            break
        fi
    done
}

function send_hi_msg() {
    local send_hi_message="$1"

    curl --location --request GET 'http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d9d00bea8cbe8b7d6c74d12bda373a70e' \
    --header 'Content-Type: text/plain' \
    --data '{
        "message":{
            "header":{

            },
            "body":[
                {
                    "content":"'"$send_hi_message"'",
                    "type":"TEXT"

                }
            ]
        }
    }'
}

function run_singlerule_check() {
    if [ ! -f $EASY_STATISTICS_BIN ]
    then
    # 加报警
        warn_msg="The easy stat bin file doesn't exist"
        send_hi_msg "$warn_msg"
        return
    fi

    echo "run_singlerule_check pwd: $pwd"
    reportBin="$EASY_STATISTICS_ROOT_PATH/$1/report"
    if [ -f "$reportBin" ]; then
        echo "report bin file exist, delete it"
    fi 

    cp -f $EASY_STATISTICS_BIN $BIN_NAME

    echo "===========================================start====================================================================="
    pwd

    if [ "$pwd" = "/home/<USER>/gocheck/newcode" ]; then
        echo "pwd is /home/<USER>/gocheck/newcode"
        return 
    fi 

    timeout 60s  ./report -single=true -rulename="golibcheck" 
    stat_result=$?
    if [ $stat_result -eq 124 ]
    then
        echo "run_singlerule_check timeout"
        send_hi_msg "[run_singlerule_check] timeout. module is $1"
        return 
    fi

    echo "run_singlerule_check success"
    send_hi_msg "[run_singlerule_check] success, module is $1, branch is $2?"
    return 
}


function cd_easy_project_and_run () {
    cur_dir="/home/<USER>/gocheck/newcode"
    local url="10.138.34.59:8089/code/icode/info?start=1667355342"

    # 发送HTTP GET请求并将响应存储在变量response中
    response=$(curl -s --location --request GET "$url")
    # 检查HTTP请求是否成功
    if [ $? -ne 0 ]; then
        warn_msg="请求 easy API获取 easy模块列表时出错"
        echo "$warn_msg"
        send_hi_msg "$warn_msg"
        return 
    fi

    # 使用jq解析JSON响应
    slice=($(echo "$response" | jq -r '.data[]'))

    for item in "${slice[@]}"; do
        check_is_easy_project "$item"
        echo "check_is_easy_project: $item, is easy project: $IS_EASY_PROJECT"
        if  [[ "$IS_EASY_PROJECT" == "true" ]]; then
            IS_EASY_PROJECT="false"
            abs_dir="$cur_dir/$item"
            echo "cur_dir: $cur_dir, abs_dir: $abs_dir"
            if [ -d "$abs_dir" ]; then
                cd "$abs_dir"
                run_singlerule_check "$item" 
            fi 

        fi 
    done
    wait 
    echo "all done"

}


cd_easy_project_and_run