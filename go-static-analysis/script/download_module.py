# !/usr/bin/env python3
# 拼接字符串
def deal_git_command(module:str):
    params_module = module
    git_download_module_command = "git clone ssh://<EMAIL>:8235/%s %s && curl -s http://icode.baidu.com/tools/hooks/commit-msg > %s/.git/hooks/commit-msg && chmod u+x %s/.git/hooks/commit-msg && git config -f %s/.git/config user.name gongweiwei && git config -f %s/.git/config user.email <EMAIL>  " %(params_module,params_module,params_module,params_module,params_module,params_module)
    print(git_download_module_command)
    return git_download_module_command

if __name__ == "__main__":
    import sys
    list_argsv = sys.argv
    deal_git_command(list_argsv[-1])