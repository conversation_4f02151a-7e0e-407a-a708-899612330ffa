package util

import (
	"encoding/json"
	"go/token"
	"io/ioutil"
	"os"
	"strings"

	"golang.org/x/tools/go/analysis"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
)

const ReportUrl = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dc291f9237967c82d458ab447c045ce39"

func SendWarnMessageToHi(msg string) {
	headerMap := map[string]string{}
	headerMap["Content-Type"] = "application/json"

	conMaps := []map[string]interface{}{}
	conMap := map[string]interface{}{}
	conMap["type"] = "TEXT"
	conMap["content"] = msg
	conMaps = append(conMaps, conMap)

	bodyMap := map[string]interface{}{}
	bodyMap["body"] = conMaps

	messageMap := map[string]interface{}{}
	messageMap["message"] = bodyMap

	bodyJson, _ := json.Marshal(messageMap)

	for i := 0; i < 3; i++ {
		_, err := httpclient.Post(ReportUrl, headerMap, 1000, 3000, string(bodyJson))
		if err == nil {
			break
		}
	}
}

func LoadConf(filename string, dest interface{}) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}
	defer file.Close()

	b, err := ioutil.ReadAll(file)
	if err != nil {
		return err
	}

	return json.Unmarshal(b, dest)
}

func ToJsonText(v interface{}) string {
	res, err := json.MarshalIndent(v, "", "  ")
	if err != nil {
		return err.Error()
	}
	return string(res)
}

// path 是绝对路径
func IsInWhiteList(path string, whiteList []string) bool {
	for _, repos := range whiteList {
		if isTail := strings.HasSuffix(path, repos); isTail {
			return true
		}
	}
	return false
}

// 前提：pos需要属于pass中的FileSet
func FinePathName(pass *analysis.Pass, pos token.Pos) string {
	p := pass.Fset.Position(pos)
	return p.Filename
}
