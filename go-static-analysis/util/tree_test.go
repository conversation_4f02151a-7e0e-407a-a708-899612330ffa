package util_test

import (
	"reflect"
	"testing"

	"icode.baidu.com/baidu/netdisk/go-static-analysis/util"
)

func TestPostorder(t *testing.T) {
	r := &util.TreeNode{
		Id:            1,
		FuncName:      "GetMsg",
		OriginalParam: "a",
		Param:         util.ParamInfo{},
		Children:      [util.MAX_CHILD]*util.TreeNode{},
	}

	paramInfo := util.ParamInfo{
		ParamName: "aa",
		ParamType: "string",
		ReferCode: "aa := a",
		CodeLine:  10,
	}

	for i := 2; i < 10; i++ {
		r1 := &util.TreeNode{
			Id:            i,
			FuncName:      "GetMsg",
			OriginalParam: "a",
			Param:         paramInfo,
			Children:      [util.MAX_CHILD]*util.TreeNode{},
		}

		r.Insert(r1, 1)
	}

	for i := 10; i < 14; i++ {
		r1 := &util.TreeNode{
			Id:            i,
			FuncName:      "GetMsg",
			OriginalParam: "a",
			Param:         paramInfo,
			Children:      [util.MAX_CHILD]*util.TreeNode{},
		}

		r.Insert(r1, 3)
	}

	for i := 14; i < 16; i++ {
		r1 := &util.TreeNode{
			Id:            i,
			FuncName:      "GetMsg",
			OriginalParam: "a",
			Param:         paramInfo,
			Children:      [util.MAX_CHILD]*util.TreeNode{},
		}

		r.Insert(r1, 12)
	}

	for i := 16; i < 20; i++ {
		r1 := &util.TreeNode{
			Id:            i,
			FuncName:      "GetMsg",
			OriginalParam: "a",
			Param:         paramInfo,
			Children:      [util.MAX_CHILD]*util.TreeNode{},
		}

		r.Insert(r1, 7)
	}

	//后序遍历
	result := r.Postorder()
	sliceArr := []int{2, 10, 11, 14, 15, 12, 13, 3, 4, 5, 6, 16, 17, 18, 19, 7, 8, 9, 1}
	equalBool := reflect.DeepEqual(result, sliceArr)

	if !equalBool {
		t.Errorf("tree postorder error A")
	}

	pTree := util.NewTreeNode()
	result = pTree.Postorder()
	sliceArr = []int{0}

	equalBool = reflect.DeepEqual(result, sliceArr)
	if !equalBool {
		t.Errorf("tree postorder error B")
	}

	pTree = nil
	result = pTree.Postorder()
	sliceArr = []int{}

	equalBool = reflect.DeepEqual(result, sliceArr)
	if !equalBool {
		t.Errorf("tree postorder error C")
	}

	bVal := r.Insert(pTree, 1)
	result = pTree.Postorder()
	if bVal != false {
		t.Errorf("tree postorder error D")
	}

	pTree = &util.TreeNode{
		Id:            1,
		FuncName:      "",
		OriginalParam: "",
		Param:         util.ParamInfo{},
		Children:      [16]*util.TreeNode{},
	}
	
	bVal = r.Insert(pTree, 1)
	if bVal != false {
		t.Errorf("tree postorder error E")
	}
}
