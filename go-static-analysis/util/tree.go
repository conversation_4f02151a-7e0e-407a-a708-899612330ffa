package util

type ParamInfo struct {
	ParamName string
	ParamType string
	ReferCode string
	CodeLine  int
}

type TreeNode struct {
	Id            int
	FuncName      string
	OriginalParam string
	Param         ParamInfo
	Children      [MAX_CHILD]*TreeNode
}

const MAX_CHILD = 16

func NewTreeNode() *TreeNode {
	return &TreeNode{}
}

func (root *TreeNode) Insert(pNode *TreeNode, parentId int) bool {
	//root node
	if pNode == nil || pNode.Id == 1 {
		return false
	}

	//search node
	pNewTree := Search(root, parentId)

	if pNewTree == nil {
		return false
	}

	for i := 0; i < MAX_CHILD; i++ {
		if pNewTree.Children[i] == nil {
			pNewTree.Children[i] = pNode
			return true
		}
	}

	return false
}

func Search(root *TreeNode, id int) *TreeNode {
	if root == nil {
		return nil
	}

	if root.Id == id {
		return root
	}

	for i := 0; i < MAX_CHILD; i++ {
		pNode := Search(root.Children[i], id)
		if pNode != nil {
			return pNode
		}
	}

	return nil
}

func (root *TreeNode) Postorder() []int {
	if root == nil {
		return []int{}
	}

	res := []int{}
	recur(root, &res)

	return res
}

func recur(root *TreeNode, res *[]int) {
	if root == nil {
		return
	}

	for _, c := range root.Children {
		recur(c, res)
	}

	*res = append(*res, root.Id)
}
