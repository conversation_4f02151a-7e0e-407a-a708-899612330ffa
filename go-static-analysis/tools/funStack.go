package main

import (
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"strings"

	"golang.org/x/tools/go/packages"
	"golang.org/x/tools/go/ssa"
	"golang.org/x/tools/go/ssa/ssautil"

	"github.com/bitly/go-simplejson"
)

var fileName = flag.String("name", "", "Must input absolute path of the source file, example: /home/<USER>/icode.baidu.com/baidu/netdisk/pcs-go-pcsapi/action/file/rapidupload.go")
var line = flag.Int("line", 0, "Must input the line number of the function declaration, example: 25")
var target = flag.String("target", "", "Optional input tracked objective function, default: (*icode.baidu.com/baidu/netdisk/pcs-go-lib/ufc.UFCClient).SendRequest")
var begin = flag.Int("begin", 0, "Optional input begin line num to analysis of the function")
var end = flag.Int("end", 0, "Optional input end line num to analysis of the function")

const (
	MAX_DEPTH       = 128
	UFC_FUN_FLAG    = "ufc"
	BAIDU_CODE_FLAG = "icode.baidu.com"
)

type FunStack struct {
	FileName string
	Line     int
	Match    string
	Froms    []string
	Targets  []string
	Begin    int
	End      int
}

var funStepCall map[string]string
var result map[string]bool

func (fs *FunStack) findFuncStepCallStack(curFun, parentFun string, depth int) {
	depth++
	if depth > MAX_DEPTH {
		return
	}

	funcStack := strings.Split(parentFun, " -> ")
	for _, v := range funcStack {
		if v == curFun {
			//fmt.Println("sameFun ", curFun, " parentFun ", parentFun)
			return
		}
	}

	tmp := curFun
	if strings.Contains(curFun, "#") {
		arr := strings.Split(curFun, "#")
		curFun = arr[0]
		//fmt.Println("after findFuncStepCallStack curFun: ", curFun, tmp)
	}

	if parentFun == "" {
		parentFun = tmp
	} else {
		parentFun = parentFun + " -> " + tmp
	}

	//寻址前需剔除获取的参数部分
	if funStepCall[curFun] == "" {
		return
	}

	for _, v := range fs.Targets {
		if curFun == v {
			//fmt.Println("overStack: ", parentFun, " cur ", curFun)
			result[parentFun] = true
			return
		}
	}

	funStrs := funStepCall[curFun]
	arr := strings.Split(funStrs, ";")
	for _, fun := range arr {
		fs.findFuncStepCallStack(fun, parentFun, depth)
	}
}

//todo 完善各种case
func getParamVal(val *ssa.Value) string {
	result := "..."

	switch expr := (*val).(type) {
	case *ssa.Const:
		if expr.Value != nil {
			result = expr.Value.String()
		} else {
			result = expr.Name()
		}

		return result
	case *ssa.FreeVar:
		if expr.String() != "" {
			result = expr.String()
		}
		return result
	case *ssa.Global:
		if expr.String() != "" {
			result = expr.String()
		}
		return result
	}

	if result == "" || result == " " {
		return "..."
	}

	return result
}

func (fs *FunStack) getFuncDel() string {
	fset := token.NewFileSet()
	f, err := parser.ParseFile(fset, fs.FileName, nil, 0)
	if err != nil {
		//fmt.Println("Parser file error ", file)
		return ""
	}

	beginStr := f.Name.Name
	endStr := ""

	ast.Inspect(f, func(x ast.Node) bool {
		if fun, ok := x.(*ast.FuncDecl); ok {
			if fs.Line == fset.Position(x.Pos()).Line {
				if fun.Recv.NumFields() == 0 {
					endStr = beginStr + "." + fun.Name.Name
					return true
				}

				for _, v := range fun.Recv.List {
					if val, ok := v.Type.(*ast.StarExpr); ok {
						if value, ok := val.X.(*ast.Ident); ok {
							//fmt.Println("name StarExpr ", value.Name)
							endStr = beginStr + "." + value.Name + ")." + fun.Name.Name
						}
					}
					if val, ok := v.Type.(*ast.Ident); ok {
						//fmt.Println("name Ident", val.Name)
						endStr = beginStr + "." + val.Name + "." + fun.Name.Name
					}
				}
			}
		}
		return true
	})

	//fmt.Println(endStr)
	return endStr
}

func (fs *FunStack) checkParam() bool {
	flag.Parse()
	if *line <= 0 || *fileName == "" {
		outputToConsole(1, 0, "Need line not 0 and name not null. Use ./funStackMac -h ", []interface{}{})
		return false
	}

	fs.FileName = *fileName
	fs.Line = *line

	fs.Match = fs.getFuncDel()
	if fs.Match == "" {
		outputToConsole(2, 0, "Not find funcdel at line num, Check name param and line param", []interface{}{})
		return false
	}

	if *begin < 0 || *end < 0 || *end < *begin {
		outputToConsole(3, 0, "Begin or End param err", []interface{}{})
		return false
	}

	fs.Begin = *begin
	fs.End = *end

	if *target == "" {
		fs.Targets = []string{"icode.baidu.com/baidu/netdisk/pcs-go-lib/ufc.NewUfcRequest",
			"(*icode.baidu.com/baidu/netdisk/pcs-go-lib/ufc.UFCClient).SendRequest"}
	} else {
		fs.Targets = []string{*target}
	}

	return true
}

func (fs *FunStack) buildSSA() (*ssa.Program, bool) {
	cfg := &packages.Config{
		Mode:  packages.LoadAllSyntax,
		Tests: false,
	}

	var pkgs []*packages.Package
	var err error

	//todo 分析是否有go get 再执行下 或者编译一下？
	//arr := strings.Split(err.Error(), "\n")
	//for i := 0; i < 2; i++
	for i := 0; i < 1; i++ {
		pkgs, err = packages.Load(cfg, "./...")
		if err != nil {
			msg := "Loaded package error, source code need go build ok and use go module"
			outputToConsole(4, 0, msg, []interface{}{})
			return nil, false
		}
	}

	msg := ""
	packages.Visit(pkgs, nil, func(pkg *packages.Package) {
		for _, err := range pkg.Errors {
			msg += err.Error()
		}
	})

	if msg != "" {
		outputToConsole(4, 0, msg, []interface{}{})
		return nil, false
	}

	prog, _ := ssautil.AllPackages(pkgs, ssa.SanityCheckFunctions)
	prog.Build()
	return prog, true
}

func (fs *FunStack) buildCallGraph(prog *ssa.Program) {
	var filePathName string

	for fn := range ssautil.AllFunctions(prog) {
		if strings.Contains(fn.String(), BAIDU_CODE_FLAG) {
			lineNum := fn.Prog.Fset.Position(fn.Pos()).Line
			file := fn.Prog.Fset.File(fn.Pos())

			if file != nil {
				filePathName = file.Name()
			} else {
				filePathName = ""
			}

			//针对函数指针嵌套等情况 lineNum == fs.Line + 1
			if (lineNum == fs.Line || lineNum == fs.Line+1) && (fs.FileName == filePathName) {
				if strings.Contains(fn.String(), fs.Match) {
					fs.Froms = append(fs.Froms, fn.String())
				}
			}

			funStepCall[fn.String()] = ""
			for _, block := range fn.DomPreorder() {
				for _, instr := range block.Instrs {
					switch instr := instr.(type) {
					//case *ssa.MakeClosure:
					case *ssa.Call:
						if instr.Call.StaticCallee() != nil {

							if fs.Begin != 0 && fs.End != 0 && fs.End >= fs.Begin {
								callLine := fn.Prog.Fset.Position(instr.Call.Pos()).Line
								if callLine < fs.Begin || callLine > fs.End {
									continue
								}
							}

							calleeName := instr.Call.StaticCallee().String()
							if strings.Contains(strings.ToLower(calleeName), UFC_FUN_FLAG) {
								args := "#("
								length := len(instr.Call.Args)
								if length > 5 {
									length = 5
								}
								for i := 1; i < length; i++ {
									args += getParamVal(&instr.Call.Args[i]) + ", "
								}
								args = strings.ReplaceAll(args, "\"", "")
								calleeName += args[:len(args)-2] + ")"
							}

							if funStepCall[fn.String()] != "" {
								funStepCall[fn.String()] = funStepCall[fn.String()] + ";" + calleeName
							} else {
								funStepCall[fn.String()] = calleeName
							}
						}
					}
				}
			}
		}
	}
}

func outputResult() {
	datas := []interface{}{}

	//todo 各个md5比较
	for k, _ := range result {
		strs := []string{}
		/*
			k = strings.ReplaceAll(k, "$1", "")
			h := md5.New()
			h.Write([]byte(k))
			fmt.Println(hex.EncodeToString(h.Sum(nil)))
		*/
		arr := strings.Split(k, " -> ")
		for _, v := range arr {
			v = strings.ReplaceAll(v, "#", "")
			v = strings.ReplaceAll(v, "(..., ..., ..., ...)", "")
			strs = append(strs, v)
		}
		datas = append(datas, strs)
	}

	outputToConsole(0, 1, "success", datas)
}

func outputToConsole(errno, hit int, msg string, datas []interface{}) {
	res := simplejson.New()
	res.Set("errno", errno)
	res.Set("msg", msg)
	res.Set("hit", hit)
	res.Set("count", len(datas))
	res.Set("data", datas)
	v, _ := res.Encode()

	result, _ := prettyString(string(v))
	fmt.Println(result)
}

func prettyString(str string) (string, error) {
	var prettyJSON bytes.Buffer
	if err := json.Indent(&prettyJSON, []byte(str), "", "    "); err != nil {
		return "", err
	}
	return prettyJSON.String(), nil
}

func main() {
	fs := FunStack{}
	ret := fs.checkParam()
	if !ret {
		return
	}

	prog, bret := fs.buildSSA()
	if prog == nil || !bret {
		return
	}

	funStepCall = make(map[string]string, 2048)
	result = make(map[string]bool, 64)
	fs.buildCallGraph(prog)

	for _, fromFun := range fs.Froms {
		fs.findFuncStepCallStack(fromFun, "", 0)
	}

	outputResult()
}
