package entity

import (
	"testing"
)

// EasyRuleData MarshalJSON测试入口返回结果封装结构
func TestEasyRuleDataMarshalJSON(t *testing.T) {
	// 创建一个 EasyRuleData 对象进行测试
	apiInfo := &EasyRuleData{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// EasyLastRecord MarshalJSON测试入口返回结果封装结构
func TestEasyLastRecordMarshalJSON(t *testing.T) {
	// 创建一个 EasyLastRecord 对象进行测试
	apiInfo := &EasyLastRecord{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSO<PERSON> returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// TangramReplayUsageStatistics MarshalJSON测试入口返回结果封装结构
func TestTangramReplayUsageStatisticsMarshalJSON(t *testing.T) {
	// 创建一个 TangramReplayUsageStatistics 对象进行测试
	apiInfo := &TangramReplayUsageStatistics{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// TangramReplayDefaultStatistics MarshalJSON测试入口返回结果封装结构
func TestTangramReplayDefaultStatisticsMarshalJSON(t *testing.T) {
	// 创建一个 TangramReplayDefaultStatistics 对象进行测试
	apiInfo := &TangramReplayDefaultStatistics{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// EeTotalLine MarshalJSON测试入口返回结果封装结构
func TestEeTotalLineMarshalJSON(t *testing.T) {
	// 创建一个 EeTotalLine 对象进行测试
	apiInfo := &EeTotalLine{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// EeIncreLine MarshalJSON测试入口返回结果封装结构
func TestEeIncreLineMarshalJSON(t *testing.T) {
	// 创建一个 EeIncreLine 对象进行测试
	apiInfo := &EeIncreLine{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// EasyRepoTotalIncreRecord MarshalJSON测试入口返回结果封装结构
func TestEasyRepoTotalIncreRecordMarshalJSON(t *testing.T) {
	// 创建一个 EasyRepoTotalIncreRecord 对象进行测试
	apiInfo := &EasyRepoTotalIncreRecord{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// EasyRepoTotalRecord MarshalJSON测试入口返回结果封装结构
func TestEasyRepoTotalRecordMarshalJSON(t *testing.T) {
	// 创建一个 EasyRepoTotalRecord 对象进行测试
	apiInfo := &EasyRepoTotalRecord{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// EasyFileexistRecord MarshalJSON测试入口返回结果封装结构
func TestEasyFileexistRecordMarshalJSON(t *testing.T) {
	// 创建一个 EasyFileexistRecord 对象进行测试
	apiInfo := &EasyFileexistRecord{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// EasyFnMsg MarshalJSON测试入口返回结果封装结构
func TestEasyFnMsgMarshalJSON(t *testing.T) {
	// 创建一个 EasyFnMsg 对象进行测试
	apiInfo := &EasyFnMsg{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// BehaviorMeasure MarshalJSON测试入口返回结果封装结构
func TestBehaviorMeasureMarshalJSON(t *testing.T) {
	// 创建一个 BehaviorMeasure 对象进行测试
	apiInfo := &BehaviorMeasure{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// LintIssues MarshalJSON测试入口返回结果封装结构
func TestLintIssuesMarshalJSON(t *testing.T) {
	// 创建一个 LintIssues 对象进行测试
	apiInfo := &LintIssues{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// ScanBatches MarshalJSON测试入口返回结果封装结构
func TestScanBatchesMarshalJSON(t *testing.T) {
	// 创建一个 ScanBatches 对象进行测试
	apiInfo := &ScanBatches{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// SQLScanResults MarshalJSON测试入口返回结果封装结构
func TestSQLScanResultsMarshalJSON(t *testing.T) {
	// 创建一个 SQLScanResults 对象进行测试
	apiInfo := &SQLScanResults{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// SQLDiffRecord MarshalJSON测试入口返回结果封装结构
func TestSQLDiffRecordMarshalJSON(t *testing.T) {
	// 创建一个 SQLDiffRecord 对象进行测试
	apiInfo := &SQLDiffRecord{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}
