/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.easy_repo_total_record数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// easy_repo_total_record表对应的实体
type EasyRepoTotalRecord struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"easy_repo_total_record"`
	// id
	ID int64 `json:"id" sql:"id" pk:"2"`
	// ä»£ç åºåç§°
	RepoName string `json:"repo_name" sql:"repo_name"`
	// å»ºæ¶é´
	CreateAt int64 `json:"create_at" sql:"create_at"`
	// easyä»£ç æ»è¡æ°
	EasyTotalCnt int32 `json:"easy_total_cnt" sql:"easy_total_cnt"`
	// userä»£ç æ»è¡æ°
	UserTotalCnt int32 `json:"user_total_cnt" sql:"user_total_cnt"`
	// easy unit test total cnt
	EasyUnitTestTotalCnt int32 `json:"easy_unit_test_total_cnt" sql:"easy_unit_test_total_cnt"`
	// user unit test total cnt
	UserUnitTestTotalCnt int32 `json:"user_unit_test_total_cnt" sql:"user_unit_test_total_cnt"`
	// commit url
	CommitURL string `json:"commitUrl" sql:"commit_url"`
}

func (t *EasyRepoTotalRecord) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
