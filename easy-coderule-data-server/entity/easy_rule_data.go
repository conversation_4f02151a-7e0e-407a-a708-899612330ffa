/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.easy_rule_data数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// easy_rule_data表对应的实体
type EasyRuleData struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"easy_rule_data"`
	// id
	ID int64 `json:"id" sql:"id" pk:"2"`
	// username
	Username string `json:"username" sql:"username"`
	// project_name
	ProjectName string `json:"project_name" sql:"project_name"`
	// create_at
	CreateAt int64 `json:"create_at" sql:"create_at"`
	// data
	Data string `json:"data" sql:"data"`
}

func (t *EasyRuleData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
