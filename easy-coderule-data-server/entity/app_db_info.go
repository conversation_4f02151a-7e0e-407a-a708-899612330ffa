/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.app_db_info数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// app_db_info表对应的实体
type AppDBInfo struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"app_db_info"`
	// id字段
	ID int32 `json:"id" sql:"id" pk:"2"`
	// app_name字段
	AppName string `json:"app_name" sql:"app_name"`
	// icode字段
	Icode string `json:"icode" sql:"icode"`
	// access_type字段
	AccessType string `json:"access_type" sql:"access_type"`
	// created_at字段
	CreatedAt string `json:"created_at" sql:"created_at"`
	// updated_at字段
	UpdatedAt string `json:"updated_at" sql:"updated_at"`
}

func (t *AppDBInfo) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
