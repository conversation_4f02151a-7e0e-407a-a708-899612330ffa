/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.sql_scan_results数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// sql_scan_results表对应的实体
type SQLScanResults struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"sql_scan_results"`
	// id
	ID int64 `json:"id" sql:"id" pk:"2"`
	// repo
	Repo string `json:"repo" sql:"repo"`
	// rd
	Rd string `json:"rd" sql:"rd"`
	// commit_url
	CommitURL string `json:"commit_url" sql:"commit_url"`
	// sql_text
	SQLText string `json:"sql_text" sql:"sql_text"`
	// filename
	Filename string `json:"filename" sql:"filename"`
	// fn_name
	FnName string `json:"fn_name" sql:"fn_name"`
	// key_name
	KeyName string `json:"key_name" sql:"key_name"`
	// type
	Type string `json:"type" sql:"type"`
	// created_at
	CreatedAt string `json:"created_at" sql:"created_at"`
	// updated_at
	UpdatedAt string `json:"updated_at" sql:"updated_at"`
	// real_key
	RealKey string `json:"real_key" sql:"real_key"`
	// instance_name
	InstanceName string `json:"instance_name" sql:"instance_name"`
	// db_name
	DBName string `json:"db_name" sql:"db_name"`
}

func (t *SQLScanResults) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
