/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.scan_batches数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// scan_batches表对应的实体
type ScanBatches struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"scan_batches"`
	// id
	ID int32 `json:"id" sql:"id" pk:"2"`
	// batch_uid
	BatchUID string `json:"batch_uid" sql:"batch_uid"`
	// repo_name
	RepoName string `json:"repo_name" sql:"repo_name"`
	// scan_time
	ScanTime string `json:"scan_time" sql:"scan_time"`
	// note
	Note string `json:"note" sql:"note"`
	// ignored
	Ignored int32 `json:"ignored" sql:"ignored"`
	// created_at
	CreatedAt string `json:"created_at" sql:"created_at"`
}

func (t *ScanBatches) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
