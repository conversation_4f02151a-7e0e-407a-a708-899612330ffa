/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const UserManagerName = "UserManager"

// UserManager请求client
type UserManagerClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewUserManagerClient(context *easy.Context) *UserManagerClient {
	client := &UserManagerClient{}
	client.Name = UserManagerName
	client.Context = context
	return client
}

// post 参数封装结构
type UserManagerPost struct {
	AppKey       string `json:"appKey"`
	ReturnFields string `json:"returnFields"`
	Sign         string `json:"sign"`
	SRandom      string `json:"sRandom"`
	Timestamp    string `json:"timestamp"`
	Username     string `json:"username"`
}

// 返回结果封装结构
type UserManagerResultResult struct {
	easy.UfcResult
	Username         string `json:"username"`
	SuperiorUsername string `json:"superiorUsername"`
}

func (t *UserManagerResultResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type UserManagerResult struct {
	easy.UfcResult
	Code   int                      `json:"code"`
	Desc   string                   `json:"desc"`
	Result *UserManagerResultResult `json:"result"`
}

func (t *UserManagerResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

// 执行函数发起请求
func (t *UserManagerClient) Do(
	body *UserManagerPost) (*UserManagerResult, error) {
	t.Body = body
	res := &UserManagerResult{}
	err := t.UfcRequest(res)
	return res, err
}
