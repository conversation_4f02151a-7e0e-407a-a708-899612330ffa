/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对下游拓扑封装的client，业务可以实例化对应参数执行Do函数即可获取数据
 */
package remote

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// RPC请求对应的唯一标识，对应业务在平台上配置的服务名称一栏，业务请不要随意变更，若变更请保证代码和配置一致
const UfcBypassName = "UfcBypass"

// UfcBypass请求client
type UfcBypassClient struct {
	easy.UfcClient
}

// 初始化需要传递上下文
func NewUfcBypassClient(context *easy.Context) *UfcBypassClient {
	client := &UfcBypassClient{}
	client.Name = UfcBypassName
	client.Context = context
	return client
}

// get 参数封装结构
type UfcBypassGet struct {
	Cmd             string `json:"cmd"`
	XUfcServiceName string `json:"x-ufc-service-name"`
}

// header 参数封装结构
type UfcBypassHeader struct {
	XUfcSelfServiceName string `json:"x-ufc-self-service-name"`
}

// 返回结果封装结构
type UfcBypassResult struct {
	easy.UfcResult
	Port            uint32      `json:"port"`
	XUfcServiceName string      `json:"x-ufc-service-name"`
	Bns             string      `json:"bns"`
	UfcLogid        interface{} `json:"ufc_logid"`
	Status          uint32      `json:"status"`
	Host            string      `json:"host"`
	ErrorMsg        string      `json:"error_msg"`
	UfcCallid       string      `json:"ufc_callid"`
}

func (t *UfcBypassResult) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

// 执行函数发起请求
func (t *UfcBypassClient) Do(
	query *UfcBypassGet,
	header *UfcBypassHeader) (*UfcBypassResult, error) {
	t.Header = header
	t.Query = query
	res := &UfcBypassResult{}
	err := t.UfcRequest(res)
	return res, err
}
