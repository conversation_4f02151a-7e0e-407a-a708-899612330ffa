/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.easy_fileexist_record数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// easy_fileexist_record表对应的实体
type EasyFileexistRecord struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"easy_fileexist_record"`
	// id字段
	ID int32 `json:"id" sql:"id" pk:"2"`
	// repo_name字段
	RepoName string `json:"repo_name" sql:"repo_name"`
	// exist_filename字段
	ExistFilename string `json:"exist_filename" sql:"exist_filename"`
}

func (t *EasyFileexistRecord) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
