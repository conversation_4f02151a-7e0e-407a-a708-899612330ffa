/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.easy_repo_total_incre_record数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// easy_repo_total_incre_record表对应的实体
type EasyRepoTotalIncreRecord struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"easy_repo_total_incre_record"`
	// id
	ID int64 `json:"id" sql:"id" pk:"2"`
	// 代码库名称
	RepoName string `json:"repo_name" sql:"repo_name"`
	// 创建时间
	CreateAt int64 `json:"create_at" sql:"create_at"`
	// easy代码总增量行数
	EasyTotalCnt int32 `json:"easy_total_cnt" sql:"easy_total_cnt"`
	// user代码总增量行数
	UserTotalCnt int32 `json:"user_total_cnt" sql:"user_total_cnt"`
	// commit_id
	CommitID string `json:"commit_id" sql:"commit_id"`
	// commit_user
	CommitUser string `json:"commit_user" sql:"commit_user"`
	// base_commit_id
	BaseCommitID string `json:"base_commit_id" sql:"base_commit_id"`
	// easy代码总增量行数单测部分
	EasyUnitTestTotalCnt int32 `json:"easy_unit_test_total_cnt" sql:"easy_unit_test_total_cnt"`
	// user代码总增量行数单测部分
	UserUnitTestTotalCnt int32 `json:"user_unit_test_total_cnt" sql:"user_unit_test_total_cnt"`
	// commit url
	CommitURL string `json:"commitUrl" sql:"commit_url"`
}

func (t *EasyRepoTotalIncreRecord) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
