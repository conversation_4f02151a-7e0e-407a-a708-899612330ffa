/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.ee_total_line数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// ee_total_line表对应的实体
type EeTotalLine struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"ee_total_line"`
	// id
	ID int32 `json:"id" sql:"id" pk:"2"`
	// repo_name
	Repo string `json:"repo" sql:"repo"`
	// 增量行数据
	TotalLine int32 `json:"total_line" sql:"total_line"`
	// 创建时间
	Ctime int64 `json:"ctime" sql:"ctime"`
	// mtime字段
	Mtime int64 `json:"mtime" sql:"mtime"`
}

func (t *EeTotalLine) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
