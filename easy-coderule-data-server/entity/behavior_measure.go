/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.behavior_measure数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// behavior_measure表对应的实体
type BehaviorMeasure struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"behavior_measure"`
	// id
	ID int64 `json:"id" sql:"id" pk:"2"`
	// username
	Username string `json:"username" sql:"username"`
	// icode or app
	NameType int32 `json:"name_type" sql:"name_type"`
	// name
	Name string `json:"name" sql:"name"`
	// is wf or not
	IsWf int32 `json:"is_wf" sql:"is_wf"`
	// easy or not easy
	AppType int32 `json:"app_type" sql:"app_type"`
	// active_time
	ActiveTime int32 `json:"active_time" sql:"active_time"`
	// character_count
	CharacterCount int32 `json:"character_count" sql:"character_count"`
	// send_time
	SendType int32 `json:"send_type" sql:"send_type"`
	// commit_msg
	CommitMsg string `json:"commit_msg" sql:"commit_msg"`
	// ctime
	Ctime string `json:"ctime" sql:"ctime"`
	// mtime
	Mtime string `json:"mtime" sql:"mtime"`
	// icafeid
	IcafeID string `json:"icafeId" sql:"icafe_id"`
	// commitid
	CommitID string `json:"commitId" sql:"commit_id"`
}

func (t *BehaviorMeasure) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
