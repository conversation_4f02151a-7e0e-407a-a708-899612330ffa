/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.ee_incre_line数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// ee_incre_line表对应的实体
type EeIncreLine struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"ee_incre_line"`
	// id
	ID int64 `json:"id" sql:"id" pk:"2"`
	// repo_name
	Repo string `json:"repo" sql:"repo"`
	// create_at
	CreateAt int64 `json:"create_at" sql:"create_at"`
	// 增量行数据
	IncreLine int32 `json:"incre_line" sql:"incre_line"`
	// username字段
	Username string `json:"username" sql:"username"`
}

func (t *EeIncreLine) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
