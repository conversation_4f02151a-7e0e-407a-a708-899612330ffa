/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: AppDBInfo 扩展函数，可在此文件自行扩展
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// Add - Entity添加操作前调用，可进行数据校验、数据加密和数据格式转换等操作。
// 使用 t.field 进行赋值和取值，ctx 是调用方透传的上下文对象。
// 通过 ctx.Get("key") 获取调用方传递的值，通过 ctx.Slog 打印日志。
// 返回 error 则终止后续SQL执行，将该 error 返回给调用方。
func (t *AppDBInfo) Add(ctx *easy.Context) error {
	return nil
}

// Update - Entity更新操作前调用，可进行数据校验、数据加密和数据格式转换等操作。
// 使用 t.field 进行赋值和取值，ctx 是调用方透传的上下文对象。
// 返回 error 则终止后续SQL执行，将该 error 返回给调用方。
func (t *AppDBInfo) Update(ctx *easy.Context) error {
	return nil
}

// Delete - Entity删除操作前调用。
// 返回 error 则终止后续SQL执行，将该 error 返回给调用方。
func (t *AppDBInfo) Delete(ctx *easy.Context) error {
	return nil
}

// Get - Entity获取单条数据，list 批量列表数据时会遍历调用此方法。
// 使用 t.field 进行赋值和取值，ctx 是调用方透传的上下文对象。
// 返回 error 则终止后续SQL执行，将该 error 返回给调用方。
func (t *AppDBInfo) Get(ctx *easy.Context) error {
	return nil
}
