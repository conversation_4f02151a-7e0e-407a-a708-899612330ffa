/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: SQLDiffRecord 扩展函数，可在此文件自行扩展
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// ReviewStatus Review状态常量
const (
	ReviewStatusPending	= 0	// 未review
	ReviewStatusNoIssue	= 1	// 已review无问题
	ReviewStatusHasIssue	= 2	// 已review有问题
)

// SQLDiffRecordWithReview 带有review状态的SQL差异记录
type SQLDiffRecordWithReview struct {
	*SQLDiffRecord
	ReviewStatus	int	`json:"review_status"`	// 0=未review, 1=已review无问题, 2=已review有问题
	ReviewTime	string	`json:"review_time"`	// review时间
	Reviewer	string	`json:"reviewer"`	// reviewer用户
}

// GetReviewStatusText 获取review状态文本
func (t *SQLDiffRecordWithReview) GetReviewStatusText() string {
	switch t.ReviewStatus {
	case ReviewStatusNoIssue:
		return "无问题"
	case ReviewStatusHasIssue:
		return "有问题"
	default:
		return "待review"
	}
}

// IsReviewed 是否已review
func (t *SQLDiffRecordWithReview) IsReviewed() bool {
	return t.ReviewStatus != ReviewStatusPending
}

// HasIssue 是否有问题
func (t *SQLDiffRecordWithReview) HasIssue() bool {
	return t.ReviewStatus == ReviewStatusHasIssue
}

// Add - Entity添加操作前调用，可进行数据校验、数据加密和数据格式转换等操作。
// 使用 t.field 进行赋值和取值，ctx 是调用方透传的上下文对象。
// 通过 ctx.Get("key") 获取调用方传递的值，通过 ctx.Slog 打印日志。
// 返回 error 则终止后续SQL执行，将该 error 返回给调用方。
func (t *SQLDiffRecord) Add(ctx *easy.Context) error {
	return nil
}

// Update - Entity更新操作前调用，可进行数据校验、数据加密和数据格式转换等操作。
// 使用 t.field 进行赋值和取值，ctx 是调用方透传的上下文对象。
// 返回 error 则终止后续SQL执行，将该 error 返回给调用方。
func (t *SQLDiffRecord) Update(ctx *easy.Context) error {
	return nil
}

// Delete - Entity删除操作前调用。
// 返回 error 则终止后续SQL执行，将该 error 返回给调用方。
func (t *SQLDiffRecord) Delete(ctx *easy.Context) error {
	return nil
}

// Get - Entity获取单条数据，list 批量列表数据时会遍历调用此方法。
// 使用 t.field 进行赋值和取值，ctx 是调用方透传的上下文对象。
// 返回 error 则终止后续SQL执行，将该 error 返回给调用方。
func (t *SQLDiffRecord) Get(ctx *easy.Context) error {
	return nil
}
