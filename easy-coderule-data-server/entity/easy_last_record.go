/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.easy_last_record数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// easy_last_record表对应的实体
type EasyLastRecord struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"easy_last_record"`
	// id
	ID int32 `json:"id" sql:"id" pk:"2"`
	// repo_name
	RepoName string `json:"repo_name" sql:"repo_name"`
	// last_time
	LastTime int64 `json:"last_time" sql:"last_time"`
	// comm_id
	CommitID string `json:"commit_id" sql:"commit_id"`
	// commit_user
	CommitUser string `json:"commit_user" sql:"commit_user"`
	// commit url
	CommitURL string `json:"commitUrl" sql:"commit_url"`
}

func (t *EasyLastRecord) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
