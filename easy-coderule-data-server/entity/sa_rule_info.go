/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 对mysql_cloud_netdisk_easy_coderule.sa_rule_info数据表封装的描述结构体
 */
package entity

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// sa_rule_info表对应的实体
type SaRuleInfo struct {
	easy.Entity `db_name:"mysql_cloud_netdisk_easy_coderule" table_name:"sa_rule_info"`
	// id字段
	ID int32 `json:"id" sql:"id" pk:"2"`
	// repo字段
	Repo string `json:"repo" sql:"repo"`
	// app字段
	App string `json:"app" sql:"app"`
	// rule字段
	Rule string `json:"rule" sql:"rule"`
	// cnt字段
	Cnt int32 `json:"cnt" sql:"cnt"`
	// created_at字段
	CreatedAt string `json:"created_at" sql:"created_at"`
	// data字段
	Data string `json:"data" sql:"data"`
}

func (t *SaRuleInfo) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
