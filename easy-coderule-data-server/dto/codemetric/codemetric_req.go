/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口入参封装dto
 * 结构体中的tag描述（由平台配置，无需手动修改）：
 * validate: 验证规则，多个规则由逗号分割
 * len：字符串限制长度
 * verify：加入参数验签 通过sign算法，框架自动验证
 * type:"raw" 标识json 格式的参数 通过raw字符串序列化
 */
package codemetric

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

type AddReqDto struct {
	easy.RequestDto

	BodyDto *AddBody `json:"-" type:"raw" validate:""`
}

type GetReqDto struct {
	easy.RequestDto
	Repo string `json:"repo" validate:"required" verify:"false"`
}

type FileexistReqDto struct {
	easy.RequestDto

	BodyDto *FileexistBody `json:"-" type:"raw" validate:""`
}

type IncreReqDto struct {
	easy.RequestDto
	StartTime int64 `json:"start_time" validate:"positiveInteger" verify:"false"`
	EndTime   int64 `json:"end_time" validate:"positiveInteger" verify:"false"`
}

type ListallReqDto struct {
	easy.RequestDto
}

type AddhalffileReqDto struct {
	easy.RequestDto

	BodyDto *AddhalffileBody `json:"-" type:"raw" validate:""`
}

type IncrebyrepoReqDto struct {
	easy.RequestDto
	StartTime  string `json:"start_time" validate:"" verify:"false"`
	EndTime    string `json:"end_time" validate:"" verify:"false"`
	TimeFormat string `json:"time_format" validate:"" verify:"false"`
}

type AddBodyFileData struct {
	FileName string `json:"file_name" validate:""`
}

type AddBody struct {
	RepoName            string             `json:"repo_name" validate:"required"`
	CreateAt            int64              `json:"create_at" validate:"positiveInteger"`
	EasyTotalCnt        int32              `json:"easy_total_cnt" validate:"integer"`
	UserTotalCnt        int32              `json:"user_total_cnt" validate:"integer"`
	IncreEasyTotalCnt   int32              `json:"incre_easy_total_cnt" validate:""`
	IncreUserTotalCnt   int32              `json:"incre_user_total_cnt" validate:""`
	CommitID            string             `json:"commit_id" validate:"required"`
	CommitUser          string             `json:"commit_user" validate:"required"`
	BaseCommitID        string             `json:"base_commit_id" validate:""`
	EasyUtTotalCnt      int32              `json:"easy_ut_total_cnt" validate:"integer"`
	UserUtTotalCnt      int32              `json:"user_ut_total_cnt" validate:"integer"`
	IncreEasyUtTotalCnt int32              `json:"incre_easy_ut_total_cnt" validate:"integer"`
	IncreUserUtTotalCnt int32              `json:"incre_user_ut_total_cnt" validate:"integer"`
	FileData            []*AddBodyFileData `json:"file_data" validate:""`
	CommitURL           string             `json:"commit_url" validate:"required"`
}

type FileexistBody struct {
	RepoName string `json:"repo_name" validate:"required"`
	FileName string `json:"file_name" validate:"required"`
}

type AddhalffileBody struct {
	RepoName string   `json:"repo_name" validate:"required"`
	FileName []string `json:"file_name" validate:"required"`
}

func (r *AddReqDto) UnmarshalBody(data []byte) error {
	return json.Unmarshal(data, &r.BodyDto)
}

func (r *FileexistReqDto) UnmarshalBody(data []byte) error {
	return json.Unmarshal(data, &r.BodyDto)
}

func (r *AddhalffileReqDto) UnmarshalBody(data []byte) error {
	return json.Unmarshal(data, &r.BodyDto)
}
