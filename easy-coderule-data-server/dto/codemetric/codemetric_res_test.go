package codemetric

import (
	"testing"
)

// GetResDto MarshalJSON测试入口返回结果封装结构
func TestGetResDtoMarshalJSON(t *testing.T) {
	// 创建一个 GetResDto 对象进行测试
	apiInfo := &GetResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// FileexistResDto MarshalJSON测试入口返回结果封装结构
func TestFileexistResDtoMarshalJSON(t *testing.T) {
	// 创建一个 FileexistResDto 对象进行测试
	apiInfo := &FileexistResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJ<PERSON><PERSON> returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// IncreResDtoData MarshalJSON测试入口返回结果封装结构
func TestIncreResDtoDataMarshalJSON(t *testing.T) {
	// 创建一个 IncreResDtoData 对象进行测试
	apiInfo := &IncreResDtoData{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// IncreResDto MarshalJSON测试入口返回结果封装结构
func TestIncreResDtoMarshalJSON(t *testing.T) {
	// 创建一个 IncreResDto 对象进行测试
	apiInfo := &IncreResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// ListallResDtoData MarshalJSON测试入口返回结果封装结构
func TestListallResDtoDataMarshalJSON(t *testing.T) {
	// 创建一个 ListallResDtoData 对象进行测试
	apiInfo := &ListallResDtoData{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// ListallResDto MarshalJSON测试入口返回结果封装结构
func TestListallResDtoMarshalJSON(t *testing.T) {
	// 创建一个 ListallResDto 对象进行测试
	apiInfo := &ListallResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// AddhalffileResDto MarshalJSON测试入口返回结果封装结构
func TestAddhalffileResDtoMarshalJSON(t *testing.T) {
	// 创建一个 AddhalffileResDto 对象进行测试
	apiInfo := &AddhalffileResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// IncrebyrepoResDto MarshalJSON测试入口返回结果封装结构
func TestIncrebyrepoResDtoMarshalJSON(t *testing.T) {
	// 创建一个 IncrebyrepoResDto 对象进行测试
	apiInfo := &IncrebyrepoResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}
