/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package sqldata

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type UploadResDto struct {
	easy.BaseDto
}

func (t *UploadResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type DiffviewResDto struct {
	easy.BaseDto
}

func (t *DiffviewResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ReviewResDto struct {
	easy.BaseDto
}

func (t *ReviewResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type IssueshowResDto struct {
	easy.BaseDto
}

func (t *IssueshowResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type HistoryResDtoItem struct {
	SQL      string `json:"sql"`
	FileName string `json:"filename"`
	FnName   string `json:"fn_name"`
	Key      string `json:"key"`
	Type     string `json:"type"`
	Database string `json:"database"`
	Instance string `json:"instance"`
}

type HistoryResDto struct {
	easy.BaseDto
	Data []*HistoryResDtoItem `json:"data"`
}

func (t *HistoryResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type StatResDtoData struct {
	easy.BaseDto
	URL      string `json:"url"`
	Total    int    `json:"total"`
	Pending  int    `json:"pending"`
	NoIssue  int    `json:"no_issue"`
	HasIssue int    `json:"has_issue"`
}

func (t *StatResDtoData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type StatResDto struct {
	easy.BaseDto
	Data []*StatResDtoData `json:"data"`
}

func (t *StatResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
