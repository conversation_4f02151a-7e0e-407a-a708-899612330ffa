package easyrule

import (
	"testing"
)

// ListResDtoDataInfoDiagnostics MarshalJSON测试入口返回结果封装结构
func TestListResDtoDataInfoDiagnosticsMarshalJSON(t *testing.T) {
	// 创建一个 ListResDtoDataInfoDiagnostics 对象进行测试
	apiInfo := &ListResDtoDataInfoDiagnostics{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// ListResDtoDataInfo MarshalJSON测试入口返回结果封装结构
func TestListResDtoDataInfoMarshalJSON(t *testing.T) {
	// 创建一个 ListResDtoDataInfo 对象进行测试
	apiInfo := &ListResDtoDataInfo{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// ListResDtoData MarshalJSON测试入口返回结果封装结构
func TestListResDtoDataMarshalJSON(t *testing.T) {
	// 创建一个 ListResDtoData 对象进行测试
	apiInfo := &ListResDtoData{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// ListResDto MarshalJSON测试入口返回结果封装结构
func TestListResDtoMarshalJSON(t *testing.T) {
	// 创建一个 ListResDto 对象进行测试
	apiInfo := &ListResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}
