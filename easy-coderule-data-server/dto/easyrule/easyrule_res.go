/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package easyrule

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type AddResDto struct {
	easy.BaseDto
}

func (t *AddResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListResDtoDataInfoDiagnostics struct {
	easy.BaseDto
	Posn     string `json:"posn"`
	Message  string `json:"message"`
	Analyzer string `json:"analyzer"`
	URL      string `json:"url"`
}

func (t *ListResDtoDataInfoDiagnostics) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListResDtoDataInfo struct {
	easy.BaseDto
	Filename    string                           `json:"filename"`
	Diagnostics []*ListResDtoDataInfoDiagnostics `json:"diagnostics"`
}

func (t *ListResDtoDataInfo) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListResDtoData struct {
	easy.BaseDto
	Username    string                `json:"username"`
	ProjectName string                `json:"project_name"`
	CreateAt    int64                 `json:"create_at"`
	Info        []*ListResDtoDataInfo `json:"info"`
}

func (t *ListResDtoData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListResDto struct {
	easy.BaseDto
	Data []*ListResDtoData `json:"data"`
	Key2 string            `json:"key2"`
}

func (t *ListResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
