package sa

import (
	"testing"
)

// QueryResDto MarshalJSON测试入口返回结果封装结构
func TestQueryResDtoMarshalJSON(t *testing.T) {
	// 创建一个 QueryResDto 对象进行测试
	apiInfo := &QueryResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// ListIcodeResDtoList MarshalJSON测试入口返回结果封装结构
func TestListIcodeResDtoListMarshalJSON(t *testing.T) {
	// 创建一个 ListIcodeResDtoList 对象进行测试
	apiInfo := &ListIcodeResDtoList{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// ListIcodeResDto MarshalJSON测试入口返回结果封装结构
func TestListIcodeResDtoMarshalJSON(t *testing.T) {
	// 创建一个 ListIcodeResDto 对象进行测试
	apiInfo := &ListIcodeResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}
