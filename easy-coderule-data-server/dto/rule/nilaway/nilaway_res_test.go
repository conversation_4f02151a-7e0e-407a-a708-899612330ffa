package nilaway

import (
	"testing"
)

// ListByRepoResDtoIssues MarshalJSON测试入口返回结果封装结构
func TestListByRepoResDtoIssuesMarshalJSON(t *testing.T) {
	// 创建一个 ListByRepoResDtoIssues 对象进行测试
	apiInfo := &ListByRepoResDtoIssues{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// ListByRepoResDto MarshalJSON测试入口返回结果封装结构
func TestListByRepoResDtoMarshalJSON(t *testing.T) {
	// 创建一个 ListByRepoResDto 对象进行测试
	apiInfo := &ListByRepoResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}
