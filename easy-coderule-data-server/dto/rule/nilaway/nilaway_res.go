/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package nilaway

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type IgnoreBatchResDto struct {
	easy.BaseDto
}

func (t *IgnoreBatchResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type IgnoreOneResDto struct {
	easy.BaseDto
}

func (t *IgnoreOneResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type SaveListResDto struct {
	easy.BaseDto
}

func (t *SaveListResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ShowResDto struct {
	easy.BaseDto
}

func (t *ShowResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ProcessResDto struct {
	easy.BaseDto
}

func (t *ProcessResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListByRepoResDtoIssues struct {
	easy.BaseDto
	RelFilePath string `json:"rel_file_path"`
	Fingerprint string `json:"fingerprint"`
	Message     string `json:"message"`
	Line        int    `json:"line"`
	Col         int    `json:"col"`
	CodeContext string `json:"code_context"`
	Status      string `json:"status"`
	Username    string `json:"username"`
	FirstSeenAt string `json:"first_seen_at"`
	LastSeenAt  string `json:"last_seen_at"`
	Ignored     bool   `json:"ignored"`
}

func (t *ListByRepoResDtoIssues) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type ListByRepoResDto struct {
	easy.BaseDto
	RepoName string                    `json:"repo_name"`
	Issues   []*ListByRepoResDtoIssues `json:"issues"`
}

func (t *ListByRepoResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
