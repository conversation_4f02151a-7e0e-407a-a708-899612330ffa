/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package wfmetric

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type AddResDto struct {
	easy.BaseDto
}

func (t *AddResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type QueryResDto struct {
	easy.BaseDto
	Username       string `json:"username"`
	NameType       string `json:"name_type"`
	Name           string `json:"name"`
	IsWf           bool   `json:"is_wf"`
	AppType        int    `json:"app_type"`
	ActiveTime     int32  `json:"active_time"`
	CharacterCount int32  `json:"character_count"`
	// 附加字段，需要额外查询
	Manager string `json:"manager"`
	// 附加字段，需要额外查询
	Team string `json:"team"`
	// 提取自 commit_msg
	IcafeID string `json:"icafe_id"`
	// commit id
	CommitID string `json:"commit_id"`
}

func (t *QueryResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
