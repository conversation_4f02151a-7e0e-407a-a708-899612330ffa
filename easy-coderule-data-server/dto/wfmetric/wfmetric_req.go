/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口入参封装dto
 * 结构体中的tag描述（由平台配置，无需手动修改）：
 * validate: 验证规则，多个规则由逗号分割
 * len：字符串限制长度
 * verify：加入参数验签 通过sign算法，框架自动验证
 * type:"raw" 标识json 格式的参数 通过raw字符串序列化
 */
package wfmetric

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

type AddReqDto struct {
	easy.RequestDto

	BodyDto *AddBody `json:"-" type:"raw" validate:""`
}

type QueryReqDto struct {
	easy.RequestDto
	Start string `json:"start" validate:"" verify:"false"`
	End   string `json:"end" validate:"" verify:"false"`
}

type AddBody struct {
	Username string `json:"username" validate:""`
	// 1: app，2: icode
	NameType int    `json:"name_type" validate:""`
	Name     string `json:"name" validate:""`
	//  0: 非easy, 1: easy， 2： easy adapter ;
	IsWf           bool  `json:"is_wf" validate:""`
	AppType        int32 `json:"app_type" validate:""`
	ActiveTime     int32 `json:"active_time" validate:""`
	CharacterCount int   `json:"character_count" validate:""`
	// 1: commit, 2:  interval
	SendType  int    `json:"send_type" validate:""`
	CommitMsg string `json:"commit_msg" validate:""`
	CommitID  string `json:"commit_id" validate:""`
}

func (r *AddReqDto) UnmarshalBody(data []byte) error {
	return json.Unmarshal(data, &r.BodyDto)
}
