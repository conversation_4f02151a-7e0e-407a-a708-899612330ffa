/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package echo

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type HandleResDto struct {
	easy.BaseDto
	Content string `json:"content"`
	Demo    string `json:"demo"`
}

func (t *HandleResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
