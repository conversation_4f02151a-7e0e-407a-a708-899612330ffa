/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口入参封装dto
 * 结构体中的tag描述（由平台配置，无需手动修改）：
 * validate: 验证规则，多个规则由逗号分割
 * len：字符串限制长度
 * verify：加入参数验签 通过sign算法，框架自动验证
 * type:"raw" 标识json 格式的参数 通过raw字符串序列化
 */
package metric

import (
	"encoding/json"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

type AddReqDto struct {
	easy.RequestDto

	BodyDto *AddBody `json:"-" type:"raw" validate:""`
}

type IncreReqDto struct {
	easy.RequestDto
	TimeFormat string `json:"time_format" validate:"required" verify:"false"`
	Start      string `json:"start" validate:"required" verify:"false"`
	End        string `json:"end" validate:"required" verify:"false"`
	Fe         bool   `json:"fe" validate:"" verify:"false"`
	Notfilter  bool   `json:"notfilter" validate:"" verify:"false"`
}

type AddBody struct {
	Repo string `json:"repo" validate:""`
	User string `json:"user" validate:""`
}

func (r *AddReqDto) UnmarshalBody(data []byte) error {
	return json.Unmarshal(data, &r.BodyDto)
}
