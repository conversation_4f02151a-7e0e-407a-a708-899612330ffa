/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package dbinfo

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type QueryConfigResDtoData struct {
	easy.BaseDto
	GroupName   string `json:"group_name"`
	Database    string `json:"database"`
	AddressType string `json:"address_type"`
	To          string `json:"to"`
	Instance    string `json:"instance"`
}

func (t *QueryConfigResDtoData) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type QueryConfigResDto struct {
	easy.BaseDto
	Data []*QueryConfigResDtoData `json:"data"`
}

func (t *QueryConfigResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
