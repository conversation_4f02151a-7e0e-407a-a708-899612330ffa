package dbinfo

import (
	"testing"
)

// QueryConfigResDtoData MarshalJSON测试入口返回结果封装结构
func TestQueryConfigResDtoDataMarshalJSON(t *testing.T) {
	// 创建一个 QueryConfigResDtoData 对象进行测试
	apiInfo := &QueryConfigResDtoData{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}

// QueryConfigResDto MarshalJSON测试入口返回结果封装结构
func TestQueryConfigResDtoMarshalJSON(t *testing.T) {
	// 创建一个 QueryConfigResDto 对象进行测试
	apiInfo := &QueryConfigResDto{
		// 设置其他字段的值
	}

	// 调用 MarshalJSON 方法
	jsonData, err := apiInfo.MarshalJSON()
	if err != nil {
		t.Fatalf("MarshalJSON returned an error: %v", err)
	}
	t.Logf("JSON : %s", jsonData)
}
