/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 接口出参封装dto
 */
package easyfn

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// DTO是Data Transfer Object 的简写，既数据传输对象。

type AddResDto struct {
	easy.BaseDto
}

func (t *AddResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type GetResDto struct {
	easy.BaseDto
	Repo     string      `json:"repo"`
	Branch   string      `json:"branch"`
	CommitAt string      `json:"commit_at"`
	CommitID string      `json:"commit_id"`
	CreateAt string      `json:"create_at"`
	Data     interface{} `json:"data"`
}

func (t *GetResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}

type BranchqueryResDto struct {
	easy.BaseDto
	Branchs []string `json:"branchs"`
}

func (t *BranchqueryResDto) MarshalJSON() ([]byte, error) {
	return easy.MarshalJSON(t)
}
