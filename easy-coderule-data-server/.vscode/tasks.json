{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [
    {
      "label": "reinstall tangram",
      "type": "shell",
      "command": "make tangram",
      "options": {
        "env": {
          "PATH": "/opt/compiler/gcc-8.2/bin:${env:PATH}"
        }
      }
    },
    {
      "label": "build for debug",
      "type": "shell",
      "command": "make debug",
      "options": {
        "env": {
          "PATH": "/opt/compiler/gcc-8.2/bin:${env:PATH}"
        }
      }
    }
  ]
}