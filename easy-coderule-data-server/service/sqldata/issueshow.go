package service

import (
	"bytes"
	"encoding/json"
	"html/template"
	"strings"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/sqldata"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type IssueshowService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化
	ReqDto           *dto.IssueshowReqDto
	SQLDiffRecordDao *dao.SQLDiffRecordDao
}

// 初始化 必须传入上下文
func NewIssueshowService(ctx *easy.Context) *IssueshowService {
	service := &IssueshowService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

func (s *IssueshowService) IssueshowExec(reqDto *dto.IssueshowReqDto) ([]byte, error) {
	s.ReqDto = reqDto

	id := s.ReqDto.ID
	sqlDiffRecord, err := s.SQLDiffRecordDao.Get(id)
	if err != nil {
		s.SLog.Warning("sql diff record not found").Set("id", id).Print()
		return nil, err
	}

	var extra DiffSQLExtra
	if err := json.Unmarshal([]byte(sqlDiffRecord.Extra), &extra); err != nil {
		s.SLog.Warning("failed to unmarshal extra data").Set("id", id).Print()
		return nil, easy.CustomError(2, "failed to parse extra data", 500)
	}

	htmlContent := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta charset="UTF-8">
	<title>SQL Diff Review</title>
	<style>
		body { font-family: "Segoe UI", sans-serif; margin: 30px; background: #f7f9fb; color: #333; line-height: 1.6; }
		h1 { font-size: 28px; margin-bottom: 20px; }
		h2 { margin-top: 30px; color: #2c3e50; font-size: 22px; }
		h3 { font-size: 18px; margin-top: 20px; }
		table { border-collapse: collapse; width: 100%; margin-top: 10px; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.05); }
		th, td { border: 1px solid #ddd; padding: 10px 12px; vertical-align: top; font-size: 14px; }
		th { background-color: #f0f2f5; width: 180px; text-align: left; font-weight: 600; }
		pre { background-color: #f8f8f8; padding: 10px; border-radius: 4px; white-space: pre-wrap; font-family: "Courier New", monospace; }
		.section { margin-bottom: 40px; padding: 20px 24px; border-radius: 10px; background: #ffffff; box-shadow: 0 2px 6px rgba(0,0,0,0.06); }
		a { color: #007acc; text-decoration: none; }
		a:hover { text-decoration: underline; }
		.sql-diff-html { margin-top: 15px; padding: 15px; border: 1px solid #ccc; background: #fefefe; font-family: "Courier New", monospace; font-size: 13px; line-height: 1.6; white-space: pre-wrap; word-break: break-word; border-radius: 6px; overflow-x: auto; }
		.sql-diff-html ins { background-color: #e6ffe6; text-decoration: none; }
		.sql-diff-html del { background-color: #ffe6e6; text-decoration: none; }
	</style>
</head>
<body>
	<h1>SQL Diff 审查报告</h1>

	<div class="section">
		<h2>基本信息</h2>
		<table>
			<tr><th>ID</th><td>{{.ID}}</td></tr>
			<tr><th>仓库</th><td>{{.Repo}}</td></tr>
			<tr><th>负责人</th><td>{{.Rd}}</td></tr>
			<tr><th>提交链接</th><td><a href="{{.CommitURL}}" target="_blank">{{.CommitURL}}</a></td></tr>
			<tr><th>旧文件名</th><td>{{.OldFilename}}</td></tr>
			<tr><th>新文件名</th><td>{{.NewFilename}}</td></tr>
			<tr><th>旧函数名</th><td>{{.OldFnName}}</td></tr>
			<tr><th>新函数名</th><td>{{.NewFnName}}</td></tr>
		</table>
	</div>

	<div class="section">
		<h2>SQL 内容对比</h2>
		<table>
			<tr><th>旧 SQL</th><td><pre>{{.OldSQLText}}</pre></td></tr>
			<tr><th>新 SQL</th><td><pre>{{.NewSQLText}}</pre></td></tr>
		</table>
		{{if .DiffHTMLSafe}}
		<div class="sql-diff-html">
			<h3>SQL Diff 渲染视图</h3>
			{{.DiffHTMLSafe}}
		</div>
		{{end}}
	</div>

	{{if or .Extra.IssueDesc .Extra.IssueRisk .Extra.IssueSuggestion}}
	<div class="section">
		<h2>问题详情（Extra）</h2>
		<table>
			<tr><th>问题描述</th><td>{{.Extra.IssueDesc}}</td></tr>
			<tr><th>风险等级</th><td>{{.Extra.IssueRisk}}</td></tr>
			<tr><th>修复建议</th><td>{{.Extra.IssueSuggestion}}</td></tr>
			<tr><th>Reviewer</th><td>{{.Extra.Reviewer}}</td></tr>
		</table>
	</div>
	{{end}}

</body>
</html>`

	type PageData struct {
		*entity.SQLDiffRecord
		Extra        DiffSQLExtra
		DiffHTMLSafe template.HTML
	}

	tmpl, err := template.New("review").Parse(htmlContent)
	if err != nil {
		return nil, err
	}

	sqlDiffRecord.OldFilename = strings.Replace(sqlDiffRecord.OldFilename, "/home/<USER>/gocheck/newcode/", "", 1)
	sqlDiffRecord.NewFilename = strings.Replace(sqlDiffRecord.NewFilename, "/home/<USER>/gocheck/newcode/", "", 1)

	var buf bytes.Buffer
	err = tmpl.Execute(&buf, PageData{
		SQLDiffRecord: sqlDiffRecord,
		Extra:         extra,
		DiffHTMLSafe:  template.HTML(sqlDiffRecord.DiffSQLHTML),
	})
	if err != nil {
		s.SLog.Warning("render review page error").SetErr(err).Print()
		return nil, err
	}

	return buf.Bytes(), nil
}

func renderReviewStatus(status int32) string {
	switch status {
	case 0:
		return "未 Review"
	case 1:
		return "已 Review（无问题）"
	case 2:
		return "已 Review（有问题）"
	default:
		return "未知"
	}
}
