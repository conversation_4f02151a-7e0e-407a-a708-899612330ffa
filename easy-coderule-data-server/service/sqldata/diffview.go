/*
* 主体代码由Easy生成，**后续update不会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: diffview业务封装
* 可根据业务自行扩展
 */
package service

import (
	"fmt"
	"strings"

	"html/template"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/sqldata"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type DiffviewService struct {
	easy.Service

	// 初始化Dao的位置，切记是指针类型，不需要初始化
	ReqDto *dto.DiffviewReqDto

	SQLDiffRecordDao *dao.SQLDiffRecordDao
}

type RenderableSQLDiffRecord struct {
	*entity.SQLDiffRecord
	RenderedDiffSQLHTML template.HTML
}

// 初始化 必须传入上下文
func NewDiffviewService(ctx *easy.Context) *DiffviewService {
	service := &DiffviewService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

func (s *DiffviewService) DiffviewExec(reqDto *dto.DiffviewReqDto) (string, error) {
	s.ReqDto = reqDto
	return s.DiffView(reqDto.CommitURL, reqDto.SendUser)
}

func (s *DiffviewService) DiffView(commitURL string, sendUser string) (string, error) {
	diffRecords, err := s.SQLDiffRecordDao.GetListByCommitURL(commitURL)
	if err != nil {
		s.Context.SLog.Error("get diff records error").Set("commit_url", commitURL).SetErr(err).Print()
		return "", err
	}
	return s.diffView(commitURL, sendUser, diffRecords)
}

// DiffView 生成展示diff的HTML页面
func (s *DiffviewService) diffView(commitURL string, sendUser string, diffRecords []*entity.SQLDiffRecord) (string, error) {

	// HTML模板
	htmlTemplate := `
 <!DOCTYPE html>
 <html lang="zh-CN">
 <head>
     <meta charset="UTF-8">
     <meta name="viewport" content="width=device-width, initial-scale=1.0">
     <title>SQL差异报告 - {{.CommitURL}}</title>
     <style>
         body {
             font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
             margin: 0;
             padding: 20px;
             background-color: #f5f5f5;
         }
         .container {
             max-width: 1200px;
             margin: 0 auto;
             background-color: white;
             border-radius: 8px;
             box-shadow: 0 2px 10px rgba(0,0,0,0.1);
             overflow: hidden;
         }
         .header {
             background-color: #2c3e50;
             color: white;
             padding: 20px;
             text-align: center;
         }
         .header h1 {
             margin: 0;
             font-size: 28px;
         }
         .header p {
             margin: 10px 0 0 0;
             opacity: 0.8;
             word-break: break-all;
         }
         .commit-info {
             background-color: #3498db;
             color: white;
             padding: 15px 20px;
             border-bottom: 1px solid #2980b9;
         }
         .commit-info a {
             color: white;
             text-decoration: none;
             font-weight: bold;
         }
         .commit-info a:hover {
             text-decoration: underline;
         }
         .stats {
             display: flex;
             justify-content: space-around;
             padding: 20px;
             background-color: #ecf0f1;
             border-bottom: 1px solid #ddd;
         }
         .stat-item {
             text-align: center;
         }
         .stat-value {
             font-size: 24px;
             font-weight: bold;
             color: #2c3e50;
         }
         .stat-label {
             font-size: 14px;
             color: #7f8c8d;
             margin-top: 5px;
         }
         .diff-item {
             margin: 20px;
             padding: 20px;
             border: 1px solid #ddd;
             border-radius: 8px;
             background-color: #fafafa;
         }
         .diff-header {
             display: flex;
             justify-content: space-between;
             align-items: center;
             margin-bottom: 15px;
             padding-bottom: 10px;
             border-bottom: 2px solid #e74c3c;
         }
         .diff-title {
             font-size: 18px;
             font-weight: bold;
             color: #2c3e50;
         }
         .diff-time {
             font-size: 12px;
             color: #7f8c8d;
         }
         .diff-content {
             display: flex;
             flex-direction: column;
             gap: 15px;
         }
         .diff-column {
             background-color: white;
             border-radius: 4px;
             padding: 15px;
             border: 1px solid #ddd;
             width: 100%;
         }
         .diff-column h4 {
             margin: 0 0 10px 0;
             padding: 8px;
             border-radius: 4px;
             font-size: 14px;
         }
         .old-column h4 {
             background-color: #ffebee;
             color: #c62828;
         }
         .new-column h4 {
             background-color: #e8f5e8;
             color: #2e7d32;
         }
         .field-row {
             margin: 8px 0;
             font-size: 13px;
         }
         .field-label {
             font-weight: bold;
             color: #555;
             display: inline-block;
             width: 80px;
         }
         .field-value {
             color: #333;
             word-break: break-all;
         }
         .sql-text {
             background-color: #f8f9fa;
             border: 1px solid #e9ecef;
             border-radius: 4px;
             padding: 10px;
             margin-top: 5px;
             font-family: 'Courier New', monospace;
             font-size: 12px;
             white-space: pre-wrap;
             overflow-x: auto;
         }
         .batch-info {
             background-color: #fff3cd;
             border: 1px solid #ffeaa7;
             padding: 10px;
             margin: 10px 20px;
             border-radius: 4px;
             font-size: 12px;
             color: #856404;
         }
         .empty-state {
             text-align: center;
             padding: 60px 20px;
             color: #7f8c8d;
         }
         .empty-state h3 {
             margin: 0 0 10px 0;
             font-size: 20px;
         }
         .repo-info {
             background-color: #f8f9fa;
             padding: 10px 20px;
             border-bottom: 1px solid #dee2e6;
             font-size: 14px;
             color: #6c757d;
         }
         .review-panel {
             background-color: #f8f9fa;
             border: 1px solid #dee2e6;
             border-radius: 6px;
             padding: 15px;
             margin-top: 15px;
         }
         .review-header {
             display: flex;
             justify-content: space-between;
             align-items: center;
             margin-bottom: 10px;
         }
         .review-info {
             color: #6c757d;
             font-size: 11px;
             margin-bottom: 10px;
             font-style: italic;
         }
         .review-status {
             font-weight: bold;
             padding: 4px 8px;
             border-radius: 4px;
             font-size: 12px;
         }
         .status-pending {
             background-color: #fff3cd;
             color: #856404;
             border: 1px solid #ffeaa7;
         }
         .status-no-issue {
             background-color: #d4edda;
             color: #155724;
             border: 1px solid #c3e6cb;
         }
         .status-has-issue {
             background-color: #f8d7da;
             color: #721c24;
             border: 1px solid #f5c6cb;
         }
         .review-buttons {
             display: flex;
             gap: 8px;
         }
         
         .issue-forms {
             border-top: 1px solid #eee;
             padding-top: 15px;
         }
         
         .form-group {
             margin-bottom: 15px;
         }
         
         .form-group label {
             display: block;
             margin-bottom: 5px;
             font-weight: bold;
             font-size: 14px;
             color: #555;
         }
         
         .form-control {
             width: 100%;
             padding: 8px;
             border: 1px solid #ddd;
             border-radius: 4px;
             font-size: 14px;
             box-sizing: border-box;
         }
         
         .form-control:focus {
             border-color: #3498db;
             outline: none;
             box-shadow: 0 0 0 2px rgba(52,152,219,0.2);
         }
         
         .form-submit {
             margin-top: 15px;
             text-align: right;
         }
         
         .form-submit .btn {
             margin-left: 10px;
         }
         .btn {
             padding: 6px 12px;
             border: none;
             border-radius: 4px;
             cursor: pointer;
             font-size: 12px;
             transition: all 0.2s;
             text-decoration: none;
             display: inline-block;
         }
         .btn-success {
             background-color: #28a745;
             color: white;
         }
         .btn-success:hover {
             background-color: #218838;
         }
         .btn-danger {
             background-color: #dc3545;
             color: white;
         }
         .btn-danger:hover {
             background-color: #c82333;
         }
         .btn-secondary {
             background-color: #6c757d;
             color: white;
         }
         .btn-secondary:hover {
             background-color: #5a6268;
         }
         .batch-operations {
             background-color: #e3f2fd;
             border: 1px solid #bbdefb;
             border-radius: 6px;
             padding: 15px;
             margin: 20px;
             text-align: center;
         }
         .batch-operations h4 {
             margin: 0 0 10px 0;
             color: #1976d2;
         }
         .checkbox-container {
             margin: 10px 0;
         }
         .checkbox-container input[type="checkbox"] {
             margin-right: 8px;
         }
         .loading {
             opacity: 0.6;
             pointer-events: none;
         }
     </style>
 </head>
 <body>
     <div class="container">
         <div class="header">
             <h1>SQL差异报告</h1>
             <p>提交: {{.CommitURL}}</p>
         </div>
         
         <div class="commit-info">
             <div>🔗 <a href="{{.CommitURL}}" target="_blank">查看提交详情</a></div>
         </div>
         
         {{if .FirstRecord}}
         <div class="repo-info">
             📁 仓库: {{.FirstRecord.Repo}} | 👤 RD: {{.FirstRecord.Rd}} | 🔍 审核人: {{.SendUser}} | 🕒 生成时间: {{.GeneratedAt}}
         </div>
         {{end}}
         
         <div class="stats">
             <div class="stat-item">
                 <div class="stat-value">{{.TotalDiffs}}</div>
                 <div class="stat-label">总差异数</div>
             </div>
             <div class="stat-item">
                 <div class="stat-value">{{.TotalBatches}}</div>
                 <div class="stat-label">批次数</div>
             </div>
             <div class="stat-item">
                 <div class="stat-value">{{.PendingReviews}}</div>
                 <div class="stat-label">待review</div>
             </div>
             <div class="stat-item">
                 <div class="stat-value">{{.NoIssueReviews}}</div>
                 <div class="stat-label">无问题</div>
             </div>
             <div class="stat-item">
                 <div class="stat-value">{{.HasIssueReviews}}</div>
                 <div class="stat-label">有问题</div>
             </div>
         </div>
 
         <!-- 
         {{if .Records}}
         <div class="batch-operations">
             <h4>📋 批量操作</h4>
             <div class="checkbox-container">
                 <label>
                     <input type="checkbox" id="selectAll"> 全选
                 </label>
             </div>
             <div class="review-buttons">
                 <button class="btn btn-success" onclick="batchReview(1)">批量标记无问题</button>
                 <button class="btn btn-danger" onclick="batchReview(2)">批量标记有问题</button>
                 <button class="btn btn-secondary" onclick="batchReview(0)">批量重置状态</button>
             </div>
         </div>
         {{end}}
         -->
         
         {{if .Records}}
             {{range .Records}}
             <div class="diff-item" data-id="{{.ID}}">
                 <div class="diff-header">
                     <div class="diff-title">
                         <input type="checkbox" class="item-checkbox" value="{{.ID}}">
                         {{.UnicKey}}
                     </div>
                     <div class="diff-time">{{.CreatedAt}}</div>
                 </div>
                 
                 {{if .BatchID}}
                 <div class="batch-info">
                     批次ID: {{.BatchID}}
                 </div>
                 {{end}}
                 
                 <div class="diff-content">
                     <div class="diff-column">
                         <h4 class="old-column">原始版本</h4>
                         <div class="field-row">
                             <span class="field-label">实例名:</span>
                             <span class="field-value">{{.OldInstanceName}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">DB名:</span>
                             <span class="field-value">{{.OldDBName}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">文件:</span>
                             <span class="field-value">{{.OldFilename}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">函数:</span>
                             <span class="field-value">{{.OldFnName}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">键名:</span>
                             <span class="field-value">{{.OldKeyName}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">类型:</span>
                             <span class="field-value">{{.OldType}}</span>
                         </div>                        
                         <div class="field-row">
                             <span class="field-label">SQL:</span>
                             <div class="sql-text">{{.OldSQLText}}</div>
                         </div>
                     </div>
                     
                     <div class="diff-column">
                         <h4 class="new-column">新版本</h4>
                         <div class="field-row">
                             <span class="field-label">实例名:</span>
                             <span class="field-value">{{.OldInstanceName}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">DB名:</span>
                             <span class="field-value">{{.OldDBName}}</span>
                         </div>
                          <div class="field-row">
                             <span class="field-label">文件:</span>
                             <span class="field-value">{{.NewFilename}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">函数:</span>
                             <span class="field-value">{{.NewFnName}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">键名:</span>
                             <span class="field-value">{{.NewKeyName}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">类型:</span>
                             <span class="field-value">{{.NewType}}</span>
                         </div>
                         <div class="field-row">
                             <span class="field-label">SQL:</span>
                             <div class="sql-text">{{.NewSQLText}}</div>
                         </div>
                     </div>
                 </div>
                 
                 <div class="diff-sql-html">
                    <h4 style="margin-top:20px; color:#e74c3c;">SQL Diff 预览</h4>
                    <div class="sql-text" style="background-color: #fff;">
                        {{.RenderedDiffSQLHTML}}
                    </div>
                </div>
             

                 <div class="review-panel">
                     <div class="review-header">
                         <span>📝 Review状态:</span>
                         {{if eq .ReviewStatus 0}}
                         <span class="review-status status-pending" id="status-{{.ID}}">待review</span>
                         {{else if eq .ReviewStatus 1}}
                         <span class="review-status status-no-issue" id="status-{{.ID}}">✅ 无问题</span>
                         {{else if eq .ReviewStatus 2}}
                         <span class="review-status status-has-issue" id="status-{{.ID}}">❌ 有问题</span>
                         {{end}}
                     </div>
                     {{if .ReviewTime}}
                     <div class="review-info">
                         <small>👤 {{.Reviewer}} 于 {{.ReviewTime}} review</small>
                     </div>
                     {{end}}
                     <div class="review-buttons">
                         <button class="btn btn-success" onclick="setReviewStatus({{.ID}}, 1)">✅ 无问题</button>
                         <button class="btn btn-danger" onclick="showIssueForms({{.ID}})">❌ 有问题</button>
                         <button class="btn btn-secondary" onclick="setReviewStatus({{.ID}}, 0)">🔄 重置</button>
                     </div>
                     <div class="issue-forms" id="issue-forms-{{.ID}}" style="margin-top:15px; display:none;">
                         <div class="form-group">
                             <label for="issue-desc-{{.ID}}">问题描述</label>
                             <textarea class="form-control" id="issue-desc-{{.ID}}" rows="2" placeholder="请描述具体问题..."></textarea>
                         </div>
                         <div class="form-group">
                             <label for="issue-risk-{{.ID}}">具体风险</label>
                             <textarea class="form-control" id="issue-risk-{{.ID}}" rows="2" placeholder="请描述可能带来的风险..."></textarea>
                         </div>
                         <div class="form-group">
                             <label for="issue-suggestion-{{.ID}}">改造建议</label>
                             <textarea class="form-control" id="issue-suggestion-{{.ID}}" rows="2" placeholder="请提供改造建议..."></textarea>
                         </div>
                         <div class="form-submit">
                             <button class="btn btn-primary" onclick="submitIssue({{.ID}})">提交问题</button>
                             <button class="btn btn-secondary" onclick="hideIssueForms({{.ID}})">取消</button>
                         </div>
                     </div>
                 </div>
             </div>
             {{end}}
         {{else}}
             <div class="empty-state">
                 <h3>暂无差异记录</h3>
                 <p>该提交没有SQL差异记录</p>
             </div>
         {{end}}
     </div>
     
     <script>
         // 全局变量
         const REVIEWER = '{{.SendUser}}';
         const API_BASE = '{{.APIBase}}';
         
         // 全选功能
         document.getElementById('selectAll').addEventListener('change', function(e) {
             const checkboxes = document.querySelectorAll('.item-checkbox');
             checkboxes.forEach(checkbox => {
                 checkbox.checked = e.target.checked;
             });
         });
         
         // 更新状态显示
         function updateStatus(diffId, status) {
             const statusElement = document.getElementById('status-' + diffId);
             const statusClasses = ['status-pending', 'status-no-issue', 'status-has-issue'];
             const statusTexts = ['待review', '✅ 无问题', '❌ 有问题'];
             
                          // 清除所有状态类
              statusClasses.forEach(cls => statusElement.classList.remove(cls));
             
             // 添加新状态类和文本
             statusElement.classList.add(statusClasses[status]);
             statusElement.textContent = statusTexts[status];
         }
         
         // 显示加载状态
         function setLoading(diffId, loading) {
             const diffItem = document.querySelector('[data-id="' + diffId + '"]');
             if (loading) {
                 diffItem.classList.add('loading');
             } else {
                 diffItem.classList.remove('loading');
             }
         }
         // 显示问题表单
         function showIssueForms(diffId) {
             const forms = document.getElementById('issue-forms-' + diffId);
             forms.style.display = 'block';
         }
         
         // 隐藏问题表单
         function hideIssueForms(diffId) {
             const forms = document.getElementById('issue-forms-' + diffId);
             forms.style.display = 'none';
         }
         
         // 设置review状态
         function setReviewStatus(diffId, reviewStatus) {
             submitReview(diffId, reviewStatus);
         }
    
         // 提交问题
         function submitIssue(diffId) {
             const issueDesc = document.getElementById('issue-desc-' + diffId)?.value || '';
             const issueRisk = document.getElementById('issue-risk-' + diffId)?.value || '';
             const issueSuggestion = document.getElementById('issue-suggestion-' + diffId)?.value || '';
             
             if (!issueDesc) {
                 showMessage('请填写问题描述', 'error');
                 return;
             }
             
             submitReview(diffId, 2, issueDesc, issueRisk, issueSuggestion);
         }
         
         // 提交review
         function submitReview(diffId, reviewStatus, issueDesc = '', issueRisk = '', issueSuggestion = '') {
             console.log("submit review start");
             setLoading(diffId, true);
             
             const url = API_BASE + '?method=review&reviewer=' + encodeURIComponent(REVIEWER) + 
                        '&diff_id=' + diffId + '&review_status=' + reviewStatus + 
                        '&issue_desc=' + encodeURIComponent(issueDesc) + 
                        '&issue_risk=' + encodeURIComponent(issueRisk) + 
                        '&issue_suggestion=' + encodeURIComponent(issueSuggestion);


            console.log("url: ", url);
        
        
             fetch(url, {
                 method: 'GET',
                 headers: {
                     'Content-Type': 'application/json'
                 }
             })
             .then(response => {
                console.log("response: ", response);
                 if (response.status === 200) {
                     updateStatus(diffId, reviewStatus);
                     showMessage('操作成功', 'success');
                 } else {
                     throw new Error('请求失败，状态码: ' + response.status);
                 }
             })
             .catch(error => {
                 console.error('Review error:', error);
                 showMessage('操作失败: ' + error.message, 'error');
             })
             .finally(() => {
                 setLoading(diffId, false);
             });
         }
         
         // 批量review操作
         function batchReview(reviewStatus) {
             const selectedCheckboxes = document.querySelectorAll('.item-checkbox:checked');
             
             if (selectedCheckboxes.length === 0) {
                 showMessage('请先选择要操作的项目', 'warning');
                 return;
             }
             
             const statusText = ['重置状态', '标记为无问题', '标记为有问题'][reviewStatus];
             if (!confirm('确认要批量' + statusText + '吗？共选中了 ' + selectedCheckboxes.length + ' 个项目。')) {
                 return;
             }
             
             let completed = 0;
             let failed = 0;
             
             selectedCheckboxes.forEach(checkbox => {
                 const diffId = checkbox.value;
                 setLoading(diffId, true);
                 
                 let url = API_BASE + '?method=review&reviewer=' + encodeURIComponent(REVIEWER) + 
                          '&diff_id=' + diffId + '&review_status=' + reviewStatus;
                 
                 // 如果是标记有问题，添加表单数据
                 if (reviewStatus === 2) {
                     const issueDesc = document.getElementById('issue-desc-' + diffId)?.value || '';
                     const issueRisk = document.getElementById('issue-risk-' + diffId)?.value || '';
                     const issueSuggestion = document.getElementById('issue-suggestion-' + diffId)?.value || '';
                     
                     url += '&issue_desc=' + encodeURIComponent(issueDesc) + 
                          '&issue_risk=' + encodeURIComponent(issueRisk) + 
                          '&issue_suggestion=' + encodeURIComponent(issueSuggestion);
                 }
                 
                 fetch(ur)
                 fetch(url, {
                     method: 'GET',
                     headers: {
                         'Content-Type': 'application/json'
                     }
                 })
                 .then(response => {
                     if (response.status === 200) {
                         updateStatus(diffId, reviewStatus);
                         completed++;
                     } else {
                         throw new Error('请求失败，状态码: ' + response.status);
                     }
                 })
                 .catch(error => {
                     console.error('Batch review error for diff ' + diffId + ':', error);
                     failed++;
                 })
                 .finally(() => {
                     setLoading(diffId, false);
                     
                     // 检查是否所有请求都已完成
                     if (completed + failed === selectedCheckboxes.length) {
                         if (failed === 0) {
                             showMessage('批量操作完成，共处理 ' + completed + ' 个项目', 'success');
                         } else {
                             showMessage('批量操作完成，成功 ' + completed + ' 个，失败 ' + failed + ' 个', 'warning');
                         }
                         
                         // 取消全选
                         document.getElementById('selectAll').checked = false;
                         selectedCheckboxes.forEach(checkbox => {
                             checkbox.checked = false;
                         });
                     }
                 });
             });
         }
         
         // 显示消息提示
         function showMessage(message, type) {
             // 创建消息元素
             const messageDiv = document.createElement('div');
             messageDiv.className = 'message-toast message-' + type;
             messageDiv.textContent = message;
             
             // 添加样式
             const style = document.createElement('style');
             if (!document.querySelector('#message-toast-style')) {
                 style.id = 'message-toast-style';
                 style.textContent = 
                     '.message-toast { position: fixed; top: 20px; right: 20px; padding: 12px 20px; border-radius: 4px; color: white; font-weight: bold; z-index: 9999; animation: slideIn 0.3s ease-out; }' +
                     '.message-success { background-color: #28a745; }' +
                     '.message-error { background-color: #dc3545; }' +
                     '.message-warning { background-color: #ffc107; color: #212529; }' +
                     '@keyframes slideIn { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }'
                 document.head.appendChild(style);
             }
             
             // 添加到页面
             document.body.appendChild(messageDiv);
             
             // 3秒后自动移除
             setTimeout(() => {
                 if (messageDiv.parentNode) {
                     messageDiv.parentNode.removeChild(messageDiv);
                 }
             }, 3000);
         }
     </script>
 </body>
 </html>`

	var renderRecords []*RenderableSQLDiffRecord
	for _, record := range diffRecords {
		renderRecords = append(renderRecords,
			&RenderableSQLDiffRecord{
				SQLDiffRecord:       record,
				RenderedDiffSQLHTML: template.HTML(record.DiffSQLHTML),
			})
	}

	// 准备模板数据
	batches := make(map[string]bool)
	var pendingReviews, noIssueReviews, hasIssueReviews int

	for _, record := range diffRecords {
		if record.BatchID != "" {
			batches[record.BatchID] = true
		}

		// 统计review状态
		switch record.ReviewStatus {
		case 0: // 待review
			pendingReviews++
		case 1: // 无问题
			noIssueReviews++
		case 2: // 有问题
			hasIssueReviews++
		}
	}

	var firstRecord *entity.SQLDiffRecord
	if len(diffRecords) > 0 {
		firstRecord = diffRecords[0]
	}

	// apiBase := fmt.Sprintf("http://%v:%d/sqldata", conf.Cardsend.SelfHost, conf.Cardsend.SelfPort)
	apiBase := fmt.Sprintf("http://%v:%d/sqldata", "***********", 8057)

	data := map[string]any{
		"CommitURL":       commitURL,
		"SendUser":        sendUser,
		"APIBase":         apiBase,
		"GeneratedAt":     time.Now().Format("2006-01-02 15:04:05"),
		"Records":         renderRecords,
		"FirstRecord":     firstRecord,
		"TotalDiffs":      len(diffRecords),
		"TotalBatches":    len(batches),
		"PendingReviews":  pendingReviews,
		"NoIssueReviews":  noIssueReviews,
		"HasIssueReviews": hasIssueReviews,
	}

	// 解析并执行模板
	tmpl, err := template.New("diff_view").Parse(htmlTemplate)
	if err != nil {
		s.Context.SLog.Error("parse template error").SetErr(err).Print()
		return "", err
	}

	var result strings.Builder
	err = tmpl.Execute(&result, data)
	if err != nil {
		s.Context.SLog.Error("execute template error").SetErr(err).Print()
		return "", err
	}

	return result.String(), nil
}
