/*
* 主体代码由Easy生成，**后续update不会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: review业务封装
* 可根据业务自行扩展
 */
package service

import (
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/sqldata"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity/remote"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type ReviewService struct {
	easy.Service

	// 初始化Dao的位置，切记是指针类型，不需要初始化
	ReqDto           *dto.ReviewReqDto
	SQLDiffRecordDao *dao.SQLDiffRecordDao
}

type DiffSQLExtra struct {
	IssueDesc       string `json:"issue_desc"`
	IssueRisk       string `json:"issue_risk"`
	IssueSuggestion string `json:"issue_suggestion"`
	Reviewer        string `json:"reviewer"`
}

// 初始化 必须传入上下文
func NewReviewService(ctx *easy.Context) *ReviewService {
	service := &ReviewService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

func NewReviewService2(ctx *easy.Context) *ReviewService {
	return nil
}

// ReviewExec 处理review请求
func (s *ReviewService) ReviewExec(reqDto *dto.ReviewReqDto) (easy.ResDto, error) {
	resDto := &dto.ReviewResDto{}

	// 解析参数
	diffID := reqDto.DiffID
	reviewer := reqDto.Reviewer
	reviewStatus := reqDto.ReviewStatus

	s.Context.SLog.Info("review request").
		Set("diff_id", diffID).
		Set("reviewer", reviewer).
		Set("review_status", reviewStatus).
		Set("issue_desc", reqDto).
		Set("issue_risk", reqDto).
		Set("issue_suggestion", reqDto).Print()

	record, err := s.SQLDiffRecordDao.Get(diffID)
	if err != nil {
		s.Context.SLog.Error("get diff record error").Set("diff_id", diffID).SetErr(err).Print()
		return nil, fmt.Errorf("获取记录失败: %v", err)
	}

	if record == nil {
		s.Context.SLog.Error("diff record not found").Set("diff_id", diffID).Print()
		return nil, fmt.Errorf("记录不存在: %s", diffID)
	}

	// 更新review状态
	record.ReviewStatus = int32(reviewStatus)
	record.Reviewer = reviewer
	record.ReviewTime = time.Now().Format("2006-01-02 15:04:05")

	if reviewStatus == 2 {
		extra := &DiffSQLExtra{
			IssueDesc:       reqDto.IssueDesc,
			IssueRisk:       reqDto.IssueRisk,
			IssueSuggestion: reqDto.IssueSuggestion,
			Reviewer:        reqDto.Reviewer,
		}
		extraByte, err := json.Marshal(extra)
		if err != nil {
			s.Context.SLog.Error("marshal diff extra error").SetErr(err).Print()
			return nil, fmt.Errorf("marhsal diff extra failed: %w", err)
		}
		record.Extra = string(extraByte)

		// 非 2 状态也不用重置 extra, 避免误点击
	}

	// 保存到数据库
	_, err = s.SQLDiffRecordDao.Update(record)
	if err != nil {
		s.Context.SLog.Error("update diff record error").Set("diff_id", diffID).SetErr(err).Print()
		return nil, fmt.Errorf("更新记录失败: %v", err)
	}

	s.Context.SLog.Info("review success").
		Set("diff_id", diffID).
		Set("reviewer", reviewer).
		Set("review_status", reviewStatus).Print()

	// 如果是有问题的情况，调用RPC回调
	if reviewStatus == 2 {
		s.Context.SLog.Warning("diff has issue, need rpc callback").
			Set("diff_id", diffID).
			Set("reviewer", reviewer).Print()
	}

	if _, err := s.SQLDiffRecordDao.Update(record, "review_status", "reviewer", "review_time", "extra"); err != nil {
		s.SLog.Warning("update diff record error").Set("diff id", diffID).Print()
		return nil, err
	}

	url := fmt.Sprintf("http://%s:%d/sqldata?method=issueshow&id=%d", conf.Cardsend.SelfHost, conf.Cardsend.SelfPort, record.ID)

	if conf.Cardsend.SqldiffCallback.CallbackEnable {
		recevierList := []string{}
		recevierList = append(recevierList, conf.Cardsend.SqldiffCallback.Recevier...)
		if conf.Cardsend.SqldiffCallback.CallbackUserEnable {
			recevierList = append(recevierList, record.Rd)
		}

		isSucc := true
		for _, user := range recevierList {
			title := "SQL DIFF 异常通知，请您根据排查结果及时处理"
			body := &remote.CardSendPost{
				UserData: &remote.CardSendPostUserData{
					Title:       title,
					ServiceName: record.Repo,
					Desc:        title,
					Subtitle:    title,
					URL: &remote.CardSendPostUserDataURL{
						URL: &remote.CardSendPostUserDataURLURL{
							Type: "newUrl",
							Contents: &remote.CardSendPostUserDataURLURLContents{
								Title: "点击前往",

								Mobile: &remote.CardSendPostUserDataURLURLContentsMobile{
									OpenType: "app_container",
									URL:      url,
								},
								Pc: &remote.CardSendPostUserDataURLURLContentsPc{
									OpenType: "normal",
									URL:      url,
								},
							},
						},
					},
				},
				ReceiverID:   user,
				TemplateID:   "b84802d37a624255a044267fba988fd7",
				ReceiverType: "user",
				UserMsg:      false,
				AppName:      "easy_helper",
			}
			header := &remote.CardSendHeader{
				ContentType: "application/json",
			}

			cardSendClient := remote.NewCardSendClient(s.Context)
			if err := cardSendClient.Do(body, header); err != nil {
				s.SLog.Warning("nilaway send card error").Set("repo", record.Repo).Set("url", url).Print()
				isSucc = true
			}
		}
		if isSucc {
			s.SLog.Info("cardsend callback  success").Set("repo", record.Repo).Set("url", url).Print()
		}

	}

	return resDto, nil
}
