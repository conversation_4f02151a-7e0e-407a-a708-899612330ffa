/*
* 主体代码由Easy生成，**后续update不会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: stat业务封装
* 可根据业务自行扩展
 */
package service

import (
	"fmt"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/sqldata"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type StatService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化
	ReqDto			*dto.StatReqDto
	SQLDiffRecordDao	*dao.SQLDiffRecordDao
}

// 初始化 必须传入上下文
func NewStatService(ctx *easy.Context) *StatService {
	service := &StatService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}

	if err == easy.BindingError {
		return nil
	}
	return service
}

func (s *StatService) StatExec(reqDto *dto.StatReqDto) (easy.ResDto, error) {
	s.ReqDto = reqDto
	resDto := &dto.StatResDto{}

	start, err := time.Parse("2006-01-02", reqDto.Start)
	if err != nil {
		s.SLog.Error("parse start time error").SetErr(err).Print()
		return nil, err
	}
	end, err := time.Parse("2006-01-02", reqDto.End)
	if err != nil {
		s.SLog.Error("parse end time error").SetErr(err).Print()
		return nil, err
	}

	formattedStart := start.Format("2006-01-02 15:04:05")
	formattedEnd := end.Add(24 * time.Hour).Format("2006-01-02 15:04:05")

	diffRecords, err := s.SQLDiffRecordDao.ListByCreateTime(formattedStart, formattedEnd)
	if err != nil {
		s.SLog.Warning("list diff records error").SetErr(err).Print()
		return nil, err
	}

	m := make(map[string][]*entity.SQLDiffRecord)
	for _, diffRecord := range diffRecords {
		m[diffRecord.CommitURL] = append(m[diffRecord.CommitURL], diffRecord)
	}

	for commitURL, diffRecords := range m {
		data := s.buildDiffRecordsItem(commitURL, diffRecords)
		resDto.Data = append(resDto.Data, data)
	}

	return resDto, nil
}

func (s *StatService) buildDiffRecordsItem(commitURL string, diffRecords []*entity.SQLDiffRecord) *dto.StatResDtoData {
	var pendingReviews, noIssueReviews, hasIssueReviews int
	url := fmt.Sprintf("http://%s:%d/sqldata?method=diffview&commit_url=%s", conf.Cardsend.SelfHost, conf.Cardsend.SelfPort, commitURL)

	for _, record := range diffRecords {
		// 统计review状态
		switch record.ReviewStatus {
		case 0:	// 待review
			pendingReviews++
		case 1:	// 无问题
			noIssueReviews++
		case 2:	// 有问题
			hasIssueReviews++
		}
	}
	total := len(diffRecords)

	// 构建返回数据
	return &dto.StatResDtoData{
		URL:		url,
		Pending:	pendingReviews,
		NoIssue:	noIssueReviews,
		HasIssue:	hasIssueReviews,
		Total:		total,
	}
}
