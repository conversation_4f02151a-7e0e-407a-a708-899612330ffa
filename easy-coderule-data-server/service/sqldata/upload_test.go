/*
 * Author:  Easy
 * Version: 1.0.0
 * Description: upload业务的单元测试
 */
package service

import (
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_client/client_http/http_mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config/config_mock"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/conf"
)

// TestDiffViewTemplate 测试diffView模板生成功能
func TestDiffViewTemplate(t *testing.T) {
	// EchoMockRegistry(t)
	// s :=
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	// s.Redis.SetRedisConfName("")
	// 直接创建服务，不依赖框架初始化
	// service := NewUploadService(easy.NewContext())

	// 准备测试数据
	// testCommitURL := "https://github.com/test/repo/commit/abc123"

	/*
			// 创建测试记录
			testRecords := []*entity.SQLDiffRecord{
				{
					Repo:        "test-repo",
					Rd:          "test-rd",
					CommitURL:   testCommitURL,
					UnicKey:     "test_key_1",
					BatchID:     "batch_test_001",
					OldSQLText:  "SELECT * FROM users WHERE id = ?",
					NewSQLText:  "SELECT id, name, email FROM users WHERE id = ? AND status = 'active'",
					OldFilename: "user_service.go",
					NewFilename: "user_service.go",
					OldFnName:   "GetUser",
					NewFnName:   "GetActiveUser",
					OldKeyName:  "user_query",
					NewKeyName:  "active_user_query",
					OldType:     "select",
					NewType:     "select",
				},
				{
					Repo:        "test-repo",
					Rd:          "test-rd",
					CommitURL:   testCommitURL,
					UnicKey:     "test_key_2",
					BatchID:     "batch_test_001",
					OldSQLText:  "INSERT INTO logs (message) VALUES (?)",
					NewSQLText:  "INSERT INTO logs (message, level, created_at) VALUES (?, ?, NOW())",
					OldFilename: "logger.go",
					NewFilename: "logger.go",
					OldFnName:   "LogMessage",
					NewFnName:   "LogWithLevel",
					OldKeyName:  "log_insert",
					NewKeyName:  "log_insert_with_level",
					OldType:     "insert",
					NewType:     "insert",
				},
			}


		// 测试diffView方法
		htmlContent, err := service.diffView(testCommitURL, testRecords)
		t.Logf("########################")
		t.Logf("htmlContent: %s", htmlContent)
		t.Logf("########################")

		assert.Nil(t, err)
		assert.NotEmpty(t, htmlContent)

		// 验证HTML内容包含预期的信息
		assert.Contains(t, htmlContent, "SQL差异报告")
		assert.Contains(t, htmlContent, testCommitURL)
		assert.Contains(t, htmlContent, "test-repo")
		assert.Contains(t, htmlContent, "test-rd")
		assert.Contains(t, htmlContent, "test_key_1")
		assert.Contains(t, htmlContent, "test_key_2")
		assert.Contains(t, htmlContent, "GetUser")
		assert.Contains(t, htmlContent, "GetActiveUser")
		assert.Contains(t, htmlContent, "SELECT * FROM users WHERE id = ?")
		assert.Contains(t, htmlContent, "SELECT id, name, email FROM users WHERE id = ? AND status = 'active'")
		assert.Contains(t, htmlContent, "INSERT INTO logs (message) VALUES (?)")
		assert.Contains(t, htmlContent, "INSERT INTO logs (message, level, created_at) VALUES (?, ?, NOW())")
		assert.Contains(t, htmlContent, "batch_test_001")

		// 验证统计信息
		assert.Contains(t, htmlContent, "2") // 总差异数应该是2
		assert.Contains(t, htmlContent, "1") // 批次数应该是1

		// 验证HTML结构
		assert.Contains(t, htmlContent, "<!DOCTYPE html>")
		assert.Contains(t, htmlContent, "<html lang=\"zh-CN\">")
		assert.Contains(t, htmlContent, "查看提交详情")
		assert.Contains(t, htmlContent, "原始版本")
		assert.Contains(t, htmlContent, "新版本")

		t.Logf("Generated HTML content length: %d", len(htmlContent))

		// 输出实际的HTML内容片段用于验证
		t.Logf("HTML snippet: %s", htmlContent[:200])
	*/
}

// TestDiffViewEmptyTemplate 测试没有差异记录的模板
func TestDiffViewEmptyTemplate(t *testing.T) {
	// 直接创建服务
	service := &DiffviewService{}

	// 使用空记录
	testCommitURL := "https://github.com/test/repo/commit/nonexistent"
	testCommitUser := "gaojia"
	var emptyRecords []*entity.SQLDiffRecord

	// 测试diffView方法
	htmlContent, err := service.diffView(testCommitURL, testCommitUser, emptyRecords)
	assert.Nil(t, err)
	assert.NotEmpty(t, htmlContent)

	// 验证空状态信息
	assert.Contains(t, htmlContent, "暂无差异记录")
	assert.Contains(t, htmlContent, "该提交没有SQL差异记录")
	assert.Contains(t, htmlContent, testCommitURL)
	assert.Contains(t, htmlContent, "0") // 总差异数应该是0

	t.Logf("Empty result HTML content length: %d", len(htmlContent))
}

// TestDiffViewMultipleBatchesTemplate 测试多个批次的模板
func TestDiffViewMultipleBatchesTemplate(t *testing.T) {
	// 直接创建服务
	// service := &UploadService{}
	/*
		// 准备测试数据
		testCommitURL := "https://github.com/test/repo/commit/multi123"

		// 创建多个批次的测试记录
		timestamp1 := time.Now().Unix()
		timestamp2 := timestamp1 + 10

		testRecords := []*entity.SQLDiffRecord{
			{
				Repo:        "test-repo",
				Rd:          "test-rd",
				CommitURL:   testCommitURL,
				UnicKey:     "key_batch1_1",
				BatchID:     fmt.Sprintf("batch_%d_test-repo", timestamp1),
				OldSQLText:  "SELECT * FROM products",
				NewSQLText:  "SELECT id, name, price FROM products",
				OldFilename: "product.go",
				NewFilename: "product.go",
				OldFnName:   "GetProducts",
				NewFnName:   "GetProducts",
				OldKeyName:  "products",
				NewKeyName:  "products",
				OldType:     "select",
				NewType:     "select",
			},
			{
				Repo:        "test-repo",
				Rd:          "test-rd",
				CommitURL:   testCommitURL,
				UnicKey:     "key_batch2_1",
				BatchID:     fmt.Sprintf("batch_%d_test-repo", timestamp2),
				OldSQLText:  "DELETE FROM cache",
				NewSQLText:  "DELETE FROM cache WHERE expired = 1",
				OldFilename: "cache.go",
				NewFilename: "cache.go",
				OldFnName:   "ClearCache",
				NewFnName:   "ClearExpiredCache",
				OldKeyName:  "clear_cache",
				NewKeyName:  "clear_expired_cache",
				OldType:     "delete",
				NewType:     "delete",
			},
		}

		// 测试diffView方法
		htmlContent, err := service.DiffView(testCommitURL)
		assert.Nil(t, err)
		assert.NotEmpty(t, htmlContent)

		// 验证多批次信息
		assert.Contains(t, htmlContent, "2") // 总差异数应该是2
		assert.Contains(t, htmlContent, "2") // 批次数应该是2
		assert.Contains(t, htmlContent, "key_batch1_1")
		assert.Contains(t, htmlContent, "key_batch2_1")
		assert.Contains(t, htmlContent, fmt.Sprintf("batch_%d_test-repo", timestamp1))
		assert.Contains(t, htmlContent, fmt.Sprintf("batch_%d_test-repo", timestamp2))

	*/
	t.Logf("Multiple batches test completed successfully")
}

// TestDiffViewHTMLSafety 测试HTML安全性（防止XSS）
func TestDiffViewHTMLSafety(t *testing.T) {
	// 直接创建服务
	service := &DiffviewService{}

	// 准备包含特殊字符的测试数据
	testCommitURL := "https://github.com/test/repo/commit/xss123"

	testRecords := []*entity.SQLDiffRecord{
		{
			Repo:        "test-repo",
			Rd:          "test-rd",
			CommitURL:   testCommitURL,
			UnicKey:     "xss_test_key",
			BatchID:     "batch_xss_001",
			OldSQLText:  "SELECT * FROM users WHERE name = '<script>alert(\"old\")</script>'",
			NewSQLText:  "SELECT * FROM users WHERE name = '<script>alert(\"new\")</script>' AND id > 0",
			OldFilename: "test<script>.go",
			NewFilename: "test<script>.go",
			OldFnName:   "Test<Function>",
			NewFnName:   "Test<Function>New",
			OldKeyName:  "test_key_<xss>",
			NewKeyName:  "test_key_<xss>_new",
			OldType:     "select",
			NewType:     "select",
		},
	}

	// 测试diffView方法
	htmlContent, err := service.diffView(testCommitURL, testRecords)
	assert.Nil(t, err)
	assert.NotEmpty(t, htmlContent)

	// 验证特殊字符被正确处理
	assert.Contains(t, htmlContent, testCommitURL)
	assert.Contains(t, htmlContent, "xss_test_key")

	// 验证脚本标签没有被直接包含（应该被转义）
	scriptCount := strings.Count(htmlContent, "<script>")
	// 除了可能的CSS或其他合法用途，不应该有来自数据的script标签
	t.Logf("Script tag count in HTML: %d", scriptCount)

	t.Logf("HTML safety test completed")
}

// TestSendCard 测试sendCard函数
func TestSendCard(t *testing.T) {
	// 初始化测试环境
	EchoMockRegistry(t)

	config_mock.SetConfRootPath("../../conf")

	// 创建服务实例
	service := NewUploadService(easy.NewContext())
	assert.NotNil(t, service)

	// 模拟配置数据
	conf.Cardsend = &conf.CardsendConf{
		SelfHost: "test.example.com",
		SelfPort: 8080,
	}

	// Mock HTTP 驱动 - 模拟成功响应
	err := http_mock.Registry("CardSend", mockHTTPSuccessCardSend{host: "http://127.0.0.1:8057"})
	assert.Nil(t, err)

	// 测试参数
	testRepoName := "test-repo"
	testCommitURL := "https://github.com/test/repo/commit/abc123"
	testUserID := "zhongzhenyan"

	// 调用被测试的函数
	err = service.sendCard(testRepoName, testCommitURL, testUserID)
	t.Logf("######################## err:%v", err)
	// 验证结果
	assert.Nil(t, err)
	t.Logf("SendCard test passed for repo: %s, user: %s", testRepoName, testUserID)
}

// TestSendCardFailure 测试sendCard函数失败场景
func TestSendCardFailure(t *testing.T) {
	// 初始化测试环境
	EchoMockRegistry(t)
	config_mock.SetConfRootPath("../../conf")

	// 创建服务实例
	service := NewUploadService(easy.NewContext())
	assert.NotNil(t, service)

	// 模拟配置数据
	conf.Cardsend = &conf.CardsendConf{
		SelfHost: "test.example.com",
		SelfPort: 8080,
	}

	// Mock HTTP 驱动 - 模拟失败响应
	err := http_mock.Registry("CardSend", mockHTTPFailureCardSend{host: "http://127.0.0.1:8057"})
	assert.Nil(t, err)

	// 测试参数
	testRepoName := "test-repo"
	testCommitURL := "https://github.com/test/repo/commit/abc123"
	testUserID := "zhongzhenyan"

	// 调用被测试的函数
	err = service.sendCard(testRepoName, testCommitURL, testUserID)

	// 验证结果 - 应该返回错误
	assert.NotNil(t, err)
	t.Logf("SendCard failure test passed with expected error: %v", err)
}

// TestSendCardParameters 测试sendCard函数参数验证
func TestSendCardParameters(t *testing.T) {
	// 初始化测试环境
	EchoMockRegistry(t)
	config_mock.SetConfRootPath("../../conf")

	// 创建服务实例
	service := NewUploadService(easy.NewContext())
	assert.NotNil(t, service)

	// 模拟配置数据
	conf.Cardsend = &conf.CardsendConf{
		SelfHost: "***********",
		SelfPort: 80,
	}

	// Mock HTTP 驱动 - 验证参数的mock
	err := http_mock.Registry("CardSend", mockHTTPParameterValidationCardSend{host: "http://127.0.0.1:8057"})
	assert.Nil(t, err)

	testCases := []struct {
		name      string
		repoName  string
		commitURL string
		userID    string
		wantErr   bool
	}{
		{
			name:      "正常参数",
			repoName:  "test-repo",
			commitURL: "https://github.com/test/repo/commit/abc123",
			userID:    "zhong'zhen'yan",
			wantErr:   false,
		},
		{
			name:      "空仓库名",
			repoName:  "",
			commitURL: "https://github.com/test/repo/commit/abc123",
			userID:    "zhongzhenyan",
			wantErr:   false, // sendCard函数本身不验证空值，只是传递给卡片
		},
		{
			name:      "空用户ID",
			repoName:  "test-repo",
			commitURL: "https://github.com/test/repo/commit/abc123",
			userID:    "zhongzhenyan",
			wantErr:   false, // sendCard函数本身不验证空值
		},
		{
			name:      "特殊字符仓库名",
			repoName:  "test-repo-with-特殊字符",
			commitURL: "https://github.com/test/repo/commit/abc123",
			userID:    "zhongzhenyan",
			wantErr:   false,
		},
	}

	for i, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := service.sendCard(tc.repoName, tc.commitURL, tc.userID)
			t.Logf("######################## i:%d, err:%v", i, err)
			if tc.wantErr {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
			}
			t.Logf("Test case '%s' completed", tc.name)
		})
	}
}

// Mock HTTP 成功响应 - 模拟CardSend成功
type mockHTTPSuccessCardSend struct {
	host string
}

func (s mockHTTPSuccessCardSend) Do(request *http.Request) (*http.Response, error) {
	// 构建成功的响应
	responseBody := `{
		"status": 200,
		"errno": 0,
		"msg": "success",
		"data": {
			"receivers": [
				{
					"receiver_id": "test-user-123",
					"send_result": "success",
					"card_id": 12345,
					"msg_id": 67890
				}
			],
			"url": "test_url"
		}
	}`

	response := &http.Response{
		Status:     "200 OK",
		StatusCode: http.StatusOK,
		Proto:      "HTTP/1.1",
		ProtoMajor: 1,
		ProtoMinor: 1,
		Header:     http.Header{},
		Body:       io.NopCloser(strings.NewReader(responseBody)),
		Request:    request,
	}
	return response, nil
}

// Mock HTTP 失败响应 - 模拟CardSend失败
type mockHTTPFailureCardSend struct {
	host string
}

func (s mockHTTPFailureCardSend) Do(request *http.Request) (*http.Response, error) {
	// 模拟网络错误
	return nil, errors.New("network error: connection timeout")
}

// Mock HTTP 参数验证响应 - 验证请求参数
type mockHTTPParameterValidationCardSend struct {
	host string
}

func (s mockHTTPParameterValidationCardSend) Do(request *http.Request) (*http.Response, error) {
	// 读取请求体进行验证
	if request.Body != nil {
		bodyBytes, err := io.ReadAll(request.Body)
		if err == nil {
			bodyStr := string(bodyBytes)
			// 验证请求体包含预期的字段
			if strings.Contains(bodyStr, "template_id") &&
				strings.Contains(bodyStr, "receiver_id") &&
				strings.Contains(bodyStr, "app_name") {
				// 请求参数正确，返回成功响应
				responseBody := `{
					"status": 200,
					"errno": 0,
					"msg": "success",
					"data": {
						"receivers": [
							{
								"receiver_id": "test-user",
								"send_result": "success"
							}
						]
					}
				}`

				response := &http.Response{
					Status:     "200 OK",
					StatusCode: http.StatusOK,
					Proto:      "HTTP/1.1",
					ProtoMajor: 1,
					ProtoMinor: 1,
					Header:     http.Header{},
					Body:       io.NopCloser(strings.NewReader(responseBody)),
					Request:    request,
				}
				return response, nil
			}
		}
	}

	// 参数验证失败，返回错误响应
	responseBody := `{
		"status": 400,
		"errno": 1001,
		"msg": "invalid parameters"
	}`

	response := &http.Response{
		Status:     "400 Bad Request",
		StatusCode: http.StatusBadRequest,
		Proto:      "HTTP/1.1",
		ProtoMajor: 1,
		ProtoMinor: 1,
		Header:     http.Header{},
		Body:       io.NopCloser(strings.NewReader(responseBody)),
		Request:    request,
	}
	return response, nil
}

// 注册驱动
func EchoMockRegistry(t *testing.T) {
	var err error
	// 初始化配置文件,如果在根目录启动则不用设置
	config_mock.SetConfRootPath("./conf")
	err = easy.Init("")
	// assert.Nil(t, err)
	// 注册直连sql驱动
	err = mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_zhongzhenyan",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "zhongzhenyan"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_tangram_dump",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "tangram_dump"})
	assert.Nil(t, err)
	// 注册直连redis驱动
	err = mock.MockRedisDirectRegistry("cache-bj", "************:8841")
	assert.Nil(t, err)
	// 注册直连http驱动, 测试的host链接由业务指定
	// 示例:err = mock.MockHttpDirectRegistry("confName","http://localhost:8080")
	err = mock.MockHttpDirectRegistry("naclocal", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("nacmaster", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("easycode", "")
	assert.Nil(t, err)
}
