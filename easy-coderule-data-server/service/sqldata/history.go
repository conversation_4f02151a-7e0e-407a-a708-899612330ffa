/*
* 主体代码由Easy生成，**后续update不会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: history业务封装
* 可根据业务自行扩展
 */
package service

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/sqldata"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type HistoryService struct {
	easy.Service

	// 初始化Dao的位置，切记是指针类型，不需要初始化
	ReqDto *dto.HistoryReqDto

	SQLResultsDao *dao.SQLScanResultsDao
}

// 初始化 必须传入上下文
func NewHistoryService(ctx *easy.Context) *HistoryService {
	service := &HistoryService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

func (s *HistoryService) HistoryExec(reqDto *dto.HistoryReqDto) (easy.ResDto, error) {
	s.ReqDto = reqDto
	resDto := &dto.HistoryResDto{}

	// 参数验证
	if reqDto.CommitID == "" {
		s.Context.SLog.Warning("commit_id is empty").Print()
		return resDto, nil
	}

	// 从数据库查询历史SQL记录
	// 这里需要根据commit_id查询对应的SQL记录
	// 由于当前数据库结构中没有直接存储commit_id，我们需要通过commit_url来查询

	var sqlList []*entity.SQLScanResults
	var err error

	if reqDto.Repo != "" {
		// 如果提供了repo参数，按repo查询最新的SQL记录
		sqlList, err = s.SQLResultsDao.GetListByRepo(reqDto.Repo)
		if err != nil {
			s.Context.SLog.Error("get sql list by repo error").Set("repo", reqDto.Repo).SetErr(err).Print()
			return nil, err
		}
	} else {
		// 如果没有提供repo，尝试通过commit_id构造commit_url查询
		// 这里需要根据实际的URL格式来构造
		// 暂时返回空结果
		s.Context.SLog.Warning("repo not provided, cannot query historical SQL").Set("commit_id", reqDto.CommitID).Print()
		return resDto, nil
	}

	// 转换为响应格式
	var historyItems []*dto.HistoryResDtoItem
	for _, item := range sqlList {
		historyItem := &dto.HistoryResDtoItem{
			SQL:      item.SQLText,
			FileName: item.Filename,
			FnName:   item.FnName,
			Key:      item.KeyName,
			Type:     item.Type,
			Database: item.DBName,
			Instance: item.InstanceName,
		}
		historyItems = append(historyItems, historyItem)
	}

	resDto.Data = historyItems

	s.Context.SLog.Info("successfully retrieved historical SQL records").
		Set("commit_id", reqDto.CommitID).
		Set("repo", reqDto.Repo).
		Set("count", len(historyItems)).Print()

	return resDto, nil
}
