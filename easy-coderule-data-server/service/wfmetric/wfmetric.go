/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: wfmetric业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/wfmetric"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity/remote"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/commitutils"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/localcache"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/uuap"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type WfmetricService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化

	BmDao *dao.BehaviorMeasureDao
}

type UserInfoMsg struct {
	UserName   string `json:"username"`   // 用户名
	Manager    string `json:"manager"`    // 经理
	Department string `json:"department"` // 部门
}

const (
	NameTypeApp   = "app"
	NameTypeICode = "icode"
)

// 初始化 必须传入上下文
func NewWfmetricService(ctx *easy.Context) *WfmetricService {
	service := &WfmetricService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewWfmetricService 后都会走的逻辑
 */
func (t *WfmetricService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * add - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *WfmetricService) Add(req *dto.AddReqDto) (*dto.AddResDto, error) {
	res := &dto.AddResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	commitMsg := req.BodyDto.CommitMsg
	icafeID := commitutils.GetICafeID(commitMsg)
	var originInfo *entity.BehaviorMeasure
	var err error
	if icafeID == "" {
		// 没有 commit id 也打日志, 此时找 commit id 对应的记录进行合并
		originInfo, err = t.BmDao.QueryInfoExistByCommitID(req.BodyDto.CommitID, req.BodyDto.NameType, req.BodyDto.Name, req.BodyDto.Username)
		if err != nil {
			t.SLog.Warning("query info by commit id error").Set("commitID", req.BodyDto.CommitID).Set("name type", req.BodyDto.NameType).Set("name", req.BodyDto.Name).
				Set("username", req.BodyDto.Username).Set("origin info", originInfo).SetErr(err).Print()
			return nil, easy.CustomError(50002, err.Error(), 500)
		}
	} else {
		originInfo, err = t.BmDao.QueryInfoExistByIcafeID(icafeID, req.BodyDto.NameType, req.BodyDto.Name, req.BodyDto.Username)
		if err != nil {
			t.SLog.Warning("query info by icafe id error").Set("icafeid", icafeID).Set("name type", req.BodyDto.NameType).Set("name", req.BodyDto.Name).
				Set("username", req.BodyDto.Username).SetErr(err).Print()
			return nil, easy.CustomError(50003, err.Error(), 500)
		}
	}

	info := &entity.BehaviorMeasure{}

	if originInfo == nil {
		// 没有老数据，直接插入
		t.SLog.Warning("no old info").Set("info", originInfo).Print()
		info.ActiveTime = req.BodyDto.ActiveTime
		info.AppType = req.BodyDto.AppType
		info.CharacterCount = int32(req.BodyDto.CharacterCount)
		info.CommitMsg = commitMsg
		if req.BodyDto.IsWf {
			info.IsWf = 1
		} else {
			info.IsWf = 0
		}

		info.Name = req.BodyDto.Name
		info.NameType = int32(req.BodyDto.NameType)
		info.SendType = int32(req.BodyDto.SendType)
		info.CommitID = req.BodyDto.CommitID
		info.Username = req.BodyDto.Username
		unixTimestamp := time.Now().Unix()
		timeNow := time.Unix(unixTimestamp, 0).Format("2006-01-02 15:04:05")
		info.Ctime = timeNow
		info.Mtime = timeNow
		if _, err := t.BmDao.Add(info); err != nil {
			t.SLog.Warning("add info error").Set("info", info).SetErr(err).Print()
			return nil, err
		}
		// t.SLog.Warning("add info access").Set("info", info).SetErr(err).Print()
	} else {
		// t.SLog.Warning("has old info").Set("info", originInfo).Print()
		info.ActiveTime = originInfo.ActiveTime + req.BodyDto.ActiveTime
		info.CharacterCount = originInfo.CharacterCount + int32(req.BodyDto.CharacterCount)
		info.CommitMsg = commitMsg // 收集最后一次 commit msg 即可
		if req.BodyDto.IsWf {
			info.IsWf = 1
		} else {
			info.IsWf = 0
		}
		info.ID = originInfo.ID
		info.AppType = req.BodyDto.AppType
		info.Name = req.BodyDto.Name
		info.NameType = int32(req.BodyDto.NameType)
		info.SendType = int32(req.BodyDto.SendType)
		info.Username = req.BodyDto.Username
		info.CommitID = req.BodyDto.CommitID
		info.Ctime = originInfo.Ctime
		info.Mtime = time.Unix(time.Now().Unix(), 0).Format("2006-01-02 15:04:05")
		rowAffected, err := t.BmDao.UpdateInfo(info)
		if err != nil {
			// t.SLog.Warning("update info error").SetErr(err).Print()
			return nil, err
		} else if rowAffected == 0 {
			// t.SLog.Warning("update info fail").Set("rowAffected", rowAffected).SetErr(err).Print()
			return nil, errors.New("update info fail") // easy.CustomError(400, "update info fail", 400)
		} else {
			// t.SLog.Warning("update info access").SetErr(err).SetJSON("info", info).Print()
		}
	}

	return res, nil
}

func (t *WfmetricService) getUserInfoAndSetCache(userName string) (*UserInfoMsg, error) {
	valueType, err := localcache.GetByStr(userName)
	if err == nil {
		var userInfo = &UserInfoMsg{}
		err = json.Unmarshal(valueType, userInfo)

		if err == nil {
			t.SLog.Warning("match cache").Set("username", userName).Print()
			return userInfo, nil
		}
	}

	sRandom, err := uuap.UUIDGenerate()
	if err != nil {
		return nil, err
	}
	userManagerClient := remote.NewUserManagerClient(t.Context)
	body := &remote.UserManagerPost{}
	body.AppKey = uuap.ConstAppKey
	body.SRandom = sRandom
	body.ReturnFields = uuap.ConstReturnFieldSuperiorUsername
	timestamp := time.Now().Unix()
	sign, err := uuap.GenUUAPSign(timestamp, sRandom, userName, body.ReturnFields)
	body.Sign = sign
	body.Timestamp = fmt.Sprint(timestamp)
	body.Username = userName

	userMangerRes, err := userManagerClient.Do(body)
	if err != nil {
		t.SLog.Warning("get user info error").SetErr(err).Print()
		return nil, err
	}
	resbody := userManagerClient.Response.Body
	manager := userMangerRes.Result.Username
	t.SLog.Warning("user manager info").Set("username", manager).Set("resbody", string(resbody)).Print()

	userInfoBody := &remote.UserInfoPost{}
	userInfoBody.AppKey = uuap.ConstAppKey
	userInfoBody.SRandom = sRandom
	userInfoBody.ReturnFields = uuap.ConstReturnFieldsDepartment
	sign, err = uuap.GenUUAPSign(timestamp, sRandom, userName, userInfoBody.ReturnFields)
	if err != nil {
		t.SLog.Warning("gen sign error").SetErr(err).Print()
		return nil, err
	}

	userInfoBody.Sign = sign
	userInfoBody.Timestamp = fmt.Sprint(timestamp)
	userInfoBody.Username = userName

	userInfoClient := remote.NewUserInfoClient(t.Context)
	ufcInfoRes, err := userInfoClient.Do(userInfoBody)
	if err != nil {
		t.SLog.Warning("get user info error").Set("username", userInfoBody.Username).SetErr(err).Print()
		return nil, err
	}

	departmentName := ufcInfoRes.Result.DepartmentName

	userInfo := &UserInfoMsg{
		Manager:    manager,
		Department: departmentName,
		UserName:   userName}

	userInfoByte, err := json.Marshal(userInfo)
	if err != nil {
		t.SLog.Warning("marshal user info error").SetErr(err).Print()
		return nil, err
	}

	if err := localcache.SetByStr(userName, userInfoByte); err != nil {
		t.SLog.Warning("set cache error").SetErr(err).Print()
		return nil, err
	}

	return userInfo, nil
}

/*
 * query - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *WfmetricService) Query(req *dto.QueryReqDto) ([]*dto.QueryResDto, error) {
	res := []*dto.QueryResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response

	layoutInput := "2006-01-02"           // 输入的日期格式
	layoutOutput := "2006-01-02 15:04:05" // SQL TIMESTAMP 格式

	// 解析日期字符串
	parsedTime, err := time.Parse(layoutInput, req.Start)
	if err != nil {
		t.SLog.Warning("parse time error").Set("start", req.Start).SetErr(err).Print()
		return nil, err
	}
	parsedTimeEnd, err := time.Parse(layoutInput, req.End)
	if err != nil {
		t.SLog.Warning("parse time error").Set("end", req.End).SetErr(err).Print()
		return nil, err
	}
	// 转换为 SQL TIMESTAMP 格式字符串
	timeStart := parsedTime.Format(layoutOutput)
	timeEnd := parsedTimeEnd.Format(layoutOutput)
	list, err := t.BmDao.ListInfos(timeStart, timeEnd)
	if err != nil {
		t.SLog.Warning("query info error").SetErr(err).Print()
		return nil, err
	}

	for _, item := range list {
		userName := item.Username
		userInfoMsg, err := t.getUserInfoAndSetCache(userName)
		if err != nil {
			t.SLog.Warning("get user info error").SetErr(err).Print()
			return nil, err
		}

		resItem := &dto.QueryResDto{}
		resItem.Username = userName
		resItem.ActiveTime = item.ActiveTime
		resItem.CharacterCount = item.CharacterCount
		resItem.CommitID = item.CommitID
		resItem.IcafeID = item.IcafeID
		if item.IsWf == 1 {
			resItem.IsWf = true
		} else {
			resItem.IsWf = false
		}
		resItem.Manager = userInfoMsg.Manager
		resItem.Team = userInfoMsg.Department

		if item.NameType == 1 {
			resItem.NameType = "app"
			resItem.Name = item.Name
		} else {
			resItem.NameType = "icode"
			resItem.Name = item.Name
		}
		resItem.Username = item.Username

		res = append(res, resItem)
	}

	return res, nil
}
