/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: WfmetricService单元测试
 */
package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config/config_mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/wfmetric"
)

// add接口业务测试入口
func TestWfmetricAdd(t *testing.T) {
	// 注册驱动
	WfmetricMockRegistry(t)
	s := NewWfmetricService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.AddReqDto{}
	// 设置reqDto参数 测试数据

	resDto, errDto := s.Add(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// 注册驱动
func WfmetricMockRegistry(t *testing.T) {
	var err error
	// 初始化配置文件,如果在根目录启动则不用设置
	config_mock.SetConfRootPath("../conf")
	err = easy.Init("")
	assert.Nil(t, err)
	// 注册直连sql驱动
	err = mock.MockSqlDirectRegistry("mysql_cloud_zhongzhenyan",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "zhongzhenyan"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_tangram_dump",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "tangram_dump"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_ai_netdisk",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "ai_netdisk"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_ai_trans",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "ai_trans"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_anti_scene",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "anti_scene"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_global_pass_session",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "global_pass_session"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_aifairy",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "aifairy"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_aiconvert",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "aiconvert"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_deepcopy",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "deepcopy"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_global_exp",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "global_exp"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_anti_online",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "anti_online"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_CL_Notice",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "CL_Notice"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_ant_cdn",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "ant_cdn"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_anti_risk_center",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "anti_risk_center"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_aipet",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "aipet"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_aipic",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "aipic"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_baidu_dba",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "baidu_dba"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_gpttest",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "gpttest"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_global_ad",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "global_ad"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_corpus",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "corpus"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_easydb",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "easydb"})
	assert.Nil(t, err)
	err = mock.MockSqlDirectRegistry("mysql_cloud_activity_ljs_test",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "activity_ljs_test"})
	assert.Nil(t, err)
	// 注册直连redis驱动
	err = mock.MockRedisDirectRegistry("cache-bj", "************:8841")
	assert.Nil(t, err)
	// 注册直连http驱动, 测试的host链接由业务指定
	// 示例:err = mock.MockHttpDirectRegistry("confName","http://localhost:8080")
	err = mock.MockHttpDirectRegistry("naclocal", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("nacmaster", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("easycode", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("feStatistics", "")
	assert.Nil(t, err)
}

// query接口业务测试入口
func TestWfmetricQuery(t *testing.T) {
	// 注册驱动
	WfmetricMockRegistry(t)
	s := NewWfmetricService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.QueryReqDto{}
	// 设置reqDto参数 测试数据

	resDto, errDto := s.Query(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}
