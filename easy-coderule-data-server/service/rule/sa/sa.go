/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: sa业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"encoding/json"
	"fmt"
	"html"
	"os"
	"regexp"
	"strings"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/rule/sa"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type SaService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化
	SaRuleInfoDao *dao.SaRuleInfoDao
}

// 初始化 必须传入上下文
func NewSaService(ctx *easy.Context) *SaService {
	service := &SaService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.SLog.Error("service injection error").SetErr(err).Print()
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewSaService 后都会走的逻辑
 */
func (t *SaService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * query - 接口业务核心逻辑，返回HTML格式的数据
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *SaService) Query(req *dto.QueryReqDto) (string, error) {
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response
	info, err := t.SaRuleInfoDao.GetByInfo(req.Icode, req.RuleName)
	if err != nil {
		t.SLog.Warning("get info err").SetErr(err).Print()
		return "", err
	}

	// 生成HTML内容
	htmlContent := t.generateHTML(info)

	return htmlContent, nil
}

/*
 * listIcode - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *SaService) ListIcode(req *dto.ListIcodeReqDto) (*dto.ListIcodeResDto, error) {
	res := &dto.ListIcodeResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response
	list, err := t.SaRuleInfoDao.ListApp()
	if err != nil {
		t.SLog.Warning("list app err").SetErr(err).Print()
		return nil, err
	}
	for _, item := range list {
		res.List = append(res.List, &dto.ListIcodeResDtoList{
			Icode: item.Repo,
			App:   item.App,
		})
	}
	return res, nil
}

func (t *SaService) InsertData() error {
	resultByte, err := os.ReadFile("result.json")
	if err != nil {
		return err
	}
	var result []AppInfo
	if err := json.Unmarshal(resultByte, &result); err != nil {
		return err
	}

	d := dao.NewSaRuleInfoDao(t.Context)

	repoMap := make(map[string]struct{})
	var list []*entity.SaRuleInfo
	for _, item := range result {
		if _, ok := repoMap[item.Icode]; ok {
			continue
		}
		repoMap[item.Icode] = struct{}{}
		list = append(list, &entity.SaRuleInfo{
			Repo: item.Icode,
			App:  item.AppName,
			Cnt:  int32(item.Cnt),
			Data: item.Text,
			Rule: "recover",
		})
	}

	if err := d.AddList(list); err != nil {
		return err
	}
	return nil

}

type AppInfo struct {
	AppName string
	Icode   string
	Cnt     int
	Text    string
	Rule    string
}

/*
 * generateHTML - 生成HTML格式的数据展示
 * PARAMS:
 *    info 数据库查询结果
 * RETURNS:
 *    string HTML格式的内容
 */
func (t *SaService) generateHTML(info *entity.SaRuleInfo) string {
	if info == nil {
		return "<html><body><h1>No Data Found</h1></body></html>"
	}

	// 解析JSON数据并生成结构化的HTML内容
	ruleDataHTML := t.parseRuleDataToHTML(info.Data)

	htmlTemplate := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SA Rule Info</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
            color: #333;
        }
        .info-line {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .click-hint {
            background-color: #e3f2fd;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 13px;
            color: #1976d2;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .rule-item {
            background-color: #f8f9fa;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #dc3545;
        }
        .rule-path {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            font-size: 15px;
        }
        .rule-type {
            font-size: 14px;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 10px;
        }
        .issue-item {
            background-color: white;
            margin: 6px 0;
            padding: 10px 15px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease;
        }
        .issue-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .issue-position {
            font-family: 'Courier New', monospace;
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            display: block;
            margin-bottom: 5px;
        }
        .issue-position:hover {
            text-decoration: underline;
            color: #0056b3;
        }
        .issue-message {
            color: #6c757d;
            font-size: 13px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SA Rule Information</h1>
            <div class="info-line"><strong>Repository:</strong> %s</div>
            <div class="info-line"><strong>App:</strong> %s</div>
            <div class="info-line"><strong>Rule:</strong> %s</div>
            <div class="info-line"><strong>Total Issues:</strong> %d</div>
        </div>

        <div class="click-hint">
            💡 Click on file positions below to jump to iCode
        </div>

        %s
    </div>
</body>
</html>`

	return fmt.Sprintf(htmlTemplate,
		html.EscapeString(info.Repo), // repository
		html.EscapeString(info.App),  // app
		html.EscapeString(info.Rule), // rule
		info.Cnt,                     // count
		ruleDataHTML,                 // parsed rule data HTML
	)
}

/*
 * parseRuleDataToHTML - 解析JSON格式的规则数据并生成HTML
 * PARAMS:
 *    data JSON格式的规则数据
 * RETURNS:
 *    string HTML格式的内容
 */
func (t *SaService) parseRuleDataToHTML(data string) string {
	if data == "" {
		return "<p>No rule data available</p>"
	}

	// 解析JSON数据为通用的map结构
	var ruleData map[string]map[string]interface{}
	if err := json.Unmarshal([]byte(data), &ruleData); err != nil {
		// 如果解析失败，返回原始数据
		return fmt.Sprintf("<pre>%s</pre>", html.EscapeString(data))
	}

	var htmlBuilder strings.Builder
	hasValidIssues := false

	// 遍历每个路径
	for path, rules := range ruleData {
		pathHasIssues := false
		var pathHTML strings.Builder

		// 遍历每个规则类型
		for ruleType, ruleValue := range rules {
			// 检查ruleValue的类型
			switch v := ruleValue.(type) {
			case []interface{}:
				// 这是问题数组
				if len(v) > 0 {
					if !pathHasIssues {
						pathHTML.WriteString(fmt.Sprintf(`<div class="rule-item">
							<div class="rule-path">%s</div>`, html.EscapeString(path)))
						pathHasIssues = true
					}

					pathHTML.WriteString(fmt.Sprintf(`<div class="rule-type">%s (%d issues)</div>`,
						html.EscapeString(ruleType), len(v)))

					// 遍历每个问题
					for _, issueInterface := range v {
						if issue, ok := issueInterface.(map[string]interface{}); ok {
							posn, _ := issue["posn"].(string)
							message, _ := issue["message"].(string)

							// 处理posn，移除前缀并生成链接
							cleanPosn, linkURL := t.processPosition(posn)

							pathHTML.WriteString(`<div class="issue-item">`)

							if linkURL != "" {
								pathHTML.WriteString(fmt.Sprintf(`<a href="%s" target="_blank" class="issue-position">%s</a>`,
									linkURL, html.EscapeString(cleanPosn)))
							} else {
								pathHTML.WriteString(fmt.Sprintf(`<span class="issue-position">%s</span>`,
									html.EscapeString(cleanPosn)))
							}

							pathHTML.WriteString(fmt.Sprintf(`<div class="issue-message">%s</div>
							</div>`, html.EscapeString(message)))
						}
					}
				}
			case map[string]interface{}:
				// 这可能是错误信息，检查是否包含error字段
				if errorMsg, hasError := v["error"].(string); hasError {
					// 跳过错误信息，不显示
					t.SLog.Info(fmt.Sprintf("Skipping error entry for path: %s, ruleType: %s, error: %s", path, ruleType, errorMsg))
					continue
				}
			default:
				// 其他类型，跳过
				continue
			}
		}

		if pathHasIssues {
			pathHTML.WriteString("</div>")
			htmlBuilder.WriteString(pathHTML.String())
			hasValidIssues = true
		}
	}

	if !hasValidIssues {
		return "<p>No issues found</p>"
	}

	return htmlBuilder.String()
}

/*
 * processPosition - 处理位置信息，移除前缀并生成iCode链接
 * PARAMS:
 *    posn 原始位置信息
 * RETURNS:
 *    cleanPosn 清理后的位置信息
 *    linkURL iCode链接URL
 */
func (t *SaService) processPosition(posn string) (string, string) {
	if posn == "" {
		return "", ""
	}

	// 移除前缀 /home/<USER>/gocheck/newnewnewcode/
	prefix := "/home/<USER>/gocheck/newnewnewcode/"
	cleanPosn := posn
	if strings.HasPrefix(posn, prefix) {
		cleanPosn = posn[len(prefix):]
	}

	// 解析路径和行号
	// 格式: baidu/netdisk/poms-meta-tangram/lib/redis/redis.go:291:3
	re := regexp.MustCompile(`^([^:]+):(\d+):(\d+)$`)
	matches := re.FindStringSubmatch(cleanPosn)

	if len(matches) != 4 {
		return cleanPosn, ""
	}

	fullPath := matches[1]
	lineNum := matches[2]

	// 解析仓库路径和文件路径
	// fullPath格式: baidu/netdisk/poms-meta-tangram/lib/redis/redis.go
	// 需要分离出: 仓库路径(baidu/netdisk/poms-meta-tangram) 和 文件路径(lib/redis/redis.go)
	pathParts := strings.Split(fullPath, "/")
	if len(pathParts) < 4 {
		return cleanPosn, ""
	}

	// 前三部分是仓库路径: baidu/netdisk/poms-meta-tangram
	repoPath := strings.Join(pathParts[:3], "/")
	// 剩余部分是文件路径: lib/redis/redis.go
	filePath := strings.Join(pathParts[3:], "/")

	// 生成iCode链接
	// https://console.cloud.baidu-int.com/devops/icode/repos/baidu/netdisk/poms-meta-tangram/blob/master/lib/redis/redis.go#L291
	linkURL := fmt.Sprintf("https://console.cloud.baidu-int.com/devops/icode/repos/%s/blob/master/%s#L%s",
		repoPath, filePath, lineNum)

	return cleanPosn, linkURL
}
