/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: codemetric业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"fmt"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/codemetric"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/lib/errs"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type CodemetricService struct {
	easy.Service

	// 初始化Dao的位置，切记是指针类型，不需要初始化
	EasyRepoTotalIncreRecordDao	*dao.EasyRepoTotalIncreRecordDao

	EasyLastRecordDao	*dao.EasyLastRecordDao
	EasyRepoTotalRecordDao	*dao.EasyRepoTotalRecordDao
	EasyFileexistRecordDao	*dao.EasyFileexistRecordDao
}

// 初始化 必须传入上下文
func NewCodemetricService(ctx *easy.Context) *CodemetricService {
	service := &CodemetricService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.Logger().Error("[service injection error] [err:%+v]", err)
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * add - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *CodemetricService) Add(req *dto.AddReqDto) (*dto.AddResDto, error) {
	res := &dto.AddResDto{}
	// 编写业务代码组装Response

	dbName := "mysql_cloud_netdisk_easy_coderule"
	err := t.Transaction.ExecWithTx(dbName, nil, func(tx *easy.Tx) error {
		// 存全量总行数
		if err := t.addTotalRecordWithTx(req, tx); err != nil {
			t.SLog.Error("add total record fail").SetErr(err).Print()
			return errs.AppDBInsertFailed
		}

		// 存增量总行数
		if err := t.addTotalIncreRecordWithTx(req, tx); err != nil {
			t.SLog.Error("add total incre record fail").SetErr(err).Print()
			return errs.AppDBInsertFailed
		}

		// 存最后一次提交时间 & 最后一次提交的 username 和 commitID
		if err := t.addOrUpdateLastTimeRecordWithTx(req, tx); err != nil {
			t.SLog.Error("add last time record fail").SetErr(err).Print()
			return errs.AppDBInsertFailed
		}
		return nil
	})
	if err != nil {
		t.SLog.Info("add record with tx failed").SetErr(err).Print()
		return nil, err
	}

	t.SLog.Info("add records success").Set("repo_name", req.BodyDto.RepoName).Set("create_at", req.BodyDto.CreateAt).Print()

	// 返回Response
	return res, nil
}

func (t *CodemetricService) Fileexist(req *dto.FileexistReqDto) (*dto.FileexistResDto, error) {
	res := &dto.FileexistResDto{}
	// 编写业务代码组装Response

	res.FileName = req.BodyDto.FileName
	res.RepoName = req.BodyDto.RepoName

	ok, err := t.EasyFileexistRecordDao.FileIsExist(req.BodyDto.RepoName, req.BodyDto.FileName)
	if err != nil {
		res.Exist = false
		t.SLog.Error("api: fileIsExist err").SetErr(err).Print()
		return res, err
	}

	res.Exist = ok
	// 返回Response
	return res, nil
}

func (t *CodemetricService) addTotalRecordWithTx(req *dto.AddReqDto, tx *easy.Tx) error {
	body := req.BodyDto

	repoTotal := entity.EasyRepoTotalRecord{}
	repoTotal.RepoName = body.RepoName
	repoTotal.CreateAt = body.CreateAt
	repoTotal.EasyTotalCnt = body.EasyTotalCnt
	repoTotal.UserTotalCnt = body.UserTotalCnt
	repoTotal.CommitURL = body.CommitURL

	// ui
	repoTotal.EasyUnitTestTotalCnt = body.EasyUtTotalCnt
	repoTotal.UserUnitTestTotalCnt = body.UserUtTotalCnt

	lastInsertID, err := tx.Model(&repoTotal).Create()
	if err != nil {
		t.SLog.Error("db insert total record failed").Set("lastInsertID", lastInsertID).SetErr(err).Print()
		return errs.AppDBInsertFailed
	}

	if lastInsertID < 0 {
		t.SLog.Error("db insert total record failed").Set("lastInsertID", lastInsertID).Print()
		return errs.AppDBInsertFailed
	}
	return nil
}

func (t *CodemetricService) addTotalIncreRecordWithTx(req *dto.AddReqDto, tx *easy.Tx) error {
	body := req.BodyDto

	repoIncreTotal := entity.EasyRepoTotalIncreRecord{}
	repoIncreTotal.RepoName = body.RepoName
	repoIncreTotal.CreateAt = body.CreateAt
	repoIncreTotal.EasyTotalCnt = body.IncreEasyTotalCnt
	repoIncreTotal.UserTotalCnt = body.IncreUserTotalCnt
	repoIncreTotal.CommitID = body.CommitID
	repoIncreTotal.BaseCommitID = body.BaseCommitID
	repoIncreTotal.CommitUser = body.CommitUser
	repoIncreTotal.CommitURL = body.CommitURL

	// ui
	repoIncreTotal.EasyUnitTestTotalCnt = body.IncreEasyUtTotalCnt
	repoIncreTotal.UserUnitTestTotalCnt = body.IncreUserUtTotalCnt

	lastInsertID, err := tx.Model(&repoIncreTotal).Create()
	if err != nil {
		t.SLog.Error("db insert total incre record failed").Set("lastInsertID", lastInsertID).SetErr(err).Print()
		return errs.AppDBInsertFailed
	}

	if lastInsertID < 0 {
		t.SLog.Error("db insert total incre record failed").Set("lastInsertID", lastInsertID).Print()
		return errs.AppDBInsertFailed
	}
	return nil
}

// 加半托管的文件名称
func (t *CodemetricService) addFileNameWithTx(req *dto.AddReqDto) error {
	fileData := req.BodyDto.FileData
	var fileList []*entity.EasyFileexistRecord
	for _, item := range fileData {
		entityItem := &entity.EasyFileexistRecord{}
		entityItem.RepoName = req.BodyDto.RepoName
		entityItem.ExistFilename = item.FileName
		fileList = append(fileList, entityItem)
	}
	if len(fileList) == 0 {
		return nil
	}
	if err := t.EasyFileexistRecordDao.AddList(fileList); err != nil {
		t.SLog.Error("api: addFileNameWithTx err").SetErr(err).Print()
		return err
	}

	return nil
}

func (t *CodemetricService) addOrUpdateLastTimeRecordWithTx(req *dto.AddReqDto, tx *easy.Tx) error {
	body := req.BodyDto

	curRecord := &entity.EasyLastRecord{}
	curRecord.RepoName = body.RepoName
	curRecord.LastTime = body.CreateAt
	curRecord.CommitUser = body.CommitUser
	curRecord.CommitID = body.CommitID
	curRecord.CommitURL = body.CommitURL

	// 数据库里面设置了UNIQUE KEY
	cnt, err := tx.Model(&entity.EasyLastRecord{}).Where("repo_name = ?", curRecord.RepoName).Count()
	if err != nil {
		t.SLog.Error("db count lasttime record failed").Print()
		return errs.AppDBQueryFailed
	}

	if cnt == 0 {
		// 插入一条数据
		lastInsertID, err := tx.Model(curRecord).Create()
		if err != nil {
			t.SLog.Error("db insert last time record failed").Set("repoName", body.RepoName).Set("lastInsertID", lastInsertID).SetErr(err).Print()
			return errs.AppDBInsertFailed
		}

		if lastInsertID < 0 {
			t.SLog.Error("db insert last time record failed").Set("repoName", body.RepoName).Set("lastInsertID", lastInsertID).SetErr(err).Print()
			return errs.AppDBInsertFailed
		}
	} else {
		// 更新一条数据
		rowsAffected, err := tx.Model(curRecord).Where("repo_name = ?", curRecord.RepoName).Update("last_time", "commit_id", "commit_user")
		if err != nil {
			t.SLog.Error("db update last time record failed").Set("repoName", body.RepoName).Set("lastTime", curRecord.LastTime).SetErr(err).Print()
			return errs.AppDBUpdateFailed
		}

		if rowsAffected < 0 {
			t.SLog.Error("db update last time record failed").Set("repoName", body.RepoName).Set("rowsAffected", rowsAffected).Print()
			return errs.AppDBUpdateFailed
		}

		t.SLog.Info("db update last time record success").Set("repoName", body.RepoName).Set("rowsAffected", rowsAffected).Print()
	}

	return nil
}

/*
 * get - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *CodemetricService) Get(req *dto.GetReqDto) (*dto.GetResDto, error) {
	res := &dto.GetResDto{}
	// 编写业务代码组装Response
	res.RepoName = req.Repo

	lastTimeRecord, err := t.EasyLastRecordDao.Get(res.RepoName)
	if err != nil {
		t.SLog.Error("get the last record faild").Set("repoName", res.RepoName).SetErr(err).Print()
		return nil, errs.AppDBQueryFailed
	}

	lastTime := lastTimeRecord.LastTime

	// 根据lastTime查询总行数
	totalRecordList, err := t.EasyRepoTotalRecordDao.List(lastTime)
	if err != nil {
		t.SLog.Error("get the total record list faield").Set("time", lastTime).SetErr(err).Print()
		return nil, errs.AppDBQueryFailed
	}
	if len(totalRecordList) == 0 {
		t.SLog.Error("the  total record len is 0").Set("repoName", req.Repo).Set("time", lastTime).Print()
		return nil, errs.AppDBQueryFailed
	}
	var targetRecord *entity.EasyRepoTotalRecord
	for _, totalRecord := range totalRecordList {
		if totalRecord.RepoName == req.Repo {
			targetRecord = totalRecord
			break
		}
	}
	if targetRecord == nil {
		t.SLog.Error("the target total record is not found").Set("repoName", req.Repo).Set("time", lastTime).Print()
		return nil, errs.AppDBQueryFailed
	}
	//  设置总行数
	res.UserTotalCnt = targetRecord.UserTotalCnt
	res.EasyTotalCnt = targetRecord.EasyTotalCnt

	// 返回Response
	return res, nil
}

// 提供所有incre信息供 QA 进行筛选
func (t *CodemetricService) Incre(req *dto.IncreReqDto) (*dto.IncreResDto, error) {
	res := &dto.IncreResDto{}
	// 编写业务代码组装Response

	start := req.StartTime
	end := req.EndTime

	list, err := t.EasyRepoTotalIncreRecordDao.List(start, end)
	if err != nil {
		t.SLog.Error("api: totalIncreRecordDao err").SetErr(err).Print()
		return nil, err
	}
	list = filtRecords(list)
	for _, item := range list {
		tmpData := &dto.IncreResDtoData{}

		tmpData.RepoName = item.RepoName
		tmpData.CommitID = item.CommitID
		tmpData.BaseCommitID = item.BaseCommitID
		tmpData.Language = "golang"
		tmpData.CommitUser = item.CommitUser
		tmpData.UserTotalCnt = item.UserTotalCnt
		tmpData.EasyTotalCnt = item.EasyTotalCnt
		tmpData.CreateAt = item.CreateAt
		res.Data = append(res.Data, tmpData)
	}

	// 返回Response
	return res, nil
}

// 返回最后一次提交的数据
func (t *CodemetricService) Listall(req *dto.ListallReqDto) (*dto.ListallResDto, error) {
	res := &dto.ListallResDto{}
	// 编写业务代码组装Response

	list, err := t.EasyLastRecordDao.List()
	if err != nil {
		t.SLog.Error("list last record failed").SetErr(err).Print()
		return nil, err
	}

	for _, item := range list {
		records, err := t.EasyRepoTotalRecordDao.List(item.LastTime)
		if err != nil {
			t.SLog.Error("list total record failed").SetErr(err).Print()
			return nil, err
		}
		var targetRecord *entity.EasyRepoTotalRecord
		for _, recordItem := range records {
			if recordItem.RepoName == item.RepoName {
				targetRecord = recordItem
				break
			}
		}

		if targetRecord == nil {
			t.SLog.Error("repo record not exist").Print()
			continue
		}

		tmpData := &dto.ListallResDtoData{}
		tmpData.RepoName = item.RepoName
		tmpData.CommitID = item.CommitID
		tmpData.CommitUser = item.CommitUser
		tmpData.LastCommitTime = item.LastTime
		tmpData.EasyCodeCnt = targetRecord.EasyTotalCnt
		tmpData.UserCodeCnt = targetRecord.UserTotalCnt

		res.Data = append(res.Data, tmpData)
	}

	// 返回Response
	return res, nil
}

func (t *CodemetricService) Addhalffile(req *dto.AddhalffileReqDto) (*dto.AddhalffileResDto, error) {
	res := &dto.AddhalffileResDto{}
	// 编写业务代码组装Response
	body := req.BodyDto
	repoName := body.RepoName
	fileNames := body.FileName
	if len(body.FileName) == 0 {
		res.Errno = "200"
		res.ErrMsg = "success"
		return res, nil
	}
	var infos []*entity.EasyFileexistRecord
	for _, fileName := range fileNames {
		info := &entity.EasyFileexistRecord{
			RepoName:	repoName,
			ExistFilename:	fileName,
		}
		infos = append(infos, info)
	}

	if err := t.EasyFileexistRecordDao.AddList(infos); err == nil {
		res.Errno = "200"
		res.ErrMsg = "success"
		return res, nil
	}

	var errRepo string
	for _, info := range infos {
		exist, err := t.EasyFileexistRecordDao.FileIsExist(info.RepoName, info.ExistFilename)
		if err != nil {
			t.SLog.Error("check file is exist failed").SetErr(err).Print()
			continue
		}

		if !exist {
			_, err := t.EasyFileexistRecordDao.Add(info)
			if err != nil {
				t.SLog.Error("add file failed").Set("repo", info.RepoName).Set("file", info.ExistFilename).SetErr(err).Print()
				errRepo += info.ExistFilename + "; "
			}
		}
	}

	if errRepo != "" {
		res.Errno = "50001"
		res.ErrMsg = fmt.Sprintf("repo: %s, filelist add failed: %s", body.RepoName, errRepo)
		return res, nil
	}

	res.Errno = "200"
	res.ErrMsg = "success"

	return res, nil
}

func filtRecords(records []*entity.EasyRepoTotalIncreRecord) []*entity.EasyRepoTotalIncreRecord {
	recordMap := map[string]*entity.EasyRepoTotalIncreRecord{}
	for _, record := range records {
		uniqueKey := genCommitUniqueKey(record.RepoName, record.CommitURL)
		if preRecord, ok := recordMap[uniqueKey]; !ok {
			recordMap[uniqueKey] = record
		} else {
			// 比较哪个时间早
			if record.CreateAt < preRecord.CreateAt {
				recordMap[uniqueKey] = record
			}
		}
	}

	var res []*entity.EasyRepoTotalIncreRecord
	for _, record := range recordMap {
		res = append(res, record)
	}
	return res
}

func genCommitUniqueKey(repo string, commitURL string) string {
	return repo + "-" + commitURL
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewCodemetricService 后都会走的逻辑
 */
func (t *CodemetricService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}

/*
 * increbyrepo - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *CodemetricService) Increbyrepo(req *dto.IncrebyrepoReqDto) (*dto.IncrebyrepoResDto, error) {
	res := &dto.IncrebyrepoResDto{}
	// 编写业务代码组装Response
	// 建议业务在service层通过结构化打印日志，详细可以参考文档https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/l8K4XoZCKXV07p
	// 另外，在service层开启协程进行DB操作时，常出现上下文超时问题，可通过派生上下文解决，具体可参看如下文档
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/X3z6Md68Uj3QF7
	// 返回Response
	return res, nil
}
