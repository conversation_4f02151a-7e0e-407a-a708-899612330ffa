/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: CodemetricService单元测试
 */
package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config/config_mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/codemetric"
)

// add接口业务测试入口
func TestCodemetricAdd(t *testing.T) {
	// 注册驱动
	CodemetricMockRegistry(t)
	s := NewCodemetricService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.AddReqDto{}
	// 设置reqDto参数 测试数据

	resDto, errDto := s.Add(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// get接口业务测试入口
func TestCodemetricGet(t *testing.T) {
	// 注册驱动
	CodemetricMockRegistry(t)
	s := NewCodemetricService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.GetReqDto{}
	// 设置reqDto参数 测试数据

	resDto, errDto := s.Get(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// incre接口业务测试入口
func TestCodemetricIncre(t *testing.T) {
	// 注册驱动
	CodemetricMockRegistry(t)
	s := NewCodemetricService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.IncreReqDto{}
	// 设置reqDto参数 测试数据

	resDto, errDto := s.Incre(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// listall接口业务测试入口
func TestCodemetricListall(t *testing.T) {
	// 注册驱动
	CodemetricMockRegistry(t)
	s := NewCodemetricService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.ListallReqDto{}
	// 设置reqDto参数 测试数据

	resDto, errDto := s.Listall(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// 注册驱动
func CodemetricMockRegistry(t *testing.T) {
	var err error
	// 初始化配置文件,如果在根目录启动则不用设置
	config_mock.SetConfRootPath("../conf")
	err = easy.Init("")
	assert.Nil(t, err)
	// 注册直连sql驱动
	err = mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
	// 注册直连redis驱动
	// 注册直连http驱动, 测试的host链接由业务指定
	// 示例:err = mock.MockHttpDirectRegistry("confName","http://localhost:8080")
	err = mock.MockHttpDirectRegistry("naclocal", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("nacmaster", "")
	assert.Nil(t, err)
}

// fileexist接口业务测试入口
func TestCodemetricFileexist(t *testing.T) {
	// 注册驱动
	CodemetricMockRegistry(t)
	s := NewCodemetricService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.FileexistReqDto{}
	// 设置reqDto参数 测试数据
	resDto, errDto := s.Fileexist(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// addhalffile接口业务测试入口
func TestCodemetricAddhalffile(t *testing.T) {
	// 注册驱动
	CodemetricMockRegistry(t)
	s := NewCodemetricService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.AddhalffileReqDto{}
	// 设置reqDto参数 测试数据
	resDto, errDto := s.Addhalffile(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}

// increbyrepo接口业务测试入口
func TestCodemetricIncrebyrepo(t *testing.T) {
	// 注册驱动
	CodemetricMockRegistry(t)
	s := NewCodemetricService(easy.NewContext())
	// 如果需要使用redis则在conf/common/interactive.yaml中寻找对应配置名称
	s.Redis.SetRedisConfName("")
	reqDto := &dto.IncrebyrepoReqDto{}
	// 设置reqDto参数 测试数据
	resDto, errDto := s.Increbyrepo(reqDto)
	tangram_logger.Info("resDto=%+v,errDto=%+v", resDto, errDto)
}
