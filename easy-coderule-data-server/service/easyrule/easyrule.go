/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: easyrule业务封装
 * 可根据业务自行扩展
 */
package service

import (
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dao"
	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/easyrule"
	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

// Service中已经封装并初始化好了redis，case,事务，上下文等可直接使用
// 如需引用其它service和dao对象直接声明其*指针*类型的*公共*属性，无需初始化
type EasyruleService struct {
	easy.Service
	// 初始化Dao的位置，切记是指针类型，不需要初始化

	EasyRuleDataDao	*dao.EasyRuleDataDao
}

// 初始化 必须传入上下文
func NewEasyruleService(ctx *easy.Context) *EasyruleService {
	service := &EasyruleService{}
	err := easy.Injection(service, ctx)
	if err != nil && ctx != nil {
		ctx.Logger().Error("[service injection error] [err:%+v]", err)
	}
	if err == easy.BindingError {
		return nil
	}
	return service
}

/*
 * add - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *EasyruleService) Add(req *dto.AddReqDto) (*dto.AddResDto, error) {
	res := &dto.AddResDto{}
	// 编写业务代码组装Response
	data := &entity.EasyRuleData{}
	data.ProjectName = req.BodyDto.ProjectName
	data.CreateAt = req.BodyDto.CreateAt
	info, err := json.Marshal(req.BodyDto.Info)
	if err != nil {
		return nil, err
	}
	data.Data = string(info)
	data.Username = req.BodyDto.Username

	lastInsertID, err := t.EasyRuleDataDao.Add(data)
	if err != nil {
		return nil, err
	}

	if lastInsertID <= 0 {
		return nil, fmt.Errorf("Add failed, lastInsertID is %d", lastInsertID)
	}

	// 返回Response
	return res, nil
}

type SimpleEasyOutputPerfile struct {
	Filename		string			`json:"filename"`
	SimpleDiagnostic	[]*SimpleDiagnostic	`json:"diagnostics"`
}

type SimpleDiagnostic struct {
	Posn		string	`json:"posn"`
	Message		string	`json:"message"`
	Analyzer	string	`json:"analyzer"`
	URL		string	`json:"url"`
}

/*
 * list - 接口业务核心逻辑
 * PARAMS:
 *    req 传输对象
 * RETURNS:
 *    res, nil  if succeed
 *    nil,error if fail
 */
func (t *EasyruleService) List(req *dto.ListReqDto) (*dto.ListResDto, error) {
	// 编写业务代码组装Response
	if len(req.Person) == 0 {
		return t.listWithNoPerson(req)
	}

	return t.listWithPerson(req)
}

func (t *EasyruleService) listWithPerson(req *dto.ListReqDto) (*dto.ListResDto, error) {
	res := &dto.ListResDto{}
	// 编写业务代码组装Response
	person := req.Person
	easyRuleDatas, err := t.EasyRuleDataDao.ListByCreateTimeAndPerson(req.Start, req.End, person)
	if err != nil {
		return nil, err
	}

	for _, data := range easyRuleDatas {
		dtoData := &dto.ListResDtoData{}
		err := json.Unmarshal([]byte(data.Data), &dtoData.Info)
		if err != nil {
			t.Logger.Error("unmarshal failed, err: %w, data is: %s", err, data.Data)
			continue
		}
		dtoData.ProjectName = data.ProjectName
		dtoData.Username = data.Username
		dtoData.CreateAt = data.CreateAt
		res.Data = append(res.Data, dtoData)
	}

	// 返回Response
	return res, nil
}

func (t *EasyruleService) listWithNoPerson(req *dto.ListReqDto) (*dto.ListResDto, error) {
	res := &dto.ListResDto{}
	// 编写业务代码组装Response

	easyRuleDatas, err := t.EasyRuleDataDao.ListByCreateTime(req.Start, req.End)
	if err != nil {
		return nil, err
	}

	for _, data := range easyRuleDatas {
		dtoData := &dto.ListResDtoData{}
		err := json.Unmarshal([]byte(data.Data), &dtoData.Info)
		if err != nil {
			t.Logger.Error("unmarshal failed, err: %w, data is: %s", err, data.Data)
			continue
		}
		dtoData.ProjectName = data.ProjectName
		dtoData.Username = data.Username
		dtoData.CreateAt = data.CreateAt
		res.Data = append(res.Data, dtoData)
	}

	// 返回Response
	return res, nil
}

/*
 * Init -Service初始化操作
 * 无论是依赖注入还是手动调用NewEasyruleService 后都会走的逻辑
 */
func (t *EasyruleService) Init() {
	// 在这里可以做初始化赋值内部属性操作
}
