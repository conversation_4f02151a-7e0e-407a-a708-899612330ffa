/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 路由配置
 */
package router

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	_ "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action" // 如果在action中设置了钩子必须导包
	codemetric "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/codemetric"
	dbinfo "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/dbinfo"
	easyfn "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/easyfn"
	easyrule "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/easyrule"
	echo "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/echo"
	metric "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/metric"
	nilaway "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/rule/nilaway"
	sa "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/rule/sa"
	sqldata "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/sqldata"
	wfmetric "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/action/wfmetric"
	worker "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/worker"
)

// 设置路由
func SetRouter() easy.RouterMap {
	router := easy.RouterMap{}
	router["/easyrule"] = &easyrule.EasyruleController{}
	router["/codemetric"] = &codemetric.CodemetricController{}
	router["/metric"] = &metric.MetricController{}
	router["/echo"] = easy.GetHandle(&echo.EchoController{})
	router["/easyfn"] = &easyfn.EasyfnController{}
	router["/wfmetric"] = &wfmetric.WfmetricController{}
	router["/rule/nilaway"] = &nilaway.NilawayController{}
	router["/sqldata"] = &sqldata.SqldataController{}
	router["/dbinfo"] = &dbinfo.DbinfoController{}
	router["/rule/sa"] = &sa.SaController{}
	return router
}

// 设置定时任务路由
func SetSchedulerRouter() easy.RouterMap {
	router := easy.RouterMap{}
	router["CodeMetricSendToMail"] = &worker.CodeMetricSendToMailScheduler{}
	router["dump"] = &worker.DumpScheduler{}
	router["feedback"] = &worker.FeedbackScheduler{}
	return router
}
