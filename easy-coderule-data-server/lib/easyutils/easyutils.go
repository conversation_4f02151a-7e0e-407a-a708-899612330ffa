package easyutils

import (
	"math/rand"
	"path/filepath"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity/remote"
)

type EasyCode struct {
	Code        string `json:"code"`
	FileName    string `json:"fileName"`
	IsHotReload int    `json:"isHotReload"`
	Key         string `json:"key"`
	Path        string `json:"path"`
	Rewrite     bool   `json:"rewrite"`
	Type        int    `json:"type"`
}

func GetTotalLine(easyCode *remote.EasycodeResult) int {
	res := 0
	codeMap := getCode(easyCode)
	for _, code := range codeMap {
		if !isGoFile(code.FileName) {
			continue
		}
		codeSlice := strings.Split(code.Code, "\n")
		res += len(codeSlice)
	}
	return res
}

func getCode(easyCodeResult *remote.EasycodeResult) map[string]EasyCode {
	res := make(map[string]EasyCode)
	for _, valueSlice := range easyCodeResult.Data {
		v, ok := valueSlice.([]any)
		if !ok {
			continue
		}
		for _, item := range v {
			ec := EasyCode{}
			if err := mapstructure.Decode(item, &ec); err != nil {
				continue
			}
			key := filepath.Join(ec.Path, ec.FileName)
			res[key] = ec
		}
	}
	return res
}

func UnixTimeStampToString(timeStamp int64) string {
	return time.Unix(timeStamp, 0).Format("2006-01-02 15:04:05")
}

func isGoFile(fileName string) bool {
	return strings.HasSuffix(fileName, ".go")
}

// 将一个大于等于 1000 的数值分隔为一个小数值的数组
func SplitNum(num int) []int {
	var res []int

	minValue := 700
	maxValue := 1000
	rand.Seed(time.Now().UnixNano())
	for num >= maxValue {
		randomNumber := rand.Intn(maxValue-minValue) + minValue
		res = append(res, randomNumber)
		num -= randomNumber
	}

	if num > 0 {
		res = append(res, num)
	}

	return res
}
