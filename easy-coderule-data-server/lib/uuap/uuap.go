package uuap

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
)

const (
	ConstAppKey                      = "uuapclient-606808270384521216-DUiqS"
	ConstSecretKey                   = "ecf9c00aee814821a39204"
	ConstReturnFieldSuperiorUsername = "superiorUsername"
	ConstReturnFieldsDepartment      = "departmentName"
)

func UUIDGenerate() (uuid string, err error) {
	b := make([]byte, 16)
	_, err = rand.Read(b)
	if err != nil {
		return
	}
	uuid = fmt.Sprintf("%X-%X-%X-%X-%X", b[0:4], b[4:6], b[6:8], b[8:10], b[10:])

	return
}

func GenUUAPSign(timeStamp int64, sRandom string, userName string, returnFields string) (string, error) {
	tmp := ConstAppKey + returnFields + sRandom + fmt.Sprint(timeStamp) + userName + ConstSecretKey
	tmpSha := sha256.Sum256([]byte(tmp))
	return hex.EncodeToString(tmpSha[:]), nil
}
