package commitutils

import "strings"

var icafeMap = map[string]string{
	"netdisk-commercialization-": "商业化",
	"netdisk-tera-":              "Terabox",
	"netdisk-tob-":               "toB生态(企业、开放平台)",
	"netdisk-basic-":             "基础体验",
	"netdisk-DI-":                "数据智能研发团队",
	"netdisk-operation-":         "运营(电商)",
	"netdisk-yike-":              "一刻",
	"netdisk-infrastructure-":    "工程效率/质量",
	"netdisk-secure-":            "数据安全",
	"netdisk-risk-":              "数据安全",
	"netdisk-onlines-":           "在线化",
	"wangpanshujuxuqiu-":         "用商策略",
	"netdisk-innovate":           "创新业务",
}

func GetICafeID(commitMsg string) string {
	for str := range icafeMap {
		if !strings.Contains(commitMsg, str) {
			continue
		}

		lastIndex := strings.LastIndex(commitMsg, str)
		subStr := commitMsg[lastIndex:]
		subStrSlice := strings.Split(subStr, " ")
		if len(subStrSlice) != 0 && len(str) <= len(subStrSlice[0]) {
			tmp := subStrSlice[0][len(str):] // 提取数字
			num := ""
			for _, item := range tmp {
				if item < '0' || item > '9' {
					break
				}
				num += string(item)
			}

			return str + num
		}
	}
	return ""
}
