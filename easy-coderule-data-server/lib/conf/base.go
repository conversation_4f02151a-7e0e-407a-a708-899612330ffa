/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: 注册配置
 */
package conf

import (
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
)

var (
	// 邮件接收人配置
	Email *EmailConf
	// 控制配置
	Control *ControlConf
	// 服务号提醒
	Cardsend *CardsendConf
)

// 业务自定义配置文件加载初始化
func Init() {
	// 获取本地配置
	Email = &EmailConf{fileName: "email.json", fileType: tangram_config.JSON}
	// email.json文件加载注册
	LoadFileAndMonitor("email.json", false, Email)
	Control = &ControlConf{fileName: "control.json", fileType: tangram_config.JSON}
	// control.json文件加载注册
	LoadFileAndMonitor("control.json", false, Control)
	Cardsend = &CardsendConf{fileName: "cardsend.json", fileType: tangram_config.JSON}
	// cardsend.json文件加载注册
	LoadFileAndMonitor("cardsend.json", false, Cardsend)

}

/*
 * LoadFileAndMonitor -加载配置文件并且注册热更新
 * 1.先从本地conf/user/目录寻找fileName文件并加载到对象内存中
 * 2.注册热更新
 * PARAMS:
 *    fileName 文件名称
 *	  isMonitor 是否注册热更新
 * 	  conf 要加载的配置接口
 */
func LoadFileAndMonitor(fileName string, isMonitor bool, conf tangram_config.Fetcher) {
	err := tangram_config.UserLoadFile(fileName, conf)
	if err != nil {
		tangram_logger.Emergency("[msg:conf userLoadFile error] [filename: %s] [error: %v]", fileName, err)
	}
	// 注册配置热更新
	if isMonitor {
		err = tangram_config.UserMonitor(fileName, conf)
		if err != nil {
			tangram_logger.Emergency("[msg:conf userMonitor error] [filename: %s] [error: %v]", fileName, err)
		}
	}
}
