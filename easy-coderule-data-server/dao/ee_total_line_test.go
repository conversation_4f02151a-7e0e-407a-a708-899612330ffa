/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: EeTotalLine实体的数据库操作单测
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package dao

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

func TestEeTotalLineAdd(t *testing.T) {
	// 注册驱动
	MockEeTotalLineRegistry(t)
	s := NewEeTotalLineDao(easy.NewContext())
	info := &entity.EeTotalLine{}
	lastInsertID, err := s.Add(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestEeTotalLineAddList(t *testing.T) {
	// 注册驱动
	MockEeTotalLineRegistry(t)
	s := NewEeTotalLineDao(easy.NewContext())
	list := []*entity.EeTotalLine{}
	err := s.AddList(list)
	assert.Nil(t, err)
}

func TestEeTotalLineUpdate(t *testing.T) {
	// 注册驱动
	MockEeTotalLineRegistry(t)
	s := NewEeTotalLineDao(easy.NewContext())
	info := &entity.EeTotalLine{}
	lastInsertID, err := s.Update(info)
	assert.Nil(t, err)
	tangram_logger.Info("lastInsertID=%+v", lastInsertID)
}

func TestEeTotalLineCount(t *testing.T) {
	// 注册驱动
	MockEeTotalLineRegistry(t)
	s := NewEeTotalLineDao(easy.NewContext())
	repoName := "repo"
	count, err := s.Count(repoName)
	assert.Nil(t, err)
	tangram_logger.Info("count=%+v", count)
}

// EeTotalLine dao层测试入口
func TestEeTotalLineGet(t *testing.T) {
	// 注册驱动
	MockEeTotalLineRegistry(t)
	// 初始化EeTotalLineDao
	s := NewEeTotalLineDao(easy.NewContext())
	// db执行
	info, err := s.Get("repo")
	// 断言结果
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("info=%+v", info)
}

// EeTotalLine dao层测试入口
func TestEeTotalLineList(t *testing.T) {
	// 注册驱动
	MockEeTotalLineRegistry(t)
	// 初始化EeTotalLineDao
	s := NewEeTotalLineDao(easy.NewContext())
	// db执行
	list, err := s.List()
	// 断言结果
	assert.Nil(t, err)
	// 结果序列化
	by, err := json.Marshal(&list)
	// 序列化结果断言
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("list=%v", string(by))
}

// EeTotalLine dao层测试入口
func TestEeTotalLinePage(t *testing.T) {
	// 注册驱动
	MockEeTotalLineRegistry(t)
	// 初始化EeTotalLineDao
	s := NewEeTotalLineDao(easy.NewContext())
	// db执行
	list, count, pages, err := s.Page(1, 10)
	// 断言结果
	assert.Nil(t, err)
	// 结果序列化
	by, err := json.Marshal(&list)
	// 序列化结果断言
	assert.Nil(t, err)
	// 打印单元测试执行结果
	tangram_logger.Info("[list=%v] [count=%v] [count=%v]", string(by), count, pages)
}

// 注册驱动
func MockEeTotalLineRegistry(t *testing.T) {
	// 注册直连sql驱动
	err := mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		// mock数据库连接地址，该地址默认为业务在平台上配置的数据库test环境下的地址，若有变更记得更改
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
}
