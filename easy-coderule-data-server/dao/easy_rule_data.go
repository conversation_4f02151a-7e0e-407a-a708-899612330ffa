/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: EasyRuleData实体的数据库操作
 * 框架只生成基础常用操作，可根据业务自行修改
 */
package dao

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	"icode.baidu.com/baidu/netdisk/easy-coderule-data-server/entity"
)

type EasyRuleDataDao struct {
	easy.Dao
}

// 初始化 必须传入上下文
func NewEasyRuleDataDao(ctx *easy.Context) *EasyRuleDataDao {
	dao := &EasyRuleDataDao{}
	err := easy.Injection(dao, ctx)
	if err != nil && ctx != nil {
		ctx.Logger().Error("[dao injection error] [err:%+v]", err)
	}
	if err == easy.BindingError {
		return nil
	}
	return dao
}

/*
 * Add -新增单条数据 如果info有关联数据会
 * 1.开启事务 2.insert info 3.将info的主键或者自增键复制给关联 4.insert关联数据
 * PARAMS:
 *    info 修改的数据
 * RETURNS:
 *    lastInsertId, nil  if succeed
 *    0,error            if fail
 */
func (d *EasyRuleDataDao) Add(info *entity.EasyRuleData) (lastInsertID int64, err error) {
	return d.Model(info).Create()
}

/*
 * AddList -批量添加数据 1.开启事务 2.开启协程池 3.批量添加 4.操作关联数据 5.check异常 6.提交或回滚事务
 * PARAMS:
 *    list 批量新增的的数据
 * RETURNS:
 *    nil        if succeed
 *    error      if fail
 */
func (d *EasyRuleDataDao) AddList(list []*entity.EasyRuleData) (err error) {
	return d.Model(&list).CreateList()
}

/*
 * Update -修改单条数据，支持批量修改:Model(info).Where("xx=?",xx)
 * PARAMS:
 *    info 修改的数据
 *    zeroValues 可以指定值修改哪些字段
 * RETURNS:
 *    rowsAffected nil  if succeed
 *    0,error      if fail
 */
func (d *EasyRuleDataDao) Update(info *entity.EasyRuleData, zeroValues ...string) (rowsAffected int64, err error) {
	// 默认不传where 条件会加入主键进行筛选
	return d.Model(info).Update(zeroValues...)
}

/*
 * Delete -物理删除一条数据，可以根据业务自定义where条件和传入参数
 * PARAMS:
 *    ID int64 主键
 * RETURNS:
 *    rowsAffected nil  if succeed
 *    0,error           if fail
 */
func (d *EasyRuleDataDao) Delete(ID int64) (rowsAffected int64, err error) {
	// 默认不传where 条件会加入主键进行筛选
	info := &entity.EasyRuleData{}
	return d.Model(info).Where("id=?", ID).Delete()
}

/*
 * Count -获取数据条数，可以根据业务自定义where条件和传入参数
 * PARAMS:
 * RETURNS:
 *    count nil    if succeed
 *    0, error     if fail
 */
func (d *EasyRuleDataDao) Count() (count int64, err error) {
	return d.Model(&entity.EasyRuleData{}).Count()
}

/*
 * Get -通过主键获取一条数据，可以根据业务自定义where条件和传入参数
 * PARAMS:
 *    ID int64 主键
 * RETURNS:
 *    info nil  if succeed
 *    nil,error if fail
 */
func (d *EasyRuleDataDao) Get(ID int64) (info *entity.EasyRuleData, err error) {
	info = &entity.EasyRuleData{}
	err, ok := d.Model(info).Where("id=?", ID).Get()
	if !ok {
		info = nil
	}
	return
}

/*
 * List -获取列表数据，可以根据业务自定义where条件和传入参数
 * PARAMS:
 * RETURNS:
 *    list nil  if succeed
 *    nil,error if fail
 */
func (d *EasyRuleDataDao) List() (list []*entity.EasyRuleData, err error) {
	// Relation是关联查询 默认是筛选外键关联info主键的所以列表，也可以自定义查询操作：b.Select().Where().OrderBy().Offset().Limit()
	err = d.Model(&list).Select("*").
		All()
	return
}

func (d *EasyRuleDataDao) ListByCreateTime(start int64, end int64) (list []*entity.EasyRuleData, err error) {
	// Relation是关联查询 默认是筛选外键关联info主键的所以列表，也可以自定义查询操作：b.Select().Where().OrderBy().Offset().Limit()
	err = d.Model(&list).Select("*").Where("create_at >= ? AND create_at < ?", start, end).
		All()
	return
}

func (d *EasyRuleDataDao) ListByCreateTimeAndPerson(start int64, end int64, person string) (list []*entity.EasyRuleData, err error) {
	// Relation是关联查询 默认是筛选外键关联info主键的所以列表，也可以自定义查询操作：b.Select().Where().OrderBy().Offset().Limit()
	err = d.Model(&list).Select("*").Where("create_at >= ? AND create_at < ? AND person = ?", start, end, person).
		All()
	return
}

/*
 * Page -获取分页列表数据，可以根据业务自定义where条件和传入参数
 * PARAMS:
 *    page:当前页码
 *    size:一页显示条数
 *    count:根据条件筛选后的总条数
 *    pages:根据条件筛选后的总页数
 *    list :分页获取的数据条目
 * RETURNS:
 *    list,count,pages, nil  if succeed
 *    nil,0,0,error          if fail
 */
func (d *EasyRuleDataDao) Page(page, size int) (list []*entity.EasyRuleData, count int64, pages int, err error) {
	count, pages, err = d.Model(&list).Select("*").Page(page, size)
	return
}

/*
 * Init -Dao初始化操作
 * 无论是依赖注入还是手动调用NewEasyRuleDataDao 后都会走的逻辑
 */
func (d *EasyRuleDataDao) Init() {
	// 在这里可以做初始化赋值内部属性操作
	// 如关闭Dao的打印SQL操作:d.DoNotPrintSQL = true
}

// Before -前置操作,在执行sql之前调用;op:操作类型
func (d *EasyRuleDataDao) Before(op easy.Operation, model *easy.ModelStruct) error {
	// Demo1:前置拦截
	// if d.Context.GetUid() == 0 {
	// 	统一处理前置条件返回error不继续执行
	// 	return errs.AppDberror
	// }

	// Demo2: 统一处理分表规则
	// model.Table(func(table string) string {
	// return fmt.Sprintf("%s_%d", table, d.Context.GetUid()%32)
	// })

	// Demo3: 统一添加公共必带条件；add 会被过滤条件所以此处不会影响结果
	// model.Where("uid=?", d.Context.GetUid())
	return nil
}

// After -后置操作,在执行sql之后调用，error:sql执行中出现的异常
func (d *EasyRuleDataDao) After(op easy.Operation, sql string, err error) {
	if err != nil {
		// d.SLog.Error("sql exec error").SetErr(err).Print()
	}
}
