/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: controller层全局拦截过滤器
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

func init() {
	easy.Handlers = []easy.Handler{easy.BeforeHandler(Before), easy.AfterHandler(After)}
}

// 所有接口执行前调用 一般用于设置公共参数，日志参数
func Before(ctx *easy.Context) error {
	return nil
}

// 所有接口执行后调用 一般用于统计
func After(ctx *easy.Context)	{}
