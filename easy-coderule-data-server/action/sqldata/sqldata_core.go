/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: sqldata模块的业务入口
 * 函数命名规则：接口名首字母大写+Core 框架会自动调用
 * 函数实现内容：1.特殊参数验证如raw 2.调用service执行业务逻辑 3.封装Response
 */
package action

import
// Upload接口业务入口
service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/sqldata"

// upload接口业务入口
func (t *SqldataController) UploadCore() {
	// 自定义验证参数 例如：t.UploadDto.Body

	// 执行WF设计的service入口
	service := service.NewUploadService(t.Context)
	resDto, errDto := service.UploadExec(t.UploadDto)
	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// diffview接口业务入口
func (t *SqldataController) DiffviewCore() {
	// 自定义验证参数 例如：t.DiffviewDto.Body

	// 执行WF设计的service入口
	service := service.NewDiffviewService(t.Context)
	resStr, err := service.DiffviewExec(t.DiffviewDto)
	if err != nil {
		t.SLog.Error("diffview exec error").SetErr(err).Print()
		t.SetResponse(nil, err)
		return
	}
	// 绑定Response 返回
	t.Response.Header = make(map[string]string)
	t.Response.Header["Content-Type"] = "text/html; charset=utf-8"

	t.Response.Output(200, []byte(resStr))
}

// review接口业务入口
func (t *SqldataController) ReviewCore() {
	// 自定义验证参数

	// 执行WF设计的service入口
	service := service.NewReviewService(t.Context)
	resDto, err := service.ReviewExec(t.ReviewDto)
	// 绑定Response 返回
	t.SetResponse(resDto, err)
}

// stat接口业务入口
func (t *SqldataController) StatCore() {
	// 自定义验证参数 例如：t.StatDto.Body

	// 执行WF设计的service入口
	service := service.NewStatService(t.Context)
	resDto, errDto := service.StatExec(t.StatDto)
	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// issueshow接口业务入口
func (t *SqldataController) IssueshowCore() {
	// 自定义验证参数 例如：t.IssueshowDto.Body

	// 执行WF设计的service入口
	service := service.NewIssueshowService(t.Context)
	resDto, errDto := service.IssueshowExec(t.IssueshowDto)
	// 绑定Response 返回
	if errDto != nil {
		t.SetResponse(nil, errDto)
		return
	}

	// 绑定Response 返回
	t.Response.Header = make(map[string]string)
	t.Response.Header["Content-Type"] = "text/html; charset=utf-8"

	t.Response.Output(200, resDto)
}
