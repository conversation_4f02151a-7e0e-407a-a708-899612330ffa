/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: WfmetricController层全局拦截过滤器
 */
package action

/*
* wfmetric内接口core执行前准备数据，一般用于配置（dto service 未初始化）
* RETURNS:
*   nil, if succeed
*   error, if fail
 */
func (t *WfmetricController) Prepare() error {
	// 接口参数验签开关，默认是关闭
	t.SignVerifyOnOff = false
	return nil
}

/*
* wfmetric内接口core执行前 dto service 已初始化，参数校验完成
* RETURNS:
*   nil, if succeed
*   error, if fail
 */
func (t *WfmetricController) Before() error {
	return nil
}

// wfmetric内接口core执行后
func (t *WfmetricController) After()	{}
