/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: CodemetricController单元测试
 */
package action

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
	"icode.baidu.com/baidu/netdisk/easy-go-sdk/library/mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_config/config_mock"
	"icode.baidu.com/baidu/netdisk/tangram-iface/tangram_logger"
)

// api 测试总入口
func TestApi(t *testing.T) {
	// 初始化配置文件,如果在根目录启动则不用设置
	config_mock.SetConfRootPath("../../conf")
	err := easy.Init("")
	assert.Nil(t, err)
	// 1.初始化要测试controller
	c := &CodemetricController{}
	// 2.构建测试数据
	tests := []struct {
		name	string
		args	*mock.ReqArgs
	}{
		{
			name:	"test api:/codemetric/add",	// 测试名称标识
			args: &mock.ReqArgs{
				Exec:		c.Add,	// 测试的接口入口函数
				Method:		http.MethodPost,
				RequestURI:	"/codemetric/add",	// 测试接口地址
				Header: http.Header{
					"X-TANGRAM-LOG-ID":	[]string{"1235671231551"},	// logid染色
					"X-TANGRAM-CALL-ID":	[]string{"0.1.1"},		// callid染色
					"Content-Type":		[]string{"application/x-www-form-urlencoded"},
				},
				Data:	mock.BuildData(func(p *mock.MockParams) {}),	// request入参
			},
		},
		{
			name:	"test api:/codemetric/get",	// 测试名称标识
			args: &mock.ReqArgs{
				Exec:		c.Get,	// 测试的接口入口函数
				Method:		http.MethodPost,
				RequestURI:	"/codemetric/get",	// 测试接口地址
				Header: http.Header{
					"X-TANGRAM-LOG-ID":	[]string{"1235671231551"},	// logid染色
					"X-TANGRAM-CALL-ID":	[]string{"0.1.1"},		// callid染色
					"Content-Type":		[]string{"application/x-www-form-urlencoded"},
				},
				Data:	mock.BuildData(func(p *mock.MockParams) {}),	// request入参
			},
		},
		{
			name:	"test api:/codemetric/fileexist",	// 测试名称标识
			args: &mock.ReqArgs{
				Exec:		c.Fileexist,	// 测试的接口入口函数
				Method:		http.MethodPost,
				RequestURI:	"/codemetric/fileexist",	// 测试接口地址
				Header: http.Header{
					"X-TANGRAM-LOG-ID":	[]string{"1235671231551"},	// logid染色
					"X-TANGRAM-CALL-ID":	[]string{"0.1.1"},		// callid染色
					"Content-Type":		[]string{"application/x-www-form-urlencoded"},
				},
				Data:	mock.BuildData(func(p *mock.MockParams) {}),	// request入参
			},
		},
	}
	// 执行API单测
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			MockRegistry(t)
			c.Mock(test.args, func(bytes []byte, e error) {
				assert.Nil(t, e)
				// 获取response 内容 bytes 编写测试逻辑
				// 打印结果
				tangram_logger.Info("dump response:\n%s", bytes)
			})
		})
	}
}

// 注册驱动
func MockRegistry(t *testing.T) {
	var err error
	// 注册直连sql驱动
	err = mock.MockSqlDirectRegistry("mysql_cloud_netdisk_easy_coderule",
		mock.MockSqlConf{Host: "*************:8811", User: "chunlei", Pwd: "MhxzKhl", DbName: "netdisk_easy_coderule"})
	assert.Nil(t, err)
	// 注册直连redis驱动
	// 注册直连http驱动, 测试的host链接由业务指定
	// 示例:err = mock.MockHttpDirectRegistry("confName","http://localhost:8080")
	err = mock.MockHttpDirectRegistry("naclocal", "")
	assert.Nil(t, err)
	err = mock.MockHttpDirectRegistry("nacmaster", "")
	assert.Nil(t, err)
}
