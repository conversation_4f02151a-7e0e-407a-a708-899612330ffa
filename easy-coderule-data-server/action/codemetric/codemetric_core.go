/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: codemetric模块的业务入口
 * 函数命名规则：接口名首字母大写+Core 框架会自动调用
 * 函数实现内容：1.特殊参数验证如raw 2.调用service执行业务逻辑 3.封装Response
 */
package action

// add接口业务入口
func (t *CodemetricController) AddCore() {
	// 自定义验证参数 例如：t.AddDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.CodemetricService.Add(t.AddDto)
	// 编写业务代码组装Response

	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// get接口业务入口
func (t *CodemetricController) GetCore() {
	// 自定义验证参数 例如：t.GetDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.CodemetricService.Get(t.GetDto)
	// 编写业务代码组装Response

	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// fileexist接口业务入口
func (t *CodemetricController) FileexistCore() {
	// 自定义验证参数 例如：t.FileexistDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.CodemetricService.Fileexist(t.FileexistDto)

	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// incre接口业务入口
func (t *CodemetricController) IncreCore() {
	// 自定义验证参数 例如：t.IncreDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.CodemetricService.Incre(t.IncreDto)
	// 编写业务代码组装Response

	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// listall接口业务入口
func (t *CodemetricController) ListallCore() {
	// 自定义验证参数 例如：t.ListallDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.CodemetricService.Listall(t.ListallDto)
	// 编写业务代码组装Response

	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// listall接口业务入口
func (t *CodemetricController) AddhalffileCore() {
	// 自定义验证参数 例如：t.ListallDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.CodemetricService.Addhalffile(t.AddhalffileDto)
	// 编写业务代码组装Response

	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// increbyrepo接口业务入口
func (t *CodemetricController) IncrebyrepoCore() {
	// 自定义验证参数 例如：t.IncrebyrepoDto.Body

	// 调用service 获取ResponseDto
	resDto, errDto := t.CodemetricService.Increbyrepo(t.IncrebyrepoDto)
	// 编写业务代码组装Response，业务可以通过配置自定义返回结构，详情可参见如下文档链接或通过t.Response.Output()之类
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/0pbQON0Sqnwzfm#anchor-dc9f7b40-5a56-11ed-804a-7302eed97af5
	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}
