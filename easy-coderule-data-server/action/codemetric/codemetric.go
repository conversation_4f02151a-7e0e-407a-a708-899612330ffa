/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: codemetric模块的接口定义模板
 * 函数命名规则：接口名首字母大写
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/codemetric"
	service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/codemetric"
)

// Codemetric 路由接口对应的Controller,包括入参Dto以及service
type CodemetricController struct {
	easy.Controller
	// 依赖注入 （动态实例化，上下文，log, query）
	CodemetricService *service.CodemetricService
	AddDto            *dto.AddReqDto
	GetDto            *dto.GetReqDto
	FileexistDto      *dto.FileexistReqDto
	IncreDto          *dto.IncreReqDto
	ListallDto        *dto.ListallReqDto
	AddhalffileDto    *dto.AddhalffileReqDto
	IncrebyrepoDto    *dto.IncrebyrepoReqDto
}

// add
func (t *CodemetricController) Add() {
	easy.Exec(t)
}

// get
func (t *CodemetricController) Get() {
	easy.Exec(t)
}

// 判断文件是否存在
func (t *CodemetricController) Fileexist() {
	easy.Exec(t)
}

// 增量生成率
func (t *CodemetricController) Incre() {
	easy.Exec(t)
}

// listall
func (t *CodemetricController) Listall() {
	easy.Exec(t)
}

// 新增半托管文件
func (t *CodemetricController) Addhalffile() {
	easy.Exec(t)
}

// 根据接口分类查看数据统计信息
func (t *CodemetricController) Increbyrepo() {
	easy.Exec(t)
}
