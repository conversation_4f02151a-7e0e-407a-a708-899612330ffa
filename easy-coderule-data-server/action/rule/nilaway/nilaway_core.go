/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: nilaway模块的业务入口
 * 函数命名规则：接口名首字母大写+Core 框架会自动调用
 * 函数实现内容：1.特殊参数验证如raw 2.调用service执行业务逻辑 3.封装Response
 */
package action

// listByRepo接口业务入口

func (t *NilawayController) ListByRepoCore() {
	// 自定义验证参数 例如：t.ListByRepoDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.NilawayService.ListByRepo(t.ListByRepoDto)
	// 编写业务代码组装Response，业务可以通过配置自定义返回结构，详情可参见如下文档链接或通过t.Response.Output()之类
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/0pbQON0Sqnwzfm#anchor-dc9f7b40-5a56-11ed-804a-7302eed97af5
	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// ignoreBatch接口业务入口
func (t *NilawayController) IgnoreBatchCore() {
	// 自定义验证参数 例如：t.IgnoreBatchDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.NilawayService.IgnoreBatch(t.IgnoreBatchDto)
	// 编写业务代码组装Response，业务可以通过配置自定义返回结构，详情可参见如下文档链接或通过t.Response.Output()之类
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/0pbQON0Sqnwzfm#anchor-dc9f7b40-5a56-11ed-804a-7302eed97af5
	// 绑定Response 返回

	t.SetResponse(resDto, errDto)
}

// ignoreOne接口业务入口
func (t *NilawayController) IgnoreOneCore() {
	// 自定义验证参数 例如：t.IgnoreOneDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.NilawayService.IgnoreOne(t.IgnoreOneDto)
	// 编写业务代码组装Response，业务可以通过配置自定义返回结构，详情可参见如下文档链接或通过t.Response.Output()之类
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/0pbQON0Sqnwzfm#anchor-dc9f7b40-5a56-11ed-804a-7302eed97af5
	// 绑定Response 返回

	t.SetResponse(resDto, errDto)
}

// saveList接口业务入口
func (t *NilawayController) SaveListCore() {
	// 自定义验证参数 例如：t.SaveListDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.NilawayService.SaveList(t.SaveListDto)
	// 编写业务代码组装Response，业务可以通过配置自定义返回结构，详情可参见如下文档链接或通过t.Response.Output()之类
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/0pbQON0Sqnwzfm#anchor-dc9f7b40-5a56-11ed-804a-7302eed97af5
	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// show接口业务入口
func (t *NilawayController) ShowCore() {
	// 自定义验证参数 例如：t.ShowDto.Body

	// 调用service 获取ResponseDto
	resDto, err := t.NilawayService.Show(t.ShowDto)
	// 编写业务代码组装Response，业务可以通过配置自定义返回结构，详情可参见如下文档链接或通过t.Response.Output()之类
	// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5mhLHJ74rR/k73vwkNTP1/0pbQON0Sqnwzfm#anchor-dc9f7b40-5a56-11ed-804a-7302eed97af5
	// 绑定Response 返回
	if err != nil {
		t.SLog.Error("nilaway show error").SetErr(err).Print()
		t.SetResponse(nil, err)
		return
	}
	// 绑定Response 返回
	t.Response.Header = make(map[string]string)
	t.Response.Header["Content-Type"] = "text/html; charset=utf-8"

	t.Response.Output(200, []byte(resDto))
}

// process接口业务入口
func (t *NilawayController) ProcessCore() {
	// 自定义验证参数

	// 执行WF设计的service入口
	resDto, err := t.NilawayService.ProcessExec(t.ProcessDto)
	// 绑定Response 返回
	t.SetResponse(resDto, err)
}
