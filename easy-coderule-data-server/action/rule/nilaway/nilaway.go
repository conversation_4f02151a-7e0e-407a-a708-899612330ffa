/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: nilaway模块的接口定义模板
 * 函数命名规则：接口名首字母大写
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/rule/nilaway"
	service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/rule/nilaway"
)

// Nilaway 路由接口对应的Controller,包括入参Dto以及service
type NilawayController struct {
	easy.Controller
	// 依赖注入 （动态实例化，上下文，log, query）
	NilawayService *service.NilawayService
	ListByRepoDto  *dto.ListByRepoReqDto
	IgnoreBatchDto *dto.IgnoreBatchReqDto
	IgnoreOneDto   *dto.IgnoreOneReqDto
	SaveListDto    *dto.SaveListReqDto
	ShowDto        *dto.ShowReqDto
	ProcessDto     *dto.ProcessReqDto
}

// listByRepo
func (t *NilawayController) ListByRepo() {
	easy.Exec(t)
}

// ignoreBatch
func (t *NilawayController) IgnoreBatch() {
	easy.Exec(t)
}

// 不提示一条
func (t *NilawayController) IgnoreOne() {
	easy.Exec(t)
}

// saveList接口
func (t *NilawayController) SaveList() {
	easy.Exec(t)
}

// 展示 html 页面
func (t *NilawayController) Show() {
	easy.Exec(t)
}

// 反馈数据回调
func (t *NilawayController) Process() {
	easy.Exec(t)
}
