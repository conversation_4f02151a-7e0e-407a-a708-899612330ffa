/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: sa模块的接口定义模板
 * 函数命名规则：接口名首字母大写
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/rule/sa"
	service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/rule/sa"
)

// Sa 路由接口对应的Controller,包括入参Dto以及service
type SaController struct {
	easy.Controller
	// 依赖注入 （动态实例化，上下文，log, query）
	SaService    *service.SaService
	QueryDto     *dto.QueryReqDto
	ListIcodeDto *dto.ListIcodeReqDto
}

// 查看具体icode的检测结果
func (t *SaController) Query() {
	easy.Exec(t)
}

// list_icode
func (t *SaController) ListIcode() {
	easy.Exec(t)
}
