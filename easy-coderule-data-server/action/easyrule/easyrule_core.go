/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: easyrule模块的业务入口
 * 函数命名规则：接口名首字母大写+Core 框架会自动调用
 * 函数实现内容：1.特殊参数验证如raw 2.调用service执行业务逻辑 3.封装Response
 */
package action

// add接口业务入口
func (t *EasyruleController) AddCore() {
	// 自定义验证参数 例如：t.AddDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.EasyruleService.Add(t.AddDto)
	// 编写业务代码组装Response

	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}

// list接口业务入口
func (t *EasyruleController) ListCore() {
	// 自定义验证参数 例如：t.ListDto.Body
	// 调用service 获取ResponseDto
	resDto, errDto := t.EasyruleService.List(t.ListDto)
	// 编写业务代码组装Response

	// 绑定Response 返回
	t.SetResponse(resDto, errDto)
}
