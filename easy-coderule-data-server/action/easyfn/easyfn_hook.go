/*
 * 主体代码由Easy生成，**后续update不会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: EasyfnController层全局拦截过滤器
 */
package action

/*
* easyfn内接口core执行前准备数据，一般用于配置（dto service 未初始化）
* RETURNS:
*   nil, if succeed
*   error, if fail
 */
func (t *EasyfnController) Prepare() error {
	// 接口参数验签开关，默认是关闭
	t.SignVerifyOnOff = false
	return nil
}

/*
* easyfn内接口core执行前 dto service 已初始化，参数校验完成
* RETURNS:
*   nil, if succeed
*   error, if fail
 */
func (t *EasyfnController) Before() error {
	return nil
}

// easyfn内接口core执行后
func (t *EasyfnController) After()	{}
