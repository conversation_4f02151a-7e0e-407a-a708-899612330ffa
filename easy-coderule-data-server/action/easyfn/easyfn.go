/*
 * Easy生成，**平台修改本地update会更新此文件**
 * Author:  Easy
 * Version: 1.0.0
 * Description: easyfn模块的接口定义模板
 * 函数命名规则：接口名首字母大写
 */
package action

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"

	dto "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/dto/easyfn"
	service "icode.baidu.com/baidu/netdisk/easy-coderule-data-server/service/easyfn"
)

// Easyfn 路由接口对应的Controller,包括入参Dto以及service
type EasyfnController struct {
	easy.Controller
	// 依赖注入 （动态实例化，上下文，log, query）
	EasyfnService  *service.EasyfnService
	AddDto         *dto.AddReqDto
	GetDto         *dto.GetReqDto
	BranchqueryDto *dto.BranchqueryReqDto
}

// 新增信息提取
func (t *EasyfnController) Add() {
	easy.Exec(t)
}

// 获取提交信息
func (t *EasyfnController) Get() {
	easy.Exec(t)
}

// 查询分支接口
func (t *EasyfnController) Branchquery() {
	easy.Exec(t)
}
