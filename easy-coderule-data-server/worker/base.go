/*
* Easy生成，**平台修改本地update会更新此文件**
* Author:  Easy
* Version: 1.0.0
* Description: 消费者封装消息Dto
*  结构体中的tag描述（由平台配置，无需手动修改）：
*	validate: 验证规则，多个规则由逗号分割
*  len：字符串限制长度
*  verify：加入参数验签 通过sign算法，框架自动验证
*  type:"raw" 标识json 格式的参数 通过raw字符串序列化
 */
package worker

import (
	"icode.baidu.com/baidu/netdisk/easy-go-sdk"
)

// 定时任务CodeMetricSendToMailScheduler
type CodeMetricSendToMailScheduler struct {
	easy.ScheduledWorker
}

// 定时任务DumpScheduler
type DumpScheduler struct {
	easy.ScheduledWorker
}

// 定时任务FeedbackScheduler
type FeedbackScheduler struct {
	easy.ScheduledWorker
}
