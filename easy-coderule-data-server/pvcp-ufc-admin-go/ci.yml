Global:
    version: "2.0"
    group_email: <PERSON><PERSON><PERSON><PERSON>@baidu.com
Default:
    profile:
        - build
Profiles:
    - profile:
      name: build
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - go: 1.21.latest
      build:
        command: make -f Makefile
      artifacts:
        release: true
    - profile:
      name: sandbox
      mode: AGENT
      environment:
        image: DECK_STD_CENTOS7
        tools:
            - go: 1.21.latest
      build:
        command: make -f Makefile sandbox
      artifacts:
        release: true
