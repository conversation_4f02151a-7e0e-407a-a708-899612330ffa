package models

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

type TypeDoubleEnd int

var (
	TypeDoubleEndSingle           TypeDoubleEnd = 1 // 只有特定 to_service
	TypeDoubleEndWithoutToService TypeDoubleEnd = 2 // 不包含接口传过来的to_service
	TypeDoubleEndOther            TypeDoubleEnd = 3 // 包含特定 to_service ，但还有其他值
)

func InitAllIdcConfigsDoubleEnds(rewrite bool) ([]string, error) {
	idcList := make([]string, 0)
	idcs := BaseConf.Idc.Names
	for _, idc := range idcs {
		if err := InitOneIdcConfigsDoubleEnds(idc, rewrite); err != nil {
			golog.Error("[InitOneIdcConfigsDoubleEnds failed] [idc:%s] [rewrite:%v]", idc, rewrite)
			return idcList, err
		}

		idcList = append(idcList, idc)
		golog.Info("[InitOneIdcConfigsDoubleEnds success] [idc:%s] [rewrite:%v]", idc, rewrite)
	}
	return idcList, nil
}

// 幂等的初始化双端配置
func InitOneIdcConfigsDoubleEnds(idc string, rewrite bool) error {
	prefix := GetPrefix(idc)
	configCntKey := fmt.Sprintf("%s-configs-cnt", prefix)
	cnt := utils.GetIntCache(configCntKey)
	if cnt < 0 {
		golog.Warn("config cnt key %s not exist", configCntKey)
		return fmt.Errorf("[get config cnt from cache failed] [key:%s]", configCntKey)
	}

	configIndexExist, index, err := isConfigDoubleEndsIndexExist(prefix, cnt)
	if err != nil {
		return err
	}

	// 设置 index -> double_ends
	if !configIndexExist {
		// configs-cnt 自增1, 并且最新的值设置为 configs_end
		configCnt := cnt + 1
		configIndexKey := fmt.Sprintf("%s-configs-%d", prefix, cnt)
		configIndexValue := "double_ends"

		// 存在则更新，不存在则添加
		if err := AddOrUpdateUfcMetaInfo(configCntKey, fmt.Sprintf("%v", configCnt)); err != nil {
			golog.Error("[UpdateUfcMetaInfo failed] [key:%s] [val: %d]", configCntKey, configCnt)
			return err
		}

		// config-index -> double_ends
		if err := AddUfcMetaInfo(configIndexKey, configIndexValue); err != nil {
			golog.Error("[UpdateUfcMetaInfo failed] [key:%s] [val: %s]", configIndexKey, configIndexValue)
			return err
		}

		if ok := utils.MsetCache(configCntKey, configCnt, configIndexKey, configIndexValue); !ok {
			golog.Warn("config cache set double_ends index and incre configs-cnt failed] [old cnt: %d]", cnt)
			return errors.New("config cache set double_ends index and incre configs-cnt failed")
		}
	} else {
		// 服务在 cache 中存在，但不一定在 mysql存在，刷到配置里面
		// config-index -> double_ends
		configIndexKey := fmt.Sprintf("%s-configs-%d", prefix, index)
		configIndexValue := "double_ends"
		if err := AddOrUpdateUfcMetaInfo(configIndexKey, configIndexValue); err != nil {
			golog.Error("[UpdateUfcMetaInfo failed] [key:%s] [val: %v]", configIndexKey, configIndexValue)
			return err
		}
	}

	// 如果db
	configExist, err := isConfigDoubleEndsConfigsExist(prefix, configIndexExist)
	if err != nil && !rewrite {
		// 此时 configExist 为 false, 配置不存在，可直接写db或者覆盖db，
		golog.Warn("[config double_ends configs key error] [configIndexExist: %v] [rewrite: %v] [err: %v]", configIndexExist, rewrite, err)
		return err
	}

	if !configExist {
		key, value, err := genConfigsDoubleEnds(prefix)
		if err != nil {
			golog.Warn("[gen configs double ends key value pair failed] [err: %v]", err)
			return errors.New("gen configs double ends key value pair failed")
		}

		// p-3ufc-sandbox-beijing-config-double_ends
		if err := AddUfcMetaInfo(key, value); err != nil {
			golog.Warn("[add configs double ends key value pair to db failed] [key: %s] [val: %s] [err: %v]", key, value, err)
			return err
		}

		if ok := utils.SetCache(key, value); !ok {
			golog.Warn("[config cache set double_ends key value pair failed] [key:%s] [value:%s]", key, value)
			return fmt.Errorf("[config cache set double_ends key value pair failed] [key:%s] [value:%s]", key, value)
		}
		return nil
	}

	return nil
}

// 是否存在 prefix-configs-index 的配置为 double_ends
func isConfigDoubleEndsIndexExist(prefix string, configCnt int64) (bool, int, error) {
	for i := 0; i < int(configCnt); i++ {
		key := fmt.Sprintf("%s-configs-%d", prefix, i)
		value := utils.GetCache(key)
		if value == "" {
			golog.Warn("[config double_ends index not exist] [key: %s]", key)
			return false, -1, fmt.Errorf("[get double_ends config index from cache failed] [key:%s]", key)
		}

		if value == "double_ends" {
			return true, i, nil
		}
	}
	return false, -1, nil
}

// 具体配置是否存在
func isConfigDoubleEndsConfigsExist(prefix string, indexExist bool) (bool, error) {
	key := fmt.Sprintf("%s-config-double_ends", prefix)
	keyExist, err := IsKeyInUfcMetaInfo(key)
	if err != nil {
		golog.Warn("[get configs double_ends config from ufc meta info failed] [key: %v] [err: %v]", key, err)
		return false, err
	}

	if !keyExist {
		golog.Warn("config %s not exist", key)
		// 如果index已存在，这里value为空则抛出错误
		if indexExist {
			return false, fmt.Errorf("[get config from cache failed] [key:%s]", key)
		}

		return false, nil
	}

	value, err := GetUfcMetaInfo(key)
	if err != nil {
		golog.Warn("[get configs double_ends config from ufc meta info failed] [key: %v] [err: %v]", key, err)
		return false, err
	}

	var doubleEndsMap map[string]interface{}
	// key存在但不为 json，返回 false 手动查看数据解决
	if err := json.Unmarshal([]byte(value), &doubleEndsMap); err != nil {
		golog.Warn("[config value not json unmarshal] [key: %v] [value: %v]", key, value)
		return false, fmt.Errorf("config  value not json unmarshal, key is %s and value is %s", key, value)
	}

	return true, nil
}

func genConfigsDoubleEnds(prefix string) (string, string, error) {
	key := fmt.Sprintf("%s-config-double_ends", prefix)

	/*
		 双端配置构成：{
			"all_service":
			"enable":
			"services":[
				{
					"from_ip"
					"from_service"
					"to_service":
				}
			]
		}
	*/
	configDoubleEndsMap := make(map[string]interface{})
	configDoubleEndsMap["all_service"] = 0
	configDoubleEndsMap["enable"] = 1
	configDoubleEndsMap["services"] = make([]interface{}, 0)
	valByte, err := json.Marshal(configDoubleEndsMap)
	if err != nil {
		return "", "", err
	}
	val := string(valByte)
	return key, val, nil
}

func AddDoubleEndsSmallflow(toService string, ips []string) error {
	if len(ips) == 0 {
		golog.Error("[ip list is empty] [to service: %s]", toService)
		return errors.New("ip list is empty")
	}

	idcs := BaseConf.Idc.Names

	// 校验是否已经存在有小流量, 已经有小流量时就不要再继续添加
	for _, idc := range idcs {
		prefix := GetPrefix(idc)
		doubleEndsSmallflowKey := getDoubleEndsKeySmallflow(prefix)
		doubleEndsBackupKey := getDoubleEndsKeyBackup(prefix)
		smallflowIpsKey := getDoubleEndsSmallflowIpsKey(prefix)
		exist1 := utils.Exists(doubleEndsSmallflowKey)
		exist2 := utils.Exists(doubleEndsBackupKey)
		exist3 := utils.Exists(smallflowIpsKey)
		if exist1 || exist2 || exist3 {
			golog.Error("[double ends key about smallfow already exists] [idc: %s] [key: %s status: %v] [key: %s status: %v]",
				idc, doubleEndsSmallflowKey, exist1, doubleEndsBackupKey, exist2)
			return errors.New("double ends key already exists")
		}
	}

	redisKVMap := make(map[string]interface{})

	for _, idc := range idcs {
		prefix := GetPrefix(idc)
		doubleEndsKey := getDoubleEndsKey(prefix)
		doubleEndsValue := utils.GetCache(doubleEndsKey)
		if doubleEndsValue == "" {
			golog.Error("[double ends value is empty] [idc: %s] [key: %s]", idc, doubleEndsKey)
			return errors.New("double ends value is empty")
		}

		doubleEndsSmallflowKey := getDoubleEndsKeySmallflow(prefix)
		doubleEndsBackupKey := getDoubleEndsKeyBackup(prefix)

		redisKVMap[doubleEndsBackupKey] = doubleEndsValue
		redisKVMap[doubleEndsSmallflowKey] = toService

		doubleEndsValue, err := genDoubleEndsValueWithAdd(doubleEndsKey, toService)
		if err != nil {
			golog.Error("[gen double ends value with add failed] [idc: %s] [key: %s]", idc, doubleEndsKey)
			return err
		}

		redisKVMap[doubleEndsKey] = doubleEndsValue

		timeNow := time.Now().Unix()
		doubleEndsMtimeKey := getDoubleEndsMtimeKey(prefix)
		redisKVMap[doubleEndsMtimeKey] = timeNow
		globalMtimeKey := getGlobalMtime(prefix)
		redisKVMap[globalMtimeKey] = timeNow
	}

	var keyValueSlice []interface{}
	for key, value := range redisKVMap {
		keyValueSlice = append(keyValueSlice, key)
		keyValueSlice = append(keyValueSlice, value)
	}

	if len(keyValueSlice) == 0 || len(keyValueSlice)%2 == 1 {
		golog.Error("[add all idc double ends failed] [keyValueSlice len is illegal] [len(keyValueSlice)=%d] [kvslice: %v]", len(keyValueSlice), keyValueSlice)
		return errors.New("keyValueSlice len is illegal")
	}
	//  更新 ips
	for _, idc := range idcs {
		prefix := GetPrefix(idc)
		doubleEndsSmallflowIpsKey := getDoubleEndsSmallflowIpsKey(prefix)
		var slice []string
		for _, ip := range ips {
			slice = append(slice, ip, "in")
		}

		ok := utils.HMSet(doubleEndsSmallflowIpsKey, slice...)
		if !ok {
			golog.Error("[add double ends smallflow ips failed] [idc: %s] [key: %v] [slice: %v]", idc, doubleEndsSmallflowIpsKey, slice)
			return errors.New("add double ends smallflow ips failed")
		}
	}

	// 直接更新配置
	ok := utils.MsetCache(keyValueSlice...)
	if !ok {
		golog.Warn("[mset cache failed] [keyValueSlice len is illegal [kvslice: %v]", keyValueSlice)
		return errors.New("mset cache failed")
	}

	return nil
}

func AddDoubleEndsRollback(toService string) error {
	idcs := BaseConf.Idc.Names

	timeNow := time.Now().Unix()
	for _, idc := range idcs {
		prefix := GetPrefix(idc)
		doubleEndsSmallflowKey := getDoubleEndsKeySmallflow(prefix)
		doubleEndsBackupKey := getDoubleEndsKeyBackup(prefix)

		service := utils.GetCache(doubleEndsSmallflowKey)
		if service == "" {
			golog.Error("[double ends key about smallfow not exist] [key: %v value: %v]", doubleEndsSmallflowKey, service)
			return errors.New("double ends key about smallfow not exist")
		}
		if service != toService {
			golog.Error("[double ends service is not equal] [key: %s] [redis value expect %s but real %s]", doubleEndsSmallflowKey, toService, service)
			return errors.New("double ends service is not equal")
		}

		// 获取备份的配置写到原有的key中,
		backupValue := utils.GetCache(doubleEndsBackupKey)
		if backupValue == "" {
			golog.Error("[double ends backup value is empty] [key: %v]", doubleEndsBackupKey)
			return errors.New("double ends backup value is empty")
		}
		doubleEndsKey := getDoubleEndsKey(prefix)

		var keyValueSlice []interface{}
		keyValueSlice = append(keyValueSlice, doubleEndsKey)
		keyValueSlice = append(keyValueSlice, backupValue)

		mtimeKey := getDoubleEndsMtimeKey(prefix)
		keyValueSlice = append(keyValueSlice, mtimeKey)
		keyValueSlice = append(keyValueSlice, timeNow)

		globalMtimeKey := getGlobalMtime(prefix)
		keyValueSlice = append(keyValueSlice, globalMtimeKey)
		keyValueSlice = append(keyValueSlice, timeNow)

		setCacheOK := utils.MsetCache(keyValueSlice...)
		if !setCacheOK {
			golog.Error("[double ends backup value set cache failed] [key: %v]", doubleEndsBackupKey)
			return fmt.Errorf("double ends backup value set cache failed, idc: %s", idc)
		}

		// 删除小流量相关key
		doubleEndsIpsKey := getDoubleEndsSmallflowIpsKey(prefix)
		utils.DelCache(doubleEndsSmallflowKey)
		utils.DelCache(doubleEndsBackupKey)
		utils.DelCache(doubleEndsIpsKey)
	}
	return nil
}
func AddDoubleEndsFull(toService string, force bool) error {
	// 全量更新，这个函数主要是校验原来的 小流量相关备份key 是否存在，以及 smallflow 的 service 和全量是否一致（严谨些，保证一个一个注册）
	// 符合预期的话更新 db， 并删除相关小流量 key

	idcs := BaseConf.Idc.Names
	// 校验小流量相关 key 是否存在
	if !force {
		// force 为 true 不用校验，直接 delete 掉即可
		for _, idc := range idcs {
			prefix := GetPrefix(idc)
			doubleEndsSmallflowKey := getDoubleEndsKeySmallflow(prefix)
			doubleEndsBackupKey := getDoubleEndsKeyBackup(prefix)
			doubleEndsIpsKey := getDoubleEndsSmallflowIpsKey(prefix)

			exist1 := utils.Exists(doubleEndsIpsKey)
			exist2 := utils.Exists(doubleEndsBackupKey)
			service := utils.GetCache(doubleEndsSmallflowKey)

			if service != toService {
				golog.Error("[double ends service is not equal] [idc: %s] [key: %s service: %v]", idc, doubleEndsSmallflowKey, service)
				return fmt.Errorf("double ends service is not equal")
			}
			if !exist1 || !exist2 {
				golog.Error("[double ends key about smallfow not exist] [idc: %s] [key: %s status: %v] [key: %s status: %v]",
					idc, doubleEndsIpsKey, exist1, doubleEndsBackupKey, exist2)
				return fmt.Errorf("double ends key about smallfow not exist")
			}
		}
	}

	// 全量： 删除小流量相关key, 把配置写入 db
	for _, idc := range idcs {
		prefix := GetPrefix(idc)
		key := getDoubleEndsKey(prefix)
		value := utils.GetCache(key)
		if value == "" {
			golog.Error("[double ends value is empty] [idc: %s] [key: %s]", idc, key)
			return fmt.Errorf("double ends value is empty")
		}

		exist, err := IsKeyInUfcMetaInfo(key)
		if err != nil {
			golog.Error("[check double ends key in ufc meta info failed] [key: %s]", key)
			return fmt.Errorf("check double ends key in ufc meta info failed")
		}
		if exist {
			originValue, err := GetUfcMetaInfo(key)
			if err != nil {
				golog.Error("[get origin key xvalue in ufc_meta_info failed] [key: %s] [value: %s]", key, originValue)
				return fmt.Errorf("get origin %s in ufc_meta_info failed", key)
			}
			// 变更时把老配置保存到本地文件，当写错数据时能够回复
			time := strconv.FormatInt(time.Now().Unix(), 10)
			fileName := fmt.Sprintf("%s_%s", time, key)
			f, err := os.Create("../data/" + fileName)
			if err != nil {
				golog.Error("[create file failed] [file name: %s]", fileName)
				return fmt.Errorf("create file failed, file name is %s, err is %w", fileName, err)
			}
			defer f.Close()

			_, err = f.Write([]byte(originValue))
			if err != nil {
				golog.Error("[write origin value to file failed] [file name: %s]", fileName)
				return fmt.Errorf("write origin value to file failed, file name is %s, err is %w", fileName, err)
			}
			golog.Info("[write file success] [file name: %s] [value: %s]", fileName, originValue)

			if err := UpdateUfcMetaInfo(key, value); err != nil {
				golog.Error("[update ufc meta info failed] [key: %s] [value: %s]", key, value)
				return fmt.Errorf("update ufc meta info failed")
			}
			golog.Info("[update ufc meta info success] [key: %s] [value: %s]", key, value)

		} else {
			// 不存在直接 add
			if err := AddUfcMetaInfo(key, value); err != nil {
				golog.Error("add ufc meta info failed] [key: %s] [value: %s]", key, value)
				return fmt.Errorf("add ufc meta info failed")
			}
			golog.Info("[add ufc meta info success] [key: %s] [value: %s]", key, value)
		}

		// delete 掉小流量相关的 redis key
		doubleEndsSmallflowKey := getDoubleEndsKeySmallflow(prefix)
		doubleEndsBackupKey := getDoubleEndsKeyBackup(prefix)
		doubleEndsIpsKey := getDoubleEndsSmallflowIpsKey(prefix)
		utils.DelCache(doubleEndsSmallflowKey)
		utils.DelCache(doubleEndsBackupKey)
		utils.DelCache(doubleEndsIpsKey)
	}
	return nil
}

func AddAllIdcDoubleEnds(toService string) ([]string, error) {
	RedisKey2IdcMap := make(map[string]string) // key -> idc
	idcs := BaseConf.Idc.Names

	redisKVMap := make(map[string]interface{})
	for _, idc := range idcs {
		prefix := GetPrefix(idc)
		doubleEndsKey := getDoubleEndsKey(prefix)
		doubleEndsValue, err := genDoubleEndsValueWithAdd(doubleEndsKey, toService)
		if err != nil {
			golog.Warn("[get double ends value err: %v] [key: %s] [value: %s]", err, doubleEndsKey, doubleEndsValue)
			continue
		}

		redisKVMap[doubleEndsKey] = doubleEndsValue
		RedisKey2IdcMap[doubleEndsKey] = idc
	}

	var keyValueSlice []interface{}
	for key, value := range redisKVMap {
		keyValueSlice = append(keyValueSlice, key)
		keyValueSlice = append(keyValueSlice, value)
	}

	if len(keyValueSlice) == 0 || len(keyValueSlice)%2 == 1 {
		golog.Error("[add all idc double ends failed] [keyValueSlice len is illegal] [len(keyValueSlice)=%d] [kvslice: %v]", len(keyValueSlice), keyValueSlice)
		return nil, errors.New("keyValueSlice len is illegal")
	}

	successList := make([]string, 0)
	// 把以上值写入 mysql 用于配置恢复
	for i := 0; i < len(keyValueSlice); i += 2 {
		key := fmt.Sprintf("%v", keyValueSlice[i])
		val := fmt.Sprintf("%v", keyValueSlice[i+1])
		err := UpdateUfcMetaInfo(key, val)
		if err != nil {
			golog.Warn("put double ends key value err: %s", err.Error())
			return nil, err
		}

		idc := RedisKey2IdcMap[key]
		successList = append(successList, idc)

		golog.Info("put double ends key value success, key: %v, value:%v", key, val)
	}

	// 修改 mtime
	timeNow := time.Now().Unix()
	for _, idc := range successList {
		prefix := GetPrefix(idc)

		mtimeKey := getDoubleEndsMtimeKey(prefix)
		keyValueSlice = append(keyValueSlice, mtimeKey)
		keyValueSlice = append(keyValueSlice, timeNow)

		globalMtimeKey := getGlobalMtime(prefix)
		keyValueSlice = append(keyValueSlice, globalMtimeKey)
		keyValueSlice = append(keyValueSlice, timeNow)
	}

	ok := utils.MsetCache(keyValueSlice...)
	if !ok {
		golog.Warn("[mset cache failed] [keyValueSlice len is illegal [kvslice: %v]", keyValueSlice)
		return nil, errors.New("mset cache failed")
	}
	return successList, nil
}

func AddOneIdcDoubleEnds(toService string, idc string) error {
	prefix := GetPrefix(idc)
	doubleEndsKey := getDoubleEndsKey(prefix)
	doubleEndsValue, err := genDoubleEndsValueWithAdd(doubleEndsKey, toService)
	if err != nil {
		golog.Warn("get double ends value err: %v", err)
		return err
	}

	err = UpdateUfcMetaInfo(doubleEndsKey, doubleEndsValue)
	if err != nil {
		golog.Warn("put double ends key value to mysql err: %s", err.Error())
		return err
	}

	var keyValueSlice []interface{}
	keyValueSlice = append(keyValueSlice, doubleEndsKey)
	keyValueSlice = append(keyValueSlice, doubleEndsValue)

	timeNow := time.Now().Unix()
	mtimeKey := getDoubleEndsMtimeKey(prefix)
	keyValueSlice = append(keyValueSlice, mtimeKey)
	keyValueSlice = append(keyValueSlice, timeNow)

	globalMtimeKey := getGlobalMtime(prefix)
	keyValueSlice = append(keyValueSlice, globalMtimeKey)
	keyValueSlice = append(keyValueSlice, timeNow)

	ok := utils.MsetCache(keyValueSlice...)
	if !ok {
		golog.Warn("[mset cache failed] [keyValueSlice len is illegal [kvslice: %v]", keyValueSlice)
		return errors.New("set cache failed")
	}

	return nil
}

// 关闭所有集群的 idc 配置
func CloseAllIdcOneDoubleEnds(toService string) ([]string, error) {
	RedisKey2IdcMap := make(map[string]string) // key -> idc
	idcs := BaseConf.Idc.Names

	redisKVMap := make(map[string]interface{})
	for _, idc := range idcs {
		prefix := GetPrefix(idc)
		doubleEndsKey := getDoubleEndsKey(prefix)
		doubleEndsValue, err := genDoubleEndsValueWithClose(doubleEndsKey, toService)
		if err != nil {
			golog.Warn("[gen double ends value witch close err: %v] [key: %s] [value: %s]", err, doubleEndsKey, doubleEndsValue)
			continue
		}
		redisKVMap[doubleEndsKey] = doubleEndsValue
		RedisKey2IdcMap[doubleEndsKey] = idc
	}

	var keyValueSlice []interface{}
	for key, value := range redisKVMap {
		keyValueSlice = append(keyValueSlice, key)
		keyValueSlice = append(keyValueSlice, value)
	}

	if len(keyValueSlice) == 0 || len(keyValueSlice)%2 == 1 {
		golog.Error("[close all idc double ends failed] [keyValueSlice len is illegal] [len(keyValueSlice)=%d] [kvslice: %v]", len(keyValueSlice), keyValueSlice)
		return nil, errors.New("keyValueSlice len is illegal")
	}

	var successList []string
	// 把以上值写入 mysql 用于配置恢复
	for i := 0; i < len(keyValueSlice); i += 2 {
		key := fmt.Sprintf("%v", keyValueSlice[i])
		val := fmt.Sprintf("%v", keyValueSlice[i+1])
		err := UpdateUfcMetaInfo(key, val)
		if err != nil {
			golog.Warn("put double ends key value err: %s", err.Error())
			return nil, err
		}

		idc := RedisKey2IdcMap[key]
		successList = append(successList, idc)

		golog.Info("put double ends key value success, key: %v, value:%v", key, val)
	}

	// 修改 mtime
	timeNow := time.Now().Unix()
	for _, idc := range successList {
		prefix := GetPrefix(idc)

		mtimeKey := getDoubleEndsMtimeKey(prefix)
		keyValueSlice = append(keyValueSlice, mtimeKey)
		keyValueSlice = append(keyValueSlice, timeNow)

		globalMtimeKey := getGlobalMtime(prefix)
		keyValueSlice = append(keyValueSlice, globalMtimeKey)
		keyValueSlice = append(keyValueSlice, timeNow)
	}

	ok := utils.MsetCache(keyValueSlice...)
	if !ok {
		golog.Warn("[mset cache failed] [keyValueSlice len is illegal [kvslice: %v]", keyValueSlice)
		return nil, errors.New("mset cache failed")
	}
	return successList, nil
}

func CloseOneIdcOneDoubleEnds(toService string, idc string) error {
	prefix := GetPrefix(idc)
	doubleEndsKey := getDoubleEndsKey(prefix)
	doubleEndsValue, err := genDoubleEndsValueWithClose(doubleEndsKey, toService)
	if err != nil {
		golog.Warn("get double ends value err: %v] [key: %v] [value: %v]", err, doubleEndsKey, doubleEndsValue)
		return err
	}

	err = UpdateUfcMetaInfo(doubleEndsKey, doubleEndsValue)
	if err != nil {
		golog.Warn("[put double ends key value to mysql err: %v] [key: %v] [value: %v]", err, doubleEndsKey, doubleEndsValue)
		return err
	}

	var keyValueSlice []interface{}
	keyValueSlice = append(keyValueSlice, doubleEndsKey)
	keyValueSlice = append(keyValueSlice, doubleEndsValue)

	timeNow := time.Now().Unix()
	mtimeKey := getDoubleEndsMtimeKey(prefix)
	keyValueSlice = append(keyValueSlice, mtimeKey)
	keyValueSlice = append(keyValueSlice, timeNow)

	globalMtimeKey := getGlobalMtime(prefix)
	keyValueSlice = append(keyValueSlice, globalMtimeKey)
	keyValueSlice = append(keyValueSlice, timeNow)

	ok := utils.MsetCache(keyValueSlice...)
	if !ok {
		golog.Warn("[mset cache failed] [keyValueSlice len is illegal [kvslice: %v]", keyValueSlice)
		return errors.New("set cache failed")
	}

	golog.Info("close one idc one double ends success, key: %v, value: %s", doubleEndsKey, doubleEndsValue)
	return nil
}

func getDoubleEndsKey(prefix string) string {
	return prefix + "-config-double_ends"
}

// 该key用于存储小流量配置
func getDoubleEndsKeySmallflow(prefix string) string {
	return prefix + "-config-double_ends-smallflow"
}

func getDoubleEndsKeyBackup(prefix string) string {
	return prefix + "-config-double_ends-backup"
}

func getDoubleEndsSmallflowIpsKey(prefix string) string {
	return prefix + "-config-double_ends-ips"
}

func getDoubleEndsMtimeKey(prefix string) string {
	return prefix + "-config-double_ends-mtime"
}

func getGlobalMtime(prefix string) string {
	return prefix + "-mtime"
}

func genDoubleEndsValueWithClose(doubleEndsKey string, toServiceName string) (string, error) {
	// key example: p-3ufc-beijing-configs-double_ends
	doubleEndsValue := utils.GetCache(doubleEndsKey)
	if doubleEndsValue == "" {
		golog.Error("[get doubleEndsValue failed close] [get cache failed] [doubleEndsKey: %v]", doubleEndsKey)
		return "", errors.New("get double ends value failed")
	}

	var doubleEndsMap map[string]interface{}
	if err := json.Unmarshal([]byte(doubleEndsValue), &doubleEndsMap); err != nil {
		golog.Error("[get doubleEndsValue failed close] [value is not json] [err: %v", err)
		return "", err
	}

	oriServicesMapSlice, ok := doubleEndsMap["services"].([]interface{})
	if !ok {
		golog.Error("[get doubleEndsValue failed close] [services is not []map[string]interface{} type]")
		return "", errors.New("type assert doubleendsmap[services] failed")
	}

	newServiceMapSlice := make([]map[string]interface{}, 0)
	for _, serviceMapInterface := range oriServicesMapSlice {
		serviceMap, ok := serviceMapInterface.(map[string]interface{})
		if !ok {
			errMsg := fmt.Sprintf("serviceMap is not map[string]interface{} type, is %v", reflect.TypeOf(serviceMapInterface))
			golog.Warn(errMsg)
			return "", errors.New(errMsg)
		}
		doubleEndType := getDoubleEndsType(serviceMap, toServiceName)
		if doubleEndType == TypeDoubleEndSingle {
			// 过滤掉已经注册的服务
			// 只过滤掉仅含有 to_service 字段。不过滤即含有from_service也含有 to_service
			continue
		}

		newServiceMapSlice = append(newServiceMapSlice, serviceMap)
	}

	// 已经注册了，则不需要再注册
	doubleEndsMap["services"] = newServiceMapSlice
	doubleEndsMap["all_service"] = 0

	deMapByte, err := json.Marshal(doubleEndsMap)
	if err != nil {
		golog.Error("[get doubleEndsValue failed witch close] [json marshal failed] [err: %v]", err)
		return "", err
	}
	return string(deMapByte), nil
}

/*
	 双端配置构成：{
		"all_service":
		"enable":
		"services":[
			{
				"from_ip"
				"from_service"
				"to_service":
			}
		]
	}

本方法只用于对 to_service 开双端，如果已存在(from_service, to_service)的关系，则将用 to_service 替换，原有的 (from_service，to_service)
*/
func genDoubleEndsValueWithAdd(doubleEndsKey string, toServiceName string) (string, error) {
	// key example: p-3ufc-beijing-configs-double_ends
	doubleEndsValue := utils.GetCache(doubleEndsKey)
	if doubleEndsValue == "" {
		golog.Error("[get doubleEndsValue failed close with add] [get cache failed] [doubleEndsKey: %v]", doubleEndsKey)
		return "", errors.New("get double ends value failed")
	}

	var doubleEndsMap map[string]interface{}
	if err := json.Unmarshal([]byte(doubleEndsValue), &doubleEndsMap); err != nil {
		golog.Error("[get doubleEndsValue failed with add] [value is not json] [err: %v", err)
		return "", err
	}

	oriServicesMapSlice, ok := doubleEndsMap["services"].([]interface{})
	if !ok {
		golog.Error("[get doubleEndsValue failed] [services is not []map[string]interface{} type]")
		return "", errors.New("type assert doubleendsmap[services] failed")
	}

	isRegister := false
	indexServiceMap := make(map[int]TypeDoubleEnd)
	for index, serviceMapInterface := range oriServicesMapSlice {
		serviceMap, ok := serviceMapInterface.(map[string]interface{})
		if !ok {
			errMsg := fmt.Sprintf("serviceMap is not map[string]interface{} type, is %v", reflect.TypeOf(serviceMapInterface))
			golog.Warn(errMsg)
			return "", errors.New(errMsg)
		}

		doubleEndType := getDoubleEndsType(serviceMap, toServiceName)
		if doubleEndType == TypeDoubleEndSingle {
			isRegister = true
			break
		}

		indexServiceMap[index] = doubleEndType
	}
	// 已经注册了，则不需要再注册
	if isRegister {
		return doubleEndsValue, nil
	}

	newServiceMapSlice := make([]map[string]interface{}, 0)
	for index, serviceMapInterface := range oriServicesMapSlice {
		switch indexServiceMap[index] {
		case TypeDoubleEndOther:
			// from_service, to_service 都指定了的话可以删掉
			continue
		case TypeDoubleEndWithoutToService:
			serviceMap, ok := serviceMapInterface.(map[string]interface{})
			if !ok {
				errMsg := fmt.Sprintf("serviceMap is not map[string]interface{} type, is %v", reflect.TypeOf(serviceMapInterface))
				golog.Warn(errMsg)
				return "", errors.New(errMsg)
			}

			newServiceMapSlice = append(newServiceMapSlice, serviceMap)
		}
	}
	newServiceMap := make(map[string]interface{})
	newServiceMap["to_service"] = toServiceName
	newServiceMapSlice = append(newServiceMapSlice, newServiceMap)

	doubleEndsMap["services"] = newServiceMapSlice
	doubleEndsMap["enable"] = 1
	doubleEndsMap["all_service"] = 0

	deMapByte, err := json.Marshal(doubleEndsMap)
	if err != nil {
		golog.Error("[get doubleEndsValue failed] [json marshal failed] [err: %v]", err)
		return "", err
	}
	return string(deMapByte), nil
}

func getDoubleEndsType(m map[string]interface{}, toService string) TypeDoubleEnd {
	oriToService, ok := m["to_service"]
	if !ok {
		return TypeDoubleEndOther
	}
	if oriToService != toService {
		return TypeDoubleEndWithoutToService
	}

	if len(m) == 1 { // 只有 to_service
		return TypeDoubleEndSingle
	}
	return TypeDoubleEndOther
}

func GetSmallFlowConfigDoubleEndIps(serviceName string) []string {
	res := make([]string, 0)
	prefix := BaseConf.Cache.Prefix
	baseIdc := BaseConf.Idc.Base
	key := fmt.Sprintf("%s-%s-config-double_ends-ips", prefix, baseIdc)
	redisData, err := utils.Hgetall(key)
	if err != nil {
		return res
	}
	for k := range redisData {
		res = append(res, k)
	}
	return res
}
