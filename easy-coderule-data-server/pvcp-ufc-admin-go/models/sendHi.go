package models

import (
	"fmt"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/simplejson"
)

func SendHi(idc string, method string, types string, Name string, content string) {
	str := fmt.Sprintf("idc:%s Method:%s types:%s name:%s content:%s", idc, method, types, Name, content)

	data := simplejson.New()
	message := make(map[string]interface{})
	var Body []map[string]string
	oneBody := make(map[string]string)
	oneBody["type"] = "TEXT"
	oneBody["content"] = str
	Body = append(Body, oneBody)

	message["body"] = Body
	data.Set("message", message)

	js, err := data.MarshalJSON()
	if err != nil {
		golog.Warn("[sendHi post fail, marshal json failed,  error is: %s]", err.Error())
		return
	}

	header := make(map[string]string)
	header["Content-Type"] = "application/json"

	var connectTimeoutMs time.Duration = 1000   // 1000ms
	var readwriteTimeoutMs time.Duration = 3000 // 3000ms

	response, err := httpclient.Post(BaseConf.Alarm.Url, header, connectTimeoutMs, readwriteTimeoutMs, string(js), nil)
	if err != nil {
		golog.Warn("[sendHi post fail, error is :%s, postbody is:%s]", err.Error(), content)
		return
	}

	if response.StatusCode != 200 {
		golog.Warn("[sendHi post fail, status code = %d response body is %s]", response.StatusCode, string(response.Body))
		return
	}

	res, err := simplejson.NewJson(response.Body)
	if err != nil {
		golog.Warn("[sendHi post fail, unmarshal json failed, status code = %d response body is %s]", response.StatusCode, string(response.Body))
		return
	}

	errno := res.Get("errcode").MustInt()
	if errno != 0 {
		golog.Warn("[sendHi post fail, status code = %d response body is %s]", response.StatusCode, string(response.Body))
		return
	}
}
