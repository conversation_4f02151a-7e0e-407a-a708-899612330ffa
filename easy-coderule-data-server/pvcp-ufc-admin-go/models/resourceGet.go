package models

import (
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/httpclient"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/simplejson"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

func GetBnsByGroup(gorupName string) []string {
	cmd := exec.Command("get_services_by_path", gorupName)
	out, _ := cmd.Output()
	bnsNames := strings.Split(string(out), "\n")
	bnsNames = bnsNames[:len(bnsNames)-1]
	return bnsNames
}

func GetBnsInstanceCount(bnsName string) int {
	cmdString := "get_instance_by_service " + bnsName + " | wc -l"
	cmd := exec.Command("bash", "-c", cmdString)
	out, err := cmd.Output()
	if err != nil {
		return 0
	}
	str := strings.Split(string(out), "\n")[0]
	res, err := strconv.Atoi(str)
	if err != nil {
		return 0
	}
	return res
}

func GetResourceData(bns []string, timeStart time.Time, timeEnd time.Time) (*simplejson.Json, error) {
	dataAvg := make(chan []map[string]interface{})
	dataAvgErr := make(chan error)
	dataAvgCount := make(chan int)

	dataMax := make(chan []map[string]interface{})
	dataMaxErr := make(chan error)
	dataMaxCount := make(chan int)

	go get(bns, timeStart, timeEnd, "avg", dataAvg, dataAvgErr, dataAvgCount)
	go get(bns, timeStart, timeEnd, "max", dataMax, dataMaxErr, dataMaxCount)

	var errAvg, errMax error
	var count int
	var resAvg, resMax []map[string]interface{}

	resAvg = <-dataAvg
	errAvg = <-dataAvgErr
	count = <-dataAvgCount
	resMax = <-dataMax
	errMax = <-dataMaxErr
	count = <-dataMaxCount

	if errAvg != nil || errMax != nil {
		return nil, &utils.UfcadminError{ErrCode: 400, ErrMsg: "can not get information from noah"}
	}

	for i := 0; i < count; i++ {
		resAvg[i]["CpuUseMax"] = resMax[i]["CpuUseMax"]
		resAvg[i]["MemUseMax"] = resMax[i]["MemUseMax"]
		resAvg[i]["NetInUseMax"] = resMax[i]["NetInUseMax"]
		resAvg[i]["NetOutUseMax"] = resMax[i]["NetOutUseMax"]
		bns := resAvg[i]["Bns"].(string)
		resAvg[i]["InstanceCount"] = GetBnsInstanceCount(bns)
	}

	res := simplejson.New()
	res.Set("count", count)
	res.Set("resource_list", resAvg)
	return res, nil
}

func get(bns []string, timeStart time.Time, timeEnd time.Time, sampleFunc string, res chan []map[string]interface{}, err chan error, count chan int) {
	if len(bns) > 0 && !strings.Contains(bns[0], "orp") && !strings.Contains(bns[0], "oxp") {
		noahRes, c, noahErr := getPhysicalFromNoah(bns, timeStart, timeEnd, sampleFunc)
		res <- noahRes
		err <- noahErr
		count <- c
	} else {
		noahRes, c, noahErr := getFromNoah(bns, timeStart, timeEnd, sampleFunc)
		res <- noahRes
		err <- noahErr
		count <- c
	}
}

func getFromNoah(bns []string, timeStart time.Time, timeEnd time.Time, sampleFunc string) ([]map[string]interface{}, int, error) {
	startTime := timeStart.Format("20060102150405")
	endTime := timeEnd.Format("20060102150405")
	interval := timeEnd.Unix() - timeStart.Unix()

	var bnss string
	for i := 0; i < len(bns); i++ {
		bnss += bns[i]
		if i != len(bns)-1 {
			bnss += ","
		}
	}

	url := fmt.Sprintf(BaseConf.Noah.Url+
		"namespaces=%s&items=CPU_USED_PERCENT.MATRIX_avg,MEM_USED_PERCENT.MATRIX_avg,NET_IN_USED_PERCENT.MATRIX_avg,NET_OUT_USED_PERCENT.MATRIX_avg,CPU_QUOTA.MATRIX_avg"+
		"&start=%s&end=%s&interval=%d&sampleFunc=%s", bnss, startTime, endTime, interval, sampleFunc)

	head := make(map[string]string)
	head["Accept"] = "text/plain"
	var connectTimeoutMs time.Duration = 100     // 100ms
	var readwriteTimeoutMs time.Duration = 10000 // 10000ms
	response, err := httpclient.Get(url, head, connectTimeoutMs, readwriteTimeoutMs, nil)
	if err != nil {
		golog.Warn("[get information from noah failed]")
		return nil, 0, &utils.UfcadminError{ErrCode: 400, ErrMsg: "can not get information from noah"}
	}

	json, err := simplejson.NewJson(response.Body)
	if err != nil {
		golog.Warn("[get information from noah failed, can not unmashal the body]")
		return nil, 0, &utils.UfcadminError{ErrCode: 400, ErrMsg: "can not get information from noah"}
	}
	r, c := transfer(json, sampleFunc)
	return r, c, nil
}

func transfer(json *simplejson.Json, sampleFunc string) ([]map[string]interface{}, int) {
	noahData := json.Get("data").MustArray()
	count := len(noahData)
	var dataList []map[string]interface{}
	var str string
	if sampleFunc == "avg" {
		str = "Avg"
	} else {
		str = "Max"
	}

	for i := 0; i < len(noahData); i++ {
		data := make(map[string]interface{})
		data["Bns"] = json.Get("data").GetIndex(i).Get("NameSpace").MustString()

		_, err := json.Get("data").GetIndex(i).Get("Items").Get("CPU_USED_PERCENT.MATRIX_avg").Array()
		if err == nil {
			data["CpuUse"+str] = json.Get("data").GetIndex(i).Get("Items").Get("CPU_USED_PERCENT.MATRIX_avg").GetIndex(0).Get("Value").MustFloat64()
		} else {
			data["CpuUse"+str] = nil
		}

		_, err = json.Get("data").GetIndex(i).Get("Items").Get("MEM_USED_PERCENT.MATRIX_avg").Array()
		if err == nil {
			data["MemUse"+str] = json.Get("data").GetIndex(i).Get("Items").Get("MEM_USED_PERCENT.MATRIX_avg").GetIndex(0).Get("Value").MustFloat64()
		} else {
			data["MemUse"+str] = nil
		}

		_, err = json.Get("data").GetIndex(i).Get("Items").Get("NET_IN_USED_PERCENT.MATRIX_avg").Array()
		if err == nil {
			data["NetInUse"+str] = json.Get("data").GetIndex(i).Get("Items").Get("NET_IN_USED_PERCENT.MATRIX_avg").GetIndex(0).Get("Value").MustFloat64()
		} else {
			data["NetInUse"+str] = nil
		}

		_, err = json.Get("data").GetIndex(i).Get("Items").Get("NET_OUT_USED_PERCENT.MATRIX_avg").Array()
		if err == nil {
			data["NetOutUse"+str] = json.Get("data").GetIndex(i).Get("Items").Get("NET_OUT_USED_PERCENT.MATRIX_avg").GetIndex(0).Get("Value").MustFloat64()
		} else {
			data["NetOutUse"+str] = nil
		}

		_, err = json.Get("data").GetIndex(i).Get("Items").Get("CPU_QUOTA.MATRIX_avg").Array()
		if err == nil {
			data["CpuQuota"] = json.Get("data").GetIndex(i).Get("Items").Get("CPU_QUOTA.MATRIX_avg").GetIndex(0).Get("Value").MustFloat64()
		} else {
			data["CpuQuota"] = nil
		}

		dataList = append(dataList, data)
	}
	return dataList, count
}

func getPhysicalFromNoah(bns []string, timeStart time.Time, timeEnd time.Time, sampleFunc string) ([]map[string]interface{}, int, error) {
	startTime := timeStart.Format("20060102150405")
	endTime := timeEnd.Format("20060102150405")
	interval := timeEnd.Unix() - timeStart.Unix()

	var bnss string
	for i := 0; i < len(bns); i++ {
		bnss += bns[i]
		if i != len(bns)-1 {
			bnss += ","
		}
	}

	url := fmt.Sprintf(BaseConf.Noah.Url+
		"namespaces=%s&items=CPU_IDLE_avg,MEM_USED_PERCENT_avg,NET_MAX_NIC_INOUT_PERCENT_avg,CPU_QUOTA.MATRIX_avg"+
		"&start=%s&end=%s&interval=%d&sampleFunc=%s", bnss, startTime, endTime, interval, sampleFunc)

	head := make(map[string]string)
	head["Accept"] = "text/plain"
	var connectTimeoutMs time.Duration = 100     // 100ms
	var readwriteTimeoutMs time.Duration = 10000 // 10000ms
	response, err := httpclient.Get(url, head, connectTimeoutMs, readwriteTimeoutMs, nil)
	if err != nil {
		golog.Warn("[get information from noah failed]")
		return nil, 0, &utils.UfcadminError{ErrCode: 400, ErrMsg: "can not get information from noah"}
	}

	json, err := simplejson.NewJson(response.Body)
	if err != nil {
		golog.Warn("[get information from noah failed, can not unmashal the body]")
		return nil, 0, &utils.UfcadminError{ErrCode: 400, ErrMsg: "can not get information from noah"}
	}
	// cpu_idle 单独最小值
	if sampleFunc == "max" {
		url = fmt.Sprintf(BaseConf.Noah.Url+"namespaces=%s&items=CPU_IDLE_avg"+"&start=%s&end=%s&interval=%d&sampleFunc=min", bnss, startTime, endTime, interval)
		response, err := httpclient.Get(url, head, connectTimeoutMs, readwriteTimeoutMs, nil)
		if err != nil {
			golog.Warn("[get information from noah failed]")
			return nil, 0, &utils.UfcadminError{ErrCode: 400, ErrMsg: "can not get information from noah"}
		}

		json_min, err := simplejson.NewJson(response.Body)
		if err != nil {
			golog.Warn("[get information from noah failed, can not unmashal the body]")
			return nil, 0, &utils.UfcadminError{ErrCode: 400, ErrMsg: "can not get information from noah"}
		}
		r, c := transferPhysical(json, json_min, sampleFunc)
		return r, c, nil
	}

	r, c := transferPhysical(json, nil, sampleFunc)
	return r, c, nil
}

func transferPhysical(json *simplejson.Json, json_min *simplejson.Json, sampleFunc string) ([]map[string]interface{}, int) {
	noahData := json.Get("data").MustArray()
	count := len(noahData)
	var dataList []map[string]interface{}
	var str string

	var noahDataMin []interface{}
	if sampleFunc == "avg" {
		str = "Avg"
	} else {
		str = "Max"
		noahDataMin = json_min.Get("data").MustArray()
	}

	for i := 0; i < len(noahData); i++ {
		data := make(map[string]interface{})
		data["Bns"] = json.Get("data").GetIndex(i).Get("NameSpace").MustString()

		if sampleFunc == "max" {
			for j := 0; j < len(noahDataMin); j++ {
				if data["Bns"] == json_min.Get("data").GetIndex(j).Get("NameSpace").MustString() {
					_, err := json_min.Get("data").GetIndex(j).Get("Items").Get("CPU_IDLE_avg").Array()
					if err == nil {
						data["CpuUse"+str] = 100 - json_min.Get("data").GetIndex(j).Get("Items").Get("CPU_IDLE_avg").GetIndex(0).Get("Value").MustFloat64()
					} else {
						data["CpuUse"+str] = nil
					}
				}
			}
		} else {
			_, err := json.Get("data").GetIndex(i).Get("Items").Get("CPU_IDLE_avg").Array()
			if err == nil {
				data["CpuUse"+str] = 100 - json.Get("data").GetIndex(i).Get("Items").Get("CPU_IDLE_avg").GetIndex(0).Get("Value").MustFloat64()
			} else {
				data["CpuUse"+str] = nil
			}
		}

		_, err := json.Get("data").GetIndex(i).Get("Items").Get("MEM_USED_PERCENT_avg").Array()
		if err == nil {
			data["MemUse"+str] = json.Get("data").GetIndex(i).Get("Items").Get("MEM_USED_PERCENT_avg").GetIndex(0).Get("Value").MustFloat64()
		} else {
			data["MemUse"+str] = nil
		}

		_, err = json.Get("data").GetIndex(i).Get("Items").Get("NET_MAX_NIC_INOUT_PERCENT_avg").Array()
		if err == nil {
			data["NetInUse"+str] = json.Get("data").GetIndex(i).Get("Items").Get("NET_MAX_NIC_INOUT_PERCENT_avg").GetIndex(0).Get("Value").MustFloat64()
		} else {
			data["NetInUse"+str] = nil
		}

		_, err = json.Get("data").GetIndex(i).Get("Items").Get("NET_MAX_NIC_INOUT_PERCENT_avg").Array()
		if err == nil {
			data["NetOutUse"+str] = json.Get("data").GetIndex(i).Get("Items").Get("NET_MAX_NIC_INOUT_PERCENT_avg").GetIndex(0).Get("Value").MustFloat64()
		} else {
			data["NetOutUse"+str] = nil
		}

		_, err = json.Get("data").GetIndex(i).Get("Items").Get("CPU_QUOTA.MATRIX_avg").Array()
		if err == nil {
			data["CpuQuota"] = json.Get("data").GetIndex(i).Get("Items").Get("CPU_QUOTA.MATRIX_avg").GetIndex(0).Get("Value").MustFloat64()
		} else {
			data["CpuQuota"] = nil
		}

		dataList = append(dataList, data)
	}
	return dataList, count
}
