package models

import (
	"fmt"
	"time"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

func GetTime() int64 {
	t := time.Now()
	return t.Unix()
}

func GetPrefix(idc string) string {
	return fmt.Sprintf("%s-%s", BaseConf.Cache.Prefix, idc)
}

func SetGlobalVersion(prefix string) bool {
	key := fmt.Sprintf("%s-mtime", prefix)
	version := GetTime()

	re := utils.SetCache(key, version)
	if !re {
		return false
	}

	return true
}
