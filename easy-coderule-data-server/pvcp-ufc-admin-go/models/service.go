package models

import (
	"errors"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

type ServiceModel struct {
	ServiceTable string
	StreamTable  string
}

func IsKeyInUfcMetaInfo(key string) (bool, error) {
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		golog.Warn("get ufc_meta_info failed, key:%s, err:%v", key, err)
		return false, err
	}

	var exist bool
	sql := fmt.Sprintf("SELECT EXISTS(SELECT 1 FROM %s WHERE ufc_key='%s');", utils.UfcMetaInfoTableName, key)
	err = db.QueryRow(sql).Scan(&exist)
	if err != nil {
		golog.Warn("get ufc_meta_info failed, key:%s, err:%v", key, err)
		return false, err
	}

	return exist, nil
}

func AddOrUpdateUfcMetaInfo(key string, value string) error {
	keyExist, err := IsKeyInUfcMetaInfo(key)
	if err != nil {
		golog.Warn("[check key exist] [key:%s] [new value: %v] [err:%v]", key, value, err)
		return err
	}

	if !keyExist {
		return AddUfcMetaInfo(key, value)
	}

	return UpdateUfcMetaInfo(key, value)
}

// 批量新增记录
func AddUfcMetaInfos(kvpairs []string) error {
	if len(kvpairs)%2 != 0 {
		return errors.New("kv pairs is illegal")
	}

	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		golog.Warn("kv paris %v, add ufc_meta_info failed, error is %v", kvpairs, err)
		return err
	}

	if len(kvpairs) < 2 {
		return errors.New("kv pairs is illegal")
	}

	sql := fmt.Sprintf("INSERT INTO %s (ufc_key, ufc_value) VALUES ('%s', '%s'),", utils.UfcMetaInfoTableName, kvpairs[0], kvpairs[1])
	for i := 2; i < len(kvpairs); i += 2 {
		sql += fmt.Sprintf("('%s', '%s'),", kvpairs[i], kvpairs[i+1])
	}
	sql = strings.TrimSuffix(sql, ",")
	sql += ";"

	golog.Info("[add ufc_meta_infos] [sql:%s]", sql)
	_, err = db.Exec(sql)
	if err != nil {
		golog.Warn("update ufc_meta_info failed, error:%v", err)
		return err
	}

	return nil
}

func GetUfcMetaInfo(key string) (string, error) {
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		golog.Warn("key %s, get ufc_meta_info failed, error is %v", key, err)
		return "", err
	}

	sql := fmt.Sprintf("SELECT ufc_value FROM %s WHERE ufc_key='%s';", utils.UfcMetaInfoTableName, key)

	golog.Info("[get ufc_meta_info] [sql:%s]", sql)
	row := db.QueryRow(sql)

	var ufcValue string
	if err := row.Scan(&ufcValue); err != nil {
		golog.Warn("key %s, get ufc_meta_info failed, error is %v", key, err)
		return "", err
	}
	return ufcValue, nil
}

func AddUfcMetaInfo(key, value string) error {
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		golog.Warn("key %s, value %s, add ufc_meta_info failed, error is %v", key, value, err)
		return err
	}

	sql := fmt.Sprintf("INSERT INTO %s (ufc_key, ufc_value) VALUES ('%s', '%s');", utils.UfcMetaInfoTableName, key, value)

	golog.Info("[add ufc_meta_info] [sql:%s]", sql)
	_, err = db.Exec(sql)
	if err != nil {
		golog.Warn("key %s, value %s, add ufc_meta_info failed, error is %v", key, value, err)
		return err
	}
	return nil
}

func UpdateUfcMetaInfo(key, value string) error {
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		golog.Warn("key %s, value %s, update ufc_meta_info failed, error is %v", key, value, err)
		return err
	}

	sql := fmt.Sprintf("UPDATE %s SET ufc_value='%s' WHERE ufc_key='%s';", utils.UfcMetaInfoTableName, value, key)

	golog.Info("[update ufc_meta_info] [sql:%s]", sql)
	_, err = db.Exec(sql)
	if err != nil {
		golog.Warn("key %s, value %s, update ufc_meta_info failed, error is %v", key, value, err)
		return err
	}
	return nil
}

func (m *ServiceModel) Add(param map[string]interface{}) int64 {
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		return 0
	}

	colume, value := "", ""
	sql := fmt.Sprintf("INSERT INTO %s ", m.ServiceTable)
	for kk, vv := range param {
		if _, ok := ReqBodyMysqlParamMap[kk]; !ok {
			continue
		}

		colume += " " + kk + ","
		switch v := vv.(type) {
		case string:
			value += " '" + v + "',"
		case float64:
			value += strconv.Itoa(int(v)) + ","
		case int64:
			value += strconv.Itoa(int(v)) + ","
		case bool:
			value += fmt.Sprintf("%v", v) + ","
		default:
			golog.Error("[add service failed][type error][type: %s]", reflect.TypeOf(vv))
		}
	}

	colume = strings.TrimRight(colume, ",")
	value = strings.TrimRight(value, ",")

	sql += "(" + colume + ") VALUES (" + value + ");"
	re, err := db.Exec(sql)
	if err != nil {
		golog.Error("[exec sql failed] [sql: %s] [err: %s]", sql, err.Error())
		return 0
	}

	id, err := re.LastInsertId()
	if err != nil {
		golog.Error("[last insert in failed][sql: %s][err: %s]", sql, err.Error())
		return 0
	}

	return id
}

func (m *ServiceModel) Update(serviceName string, data map[string]interface{}) bool {
	sql := fmt.Sprintf("UPDATE `%s` SET ", m.ServiceTable)
	for kk, vv := range data {
		if _, ok := ReqBodyMysqlParamMap[kk]; !ok {
			continue
		}

		sql += " " + kk + "="
		switch v := vv.(type) {
		case string:
			sql += " '" + v + "',"
		case float64:
			sql += " " + strconv.Itoa(int(v)) + ","
		case int64:
			sql += " " + strconv.Itoa(int(v)) + ","
		case bool:
			sql += fmt.Sprintf("%v", v) + ","
		default:
			golog.Error("[update service failed][type error][type: %s]", reflect.TypeOf(vv))
		}
	}

	sql = strings.TrimRight(sql, ",")
	sql += " WHERE service_name = '" + serviceName + "'"
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		return false
	}

	_, err = db.Exec(sql)
	if err != nil {
		golog.Error("[exec sql failed][err: %s]", err.Error())
		return false
	}

	return true
}

func (m *ServiceModel) Query(key string, value string) (map[string]interface{}, error) {
	sql := fmt.Sprintf("SELECT * FROM `%s` WHERE %s='%s'", m.ServiceTable, key, value)
	res, err := m.QueryRows(sql)
	if err != nil {
		golog.Error("[query failed][sql: %s][err: %s]", sql, err.Error())
		return nil, err
	}

	return res, nil
}

func (m *ServiceModel) Delete(serviceName string) (map[string]interface{}, error) {
	sql := fmt.Sprintf("DELETE FROM `%s` WHERE service_name = '%s'", m.ServiceTable, serviceName)
	res, err := m.QueryRows(sql)
	if err != nil {
		golog.Error("[delete failed][sql: %s][err: %s]", sql, err.Error())
		return nil, err
	}

	return res, nil
}

func (m *ServiceModel) CheckIsExist(serviceName string) bool {
	var cnt int = 0
	sql := fmt.Sprintf("SELECT COUNT(*) as cnt FROM `%s` WHERE service_name = '%s'", m.ServiceTable, serviceName)
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		golog.Error("[check service is exist failed][err: %s]", err.Error())
		return true
	}

	err = db.QueryRow(sql).Scan(&cnt)
	if err != nil {
		golog.Error("[exec sql failed][sql: %s][err: %s]", sql, err.Error())
		return true
	}

	if cnt > 0 {
		return true
	}

	return false
}

// 查看stream这张表里面是否已经注册了toService name
func (m *ServiceModel) CheckStreamIsExist(toServiceName string) bool {
	var cnt int = 0
	sql := fmt.Sprintf("SELECT COUNT(*) as cnt FROM `%s` WHERE service_name = '%s'", m.StreamTable, toServiceName)
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		return true
	}

	err = db.QueryRow(sql).Scan(&cnt)
	if err != nil {
		golog.Error("[exec sql failed][err: %s]", err.Error())
		return true
	}

	if cnt > 0 {
		return true
	}

	return false
}

// 根据service name获取id
func (m *ServiceModel) GetStreamID(toServiceName string) int32 {
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		return -1
	}

	sql := fmt.Sprintf("SELECT id FROM `%s` WHERE service_name='%s'", m.StreamTable, toServiceName)
	var id int32
	err = db.QueryRow(sql).Scan(&id)
	if err != nil {
		return -1
	}
	return id
}

// 把表中service 对应的link_index 加一并且返回更新后的值
func (m *ServiceModel) UpdateStreamIndex(toServiceName string) int32 {
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		return -1
	}

	sql := fmt.Sprintf("SELECT link_index FROM `%s` WHERE service_name='%s'", m.StreamTable, toServiceName)
	var index int32
	err = db.QueryRow(sql).Scan(&index)
	if err != nil {
		return -1
	}

	index++
	sql = fmt.Sprintf("UPDATE `%s` SET link_index=%d WHERE service_name='%s'", m.StreamTable, index, toServiceName)
	_, err = db.Exec(sql)
	if err != nil {
		return -1
	}
	return index
}

// 向stream表中插入新的to service, 自增id作为分配映射ip的index，自增id从1开始
// link_index 记录该service上次分配给from service的index，出事值是0
func (m *ServiceModel) AddStream(toServiceName string) int64 {
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		return 0
	}

	sql := fmt.Sprintf("INSERT INTO %s (service_name, link_index) VALUES ('%s', 0);", m.StreamTable, toServiceName)

	re, err := db.Exec(sql)
	if err != nil {
		return 0
	}

	id, err := re.LastInsertId()
	if err != nil {
		return 0
	}

	return id
}

func (m *ServiceModel) QueryAll(start int, size int) (map[string]interface{}, error) {
	sql := fmt.Sprintf("SELECT * FROM `%s` ORDER BY id DESC LIMIT %d, %d", m.ServiceTable, start, size)
	res, err := m.QueryRows(sql)
	if err != nil {
		golog.Error("[exec sql failed][sql: %s][err: %s]", sql, err.Error())
		return res, err
	}

	return res, nil
}
func (m *ServiceModel) QueryTotal() (int, error) {
	var cnt = 0
	sql := fmt.Sprintf("SELECT COUNT(*) as cnt FROM `%s`", m.ServiceTable)
	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		return cnt, err
	}

	err = db.QueryRow(sql).Scan(&cnt)
	if err != nil {
		return cnt, &utils.UfcadminError{ErrCode: 503, ErrMsg: "db queryrow error"}
	}

	return cnt, nil
}

func (m *ServiceModel) QueryRows(sql string) (map[string]interface{}, error) {
	res := make(map[string]interface{})

	db, err := utils.UfcadminDB.GetInstance()
	if err != nil {
		return res, err
	}

	rows, err := db.Query(sql)
	if err != nil {
		return res, &utils.UfcadminError{ErrCode: 503, ErrMsg: "db query error"}
	}

	column, _ := rows.Columns()
	values := make([][]byte, len(column))
	scans := make([]interface{}, len(column))
	for i := range values {
		scans[i] = &values[i]
	}

	i := 0
	for rows.Next() {
		if err := rows.Scan(scans...); err != nil {
			return res, err
		}

		row := make(map[string]string)
		for k, v := range values {
			key := column[k]
			row[key] = string(v)
		}

		index := strconv.Itoa(i)
		res[index] = row
		i++
	}

	if rows.Err() != nil {
		golog.Error("[rows err exist] [err: %s]", rows.Err())
		return res, &utils.UfcadminError{ErrCode: 503, ErrMsg: "db query error"}
	}

	return res, nil
}
