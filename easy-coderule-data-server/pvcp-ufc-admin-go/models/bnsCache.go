package models

import (
	"encoding/json"
	"errors"
	"fmt"
	"math/rand"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

type BnsCache struct {
	IsFakeBns bool
	Idc       string
	Bns       string
	Mutex     sync.Mutex
	Errors    []error
}

func (m *BnsCache) AppendErrorWithLockInBnsCache(err error) {
	m.Mutex.Lock()
	defer m.Mutex.Unlock()

	m.Errors = append(m.Errors, err)
}

// 删除bns配置，如果有idcmap的配置就把idcmap也删掉
func (m *BnsCache) DeleteBns() bool {
	if err := m.deleteOne("bns"); err != nil {
		m.AppendErrorWithLockInBnsCache(err)
		// 这里不能先退出，仍需要继续删后续的 idcmap 信息
	}

	idcMapEnable := m.isAdded("idcmap")
	if idcMapEnable {
		if err := m.deleteOne("idcmap"); err != nil {
			m.AppendErrorWithLockInBnsCache(err)
		}
	}
	return true
}

// 删除bns或者idcmap配置
func (m *BnsCache) deleteOne(ac string) error {
	pre := GetPrefix(m.Idc)
	cntKey := fmt.Sprintf("%s-%ss-cnt", pre, ac)
	cnt := utils.GetIntCache(cntKey)
	if cnt < 0 {
		// 获取cnt失败，发送hi报警
		SendHi(m.Idc, "delete", ac, m.Bns, "get "+ac+" cnt failed")
		msg := fmt.Sprintf("[Idc = %s] [%s = %s] [get %s cnt failed, delete this %s fail]", m.Idc, ac, m.Bns, m.Bns, ac)
		golog.Warn(msg)
		return errors.New(msg)
	}
	// 找到需要删除的bns或者idcmap的index
	index := m.GetIndex(m.Idc, ac, cnt)
	if index < 0 {
		SendHi(m.Idc, "delete", ac, m.Bns, "this "+ac+" is not exit in redis")
		msg := fmt.Sprintf("[Idc = %s] [%s = %s] [this %s is not exit in redis]", m.Idc, ac, m.Bns, ac)
		golog.Warn(msg)
		return errors.New(msg)
	}
	golog.Info("[Idc = %s] [%s = %s] [delete index is %d]", m.Idc, ac, m.Bns, index)

	// 最后一个bns或者idcmap name
	lastNameKey := fmt.Sprintf("%s-%ss-%d", pre, ac, cnt-1)
	lastName := utils.GetCache(lastNameKey)
	if lastName == "" {
		// 获取失败
		SendHi(m.Idc, "delete", ac, m.Bns, "get last "+ac+" name failed")
		msg := fmt.Sprintf("[Idc = %s] [%s = %s] [get last %s name failed]", m.Idc, lastNameKey, lastName, lastNameKey)
		golog.Warn(msg)
		return errors.New(msg)
	}

	// 将bns/idcmap cnt减1，最后一个bns/idcmap name 覆盖到index上
	// 用mset做成一个原子操作
	changeNameKey := fmt.Sprintf("%s-%ss-%d", pre, ac, index)
	re := utils.MsetCache(cntKey, cnt-1, changeNameKey, lastName)
	if !re {
		// 写入失败
		SendHi(m.Idc, "delete", ac, m.Bns, "get last name failed")
		msg := fmt.Sprintf("[Idc = %s] [%s = %s] [get last %s name failed]", m.Idc, ac, m.Bns, ac)
		golog.Warn(msg)
		return errors.New(msg)
	}

	// 把需要删除的service相关信息删掉
	delInfoKey := fmt.Sprintf("%s-%s-%s", pre, ac, m.Bns)
	delMtimeKey := fmt.Sprintf("%s-%s-%s-mtime", pre, ac, m.Bns)
	delCntKey := fmt.Sprintf("%s-%ss-%d", pre, ac, cnt-1)

	if re := utils.DelCache(delInfoKey); re != 1 {
		err := fmt.Errorf("del bns info key failed, redis key is %s", delInfoKey)
		m.AppendErrorWithLockInBnsCache(err)
	}
	if re := utils.DelCache(delMtimeKey); re != 1 {
		err := fmt.Errorf("del bns mtime key failed, redis key is %s", delInfoKey)
		m.AppendErrorWithLockInBnsCache(err)
	}
	if re := utils.DelCache(delCntKey); re != 1 {
		err := fmt.Errorf("del bns cnt key failed, redis key is %s", delInfoKey)
		m.AppendErrorWithLockInBnsCache(err)
	}

	golog.Info("[Idc = %s] [%s = %s] [delete %s success]", m.Idc, ac, m.Bns, m.Bns)
	return nil
}

// 找到bns或者idcmap的index
func (m *BnsCache) GetIndex(idc, ac string, cnt int64) int64 {
	for i := int64(0); i < cnt; i++ {
		pre := GetPrefix(idc)
		key := fmt.Sprintf("%s-%ss-%d", pre, ac, i)
		nameTemp := utils.GetCache(key)
		// 某一个key获取失败打个日志，但是继续往下走
		if nameTemp == "" {
			golog.Warn("[Idc = " + idc + "] [get " + key + " fail]")
			continue
		}

		if nameTemp == m.Bns {
			return i
		}
	}
	return -1
}

func (m *BnsCache) Add(idcMapEnable bool, serviceConfig map[string]interface{}) bool {
	if m.IsFakeBns {
		return m.AddFakeBns(idcMapEnable, serviceConfig)
	}
	return m.AddRealBns(idcMapEnable)
}

func (m *BnsCache) AddFakeBns(idcMapEnable bool, serviceConfig map[string]interface{}) bool {
	if idcMapEnable {
		golog.Error("add fake bns failed, because idcmap enable is true")
		return false
	}
	if m.isAdded("bns") {
		golog.Error("fake bns is added, you can call the update to change it")
		return false
	}

	fakeBnsListData, ok := serviceConfig["fake_bns_list_data"].(map[string]interface{})
	if !ok {
		golog.Error("fake_bns_list_data type is %v, not map[string]interface{}", reflect.TypeOf(serviceConfig["fake_bns_list_data"]))
		return false
	}

	addrSliceInterface, ok := fakeBnsListData[m.Bns].([]interface{})
	if !ok {
		golog.Error("the addresses of bns %s type is not []interface{}, is %s", m.Bns, reflect.TypeOf(fakeBnsListData[m.Bns]))
		return false
	}

	var addrSlice []string
	for _, item := range addrSliceInterface {
		addr, ok := item.(string)
		if !ok {
			msg := fmt.Sprintf("the addresses of bns %s type is not string, is %s ", m.Bns, reflect.TypeOf(item).String())
			golog.Error("[msg: %s]", msg)
			return false
		}
		addrSlice = append(addrSlice, addr)
	}

	if len(addrSlice) == 0 {
		addrSlice = []string{}
	}

	addCloudCacheOK := m.AddCloudCache("bns", addrSlice, map[string][]string{})
	if !addCloudCacheOK {
		bnsStr := fmt.Sprintf("%s", addrSlice)
		golog.Error("[msg: add fakebns to redis failed] [bns: %s] [bnslist: %s]", bnsStr, bnsStr)
		return false
	}
	return addCloudCacheOK
}

func (m *BnsCache) UpdateFakeBns(idcMapEnable bool, serviceConfig map[string]interface{}) bool {
	if idcMapEnable {
		golog.Error("add fake bns failed, because idcmap enable is true")
		return false
	}

	ok := m.isAdded("bns")
	if !ok {
		// 没有add 时update 失败
		golog.Error("update fakebns failed, because redis cache is not exist")
		return false
	}

	fakeBnsListData, ok := serviceConfig["fake_bns_list_data"].(map[string]interface{})
	if !ok {
		golog.Error("fake_bns_list_data type is %v, not map[string]interface{}", reflect.TypeOf(serviceConfig["fake_bns_list_data"]))
		return false
	}

	addrSliceInterface, ok := fakeBnsListData[m.Bns].([]interface{})
	if !ok {
		golog.Error("the addresses of bns %s type is not []interface{}, is %s", m.Bns, reflect.TypeOf(addrSliceInterface))
		return false
	}

	var addrSlice []string
	for _, item := range addrSliceInterface {
		addr, ok := item.(string)
		if !ok {
			msg := fmt.Sprintf("the addresses of bns %s type is not string, is %s ", m.Bns, reflect.TypeOf(item).String())
			golog.Error("[msg: %s]", msg)
			return false
		}
		addrSlice = append(addrSlice, addr)
	}

	if len(addrSlice) == 0 {
		addrSlice = []string{}
	}

	saveCloudCacheOK := m.SaveCloudCache("bns", addrSlice, map[string][]string{})
	if !saveCloudCacheOK {
		golog.Error("[add bns cloud cache failed] [bns: %s] ", m.Bns)
		return false
	}
	return true
}

func (m *BnsCache) AddRealBns(idcMapEnable bool) bool {
	ac := "bns"
	ok := m.isAdded(ac)
	if ok {
		return true
	}

	ips := m.GetBnsIps(false)
	if len(ips) < 1 {
		SendHi(m.Idc, "add", "bns", m.Bns, "bns-has-no-ips")
		golog.Warn("[Idc = "+m.Idc+"] [Method = bns_add] [Bns = %s] [Message = 'get ips fail']", m.Bns)
		return false
	}

	var idcMaps map[string][]string
	addCloudCacheOK := m.AddCloudCache(ac, ips, idcMaps)

	if addCloudCacheOK && idcMapEnable {
		idcMaps = m.GetIdcMapIps(ips)
		if idcMaps != nil {
			ac = "idcmap"
			ok = m.AddCloudCache(ac, ips, idcMaps)
			if !ok {
				SendHi(m.Idc, "add", "idcmap", m.Bns, "add-idc-map-fail")
				golog.Warn("[Idc = "+m.Idc+"] [Method = idcmap_add] [Bns = %s] [Message = 'add idcmap fail']", m.Bns)
				m.AppendErrorWithLockInBnsCache(fmt.Errorf("add idcmap fail, idcMaps = %+v, bns = %s", idcMaps, m.Bns))
			}
		}
	}

	return addCloudCacheOK
}

func (m *BnsCache) Update(idcMapEnable bool, serviceConfig map[string]interface{}) bool {
	if m.IsFakeBns {
		return m.UpdateFakeBns(idcMapEnable, serviceConfig)
	}

	return m.UpdateRealBns(idcMapEnable)
}

func (m *BnsCache) UpdateRealBns(idcMapEnable bool) bool {

	ac := "bns"
	ok := m.isAdded(ac)
	if !ok {
		golog.Warn(" [Idc = "+m.Idc+"] [Method = bns_update] [Bns = %s] [Message = update data faiiled,because the bns not exist']", m.Bns)
		return false
	}

	ips := m.GetBnsIps(false)
	if len(ips) < 1 {
		SendHi(m.Idc, "update", "bns", m.Bns, "bns-has-no-ips")
		errMsg := fmt.Sprintf("[Idc = %s] [Method = bns_update] [Bns = %s] [Message = 'get ips fail']", m.Idc, m.Bns)
		golog.Warn(errMsg)
		m.AppendErrorWithLockInBnsCache(errors.New(errMsg))
		return false
	}

	var idcMaps map[string][]string

	saveCloudCacheOK := m.SaveCloudCache(ac, ips, idcMaps)
	if saveCloudCacheOK && idcMapEnable {
		idcMaps = m.GetIdcMapIps(ips)
		if idcMaps != nil {
			ac = "idcmap"
			ok = m.isAdded(ac)
			if !ok {
				ok = m.AddCloudCache(ac, ips, idcMaps)
				if !ok {
					SendHi(m.Idc, "add", "idcmap", m.Bns, "add-idc-map-fail")
					errMsg := fmt.Sprintf("[Idc = %s] [Method = idcmap_add] [Bns = %s] [Message = 'add idcmap fail']", m.Idc, m.Bns)
					golog.Warn(errMsg)
					m.AppendErrorWithLockInBnsCache(errors.New(errMsg))
				}
			} else {
				ok = m.SaveCloudCache(ac, ips, idcMaps)
				if !ok {
					SendHi(m.Idc, "update", "idcmap", m.Bns, "update-idc-map-fail")
					errMsg := fmt.Sprintf("[Idc = %s] [Method = idcmap_update] [Bns = %s] [Message = 'update idcmap fail']", m.Idc, m.Bns)
					golog.Warn(errMsg)
					m.AppendErrorWithLockInBnsCache(errors.New(errMsg))
				}
			}
		}
	}

	return saveCloudCacheOK
}

func (m *BnsCache) SaveCloudCache(ac string, ips []string, idcMaps map[string][]string) bool {
	prefix := GetPrefix(m.Idc)
	// p-3ufc-beijing-bns-lock
	key := fmt.Sprintf("%s-%s-update-lock", prefix, ac)

	defer utils.UnLock(key)
	if !utils.Lock(key) {
		// bns/idcmap 加锁失败
		golog.Warn("[Idc = " + m.Idc + "] [add " + m.Bns + " fail] [lock " + key + " fail]")
		return false
	}

	var content []byte
	var err error

	if ac == "bns" {
		content, err = json.Marshal(ips)
		if err != nil {
			golog.Warn("[Idc = "+m.Idc+"] [Method = bns_update] [Bns = %s] [Message = save bns ips fail,because json marshal fail]", m.Bns)
			return false
		}
	} else {
		content, err = json.Marshal(idcMaps)
		if err != nil {
			golog.Warn("[Idc = "+m.Idc+"] [Method = idcmap_update] [Bns = %s] [Message = save idcmaps ips fail,because json marshal fail]", m.Bns)
			return false
		}
	}

	contentString := string(content)
	if m.IsFakeBns && ac == "bns" {
		pre := GetPrefix(m.Idc)
		bnsKey := fmt.Sprintf("%s-%s-%s", pre, ac, m.Bns)
		if err := m.addOrUpdateUfcMetaInfoForFakeBnsData(bnsKey, contentString); err != nil {
			sendHiMsg := fmt.Sprintf("[add or update ufc_meta_info failed] [key = %s] [value = %s]", bnsKey, contentString)
			golog.Error(sendHiMsg)
			SendHi(m.Idc, "update", ac, m.Bns, sendHiMsg)
			return false
		}
	}

	setConfigCacheOK := m.SetConfigCache(ac, string(content))
	if !setConfigCacheOK {
		return false
	}

	setConfigMtimeOK := m.SetConfigMtimeCache(ac)
	if !setConfigMtimeOK {
		return false
	}

	return true
}

func (m *BnsCache) AddCloudCache(ac string, ips []string, idcMaps map[string][]string) bool {
	pre := GetPrefix(m.Idc)

	key := fmt.Sprintf("%s-%s-add-lock", pre, ac)
	defer utils.UnLock(key)
	if !utils.Lock(key) {
		return false
	}

	acCntKey := fmt.Sprintf("%s-%ss-cnt", pre, ac)
	acCnt := utils.GetIntCache(acCntKey)
	if acCnt < 0 {
		return false
	}
	acCnt++

	acNameKey := fmt.Sprintf("%s-%ss-%d", pre, ac, acCnt-1)

	var content []byte
	var err error

	if ac == "bns" {
		content, err = json.Marshal(ips)
		if err != nil {
			return false
		}
	} else {
		content, err = json.Marshal(idcMaps)
		if err != nil {
			return false
		}
	}

	acConfigKey := fmt.Sprintf("%s-%s-%s", pre, ac, m.Bns)
	acConfig := string(content)

	version := GetTime()
	acMtimeKey := fmt.Sprintf("%s-%s-%s-mtime", pre, ac, m.Bns)

	//把bns 信息写到ufc meta info 表中
	err = AddUfcMetaInfo(acNameKey, m.Bns)
	if err != nil {
		//写mysql失败，发hi报警
		SendHi(m.Idc, "add", ac, m.Bns, "add ufc_meta_info failed")
		errMsg := fmt.Sprintf("[add ufc_meta_info failed] [key = %s] [value = %s]", acNameKey, acConfig)
		golog.Warn(errMsg)
		m.AppendErrorWithLockInBnsCache(errors.New(errMsg))
		return false
	}
	err = UpdateUfcMetaInfo(acCntKey, strconv.Itoa(int(acCnt)))
	if err != nil {
		//写mysql失败，发hi报警
		errMsg := fmt.Sprintf("[update ufc_meta_info failed] [key = %s] [value = %s]", acCntKey, strconv.Itoa(int(acCnt)))
		SendHi(m.Idc, "update", ac, m.Bns, errMsg)
		golog.Warn(errMsg)
		m.AppendErrorWithLockInBnsCache(errors.New(errMsg))
		return false
	}

	// 对于fakebns，需要把具体数据也写到 mysql
	if m.IsFakeBns && ac == "bns" {
		acConfigKey := fmt.Sprintf("%s-%s-%s", pre, ac, m.Bns)
		if err := m.addOrUpdateUfcMetaInfoForFakeBnsData(acConfigKey, acConfig); err != nil {
			sendHiMsg := fmt.Sprintf("[add or update ufc_meta_info failed] [key = %s] [value = %s]", acConfigKey, acConfig)
			golog.Error(sendHiMsg)
			SendHi(m.Idc, "update", ac, m.Bns, sendHiMsg)
			return false
		}
	}

	msetCacheOK := utils.MsetCache(acCntKey, acCnt, acNameKey, m.Bns, acConfigKey, acConfig, acMtimeKey, version)
	if !msetCacheOK {
		sendHiMsg := fmt.Sprintf("[mset bns/idcmap failed][%s:%d][%s:%s][%s:%s][%s:%d]",
			acCntKey, acCnt, acNameKey, m.Bns, acConfigKey, acConfig, acMtimeKey, version)
		SendHi(m.Idc, "add", "bns/idcmap", m.Bns, sendHiMsg)
		golog.Warn(sendHiMsg)
		m.AppendErrorWithLockInBnsCache(errors.New("add bns failed, add mset cache failed"))
		return false
	}

	return true
}

// 增加/变更 ufc 中的数据
func (m *BnsCache) addOrUpdateUfcMetaInfoForFakeBnsData(key string, value string) error {
	exist, err := IsKeyInUfcMetaInfo(key)
	if err != nil {
		msg := fmt.Sprintf("check key exist failed, key is %s, err is %s", key, err)
		golog.Error(msg)
		return &utils.UfcadminError{ErrCode: 500, ErrMsg: msg}
	}

	if exist {
		golog.Info("the bns exist, bns is %s, value is %s", key, value)
		if err := UpdateUfcMetaInfo(key, value); err != nil {
			msg := fmt.Sprintf("update ufc meta info failed, key is %s, value is %s, err is %v", key, value, err)
			golog.Error(msg)
			return &utils.UfcadminError{ErrCode: 500, ErrMsg: msg}
		}
	} else {
		golog.Info("the bns not exist, bns is %s, value is %s", key, value)
		if err := AddUfcMetaInfo(key, value); err != nil {
			msg := fmt.Sprintf("add ufc meta info failed, key is %s, value is %s, err is %v", key, value, err)
			golog.Error(msg)
			return &utils.UfcadminError{ErrCode: 500, ErrMsg: msg}
		}
	}

	return nil
}

func (m *BnsCache) GetBnsIps(noTreat bool) []string {
	var ips []string
	var err error
	var ipsList []string
	bns := m.Bns
	for i := 0; i < 3; i++ {
		// 如果想获取smart bns的非idcmap信息，需要处理一下，把前缀信息拿掉，否则拿到的是admin所在机房的ip
		// 获取idcmap信息的时候 notreat == true, 其它时候notreat == false
		if strings.Contains(m.Bns, "SmartBns") && !noTreat {
			splitStr := strings.Split(m.Bns, "@")
			if len(splitStr) != 2 {
				golog.Warn("smart bns %s format error", m.Bns)
				return ipsList
			}
			bns = splitStr[1]
		}

		b := utils.NewBnsClient(bns)
		ipsList, err = b.GetServers(false)
		if err != nil {
			golog.Warn("[Idc = "+m.Idc+"] [Method = set_bns_cache] [Bns = %s] [Tries = %d] [Message = 'get ips fail']", m.Bns, i)
			continue
		}

		// 打乱bns数组
		shuffleMap(ipsList)
		if len(ipsList) > 0 {
			return ipsList
		}
	}

	return ips
}

// 打乱bns数组
func shuffleMap(slice []string) {
	r := rand.New(rand.NewSource(time.Now().Unix()))
	for n := len(slice); n > 0; n-- {
		randIndex := r.Intn(n)
		slice[n-1], slice[randIndex] = slice[randIndex], slice[n-1]
	}
}

// 判断bns或者idcmap配置是否存在
func (m *BnsCache) IsExit(ac string) bool {
	if ac == "bns" {
		v := m.GetBnsCache()
		if v == nil {
			return false
		}
	} else {
		v := m.GetIdcMapCache()
		if v == nil {
			return false
		}
	}
	return true
}

func (m *BnsCache) isAdded(ac string) bool {
	if ac == "bns" {
		v := m.GetBnsCache()
		if v == nil {
			golog.Warn("bns cache is nil, bns is %s, idc is %s", m.Bns, m.Idc)
			return false
		}
	} else {
		v := m.GetIdcMapCache()
		if v == nil {
			golog.Warn("idcmap cache is nil, bns is %s, idc is %s", m.Bns, m.Idc)
			return false
		}
	}

	return true
}

func (m *BnsCache) GetBnsCache() []string {
	pre := GetPrefix(m.Idc)
	key := fmt.Sprintf("%s-bns-%s", pre, m.Bns)

	str := utils.GetCache(key)
	var js []string
	err := json.Unmarshal([]byte(str), &js)
	if err != nil {
		golog.Warn("get bns key failed, bns key is: %v", key)
		return nil
	}

	return js
}

func (m *BnsCache) GetIdcMapCache() map[string][]string {
	pre := GetPrefix(m.Idc)
	key := fmt.Sprintf("%s-idcmap-%s", pre, m.Bns)

	str := utils.GetCache(key)
	var idcmap map[string][]string
	err := json.Unmarshal([]byte(str), &idcmap)
	if err != nil {
		golog.Warn("get idcmap key failed, idcmap key is: %v", key)
		return nil
	}

	return idcmap
}

func (m *BnsCache) GetNameIndex(ac string) int64 {
	pre := GetPrefix(m.Idc)
	key := fmt.Sprintf("%s-%ss-cnt", pre, ac)

	v := utils.SetIncr(key)
	return v
}

func (m *BnsCache) SetConfigCache(ac string, content string) bool {
	pre := GetPrefix(m.Idc)
	// p-3ufc-beijing-bns-xxx
	key := fmt.Sprintf("%s-%s-%s", pre, ac, m.Bns)
	re := utils.SetCache(key, content)
	if !re {
		golog.Warn("set cache failed, [key = %s] [value = %s]", key, content)
		return false
	}

	return true
}

func (m *BnsCache) SetNameIndexCache(ac string) bool {
	index := m.GetNameIndex(ac)
	if index < 1 {
		return false
	}

	ok := m.SetNameCache(ac, index)
	if !ok {
		return false
	}

	return true
}

func (m *BnsCache) SetNameCache(ac string, index int64) bool {
	if index < 1 {
		return false
	}

	pre := GetPrefix(m.Idc)
	key := fmt.Sprintf("%s-%ss-%d", pre, ac, index-1)

	re := utils.SetCache(key, m.Bns)
	if !re {
		golog.Warn("[set name cache failed] [key = %s] [value = %s]", key, m.Bns)
		return false
	}

	return true
}

func (m *BnsCache) SetConfigMtimeCache(ac string) bool {
	version := GetTime()

	pre := GetPrefix(m.Idc)
	// p-3ufc-beijing-bns-xx-mtimex
	key := fmt.Sprintf("%s-%s-%s-mtime", pre, ac, m.Bns)
	re := utils.SetCache(key, version)
	if !re {
		golog.Warn("set config mtime fail, key=%s", key)
		return false
	}

	return true
}

// 需要分两种情况，普通bns走原有逻辑，smart bns走特殊逻辑
func (m *BnsCache) GetIdcMapIps(ips []string) map[string][]string {
	// 判断一下是否是smart bns
	if strings.Contains(m.Bns, "SmartBns") {
		return m.GetSmartBnsIdcMapIps()
	}
	return m.GetNormalBnsIdcMapIps(ips)
}

func (m *BnsCache) GetSmartBnsIdcMapIps() map[string][]string {
	var slices map[string][]string
	slices = make(map[string][]string)
	splitStrings := strings.Split(m.Bns, "@")
	if len(splitStrings) != 2 {
		golog.Warn("smart bns %s format error", m.Bns)
		return nil
	}

	physicalIdcs := BaseConf.Idc.PhysicalIdcs
	for _, idc := range physicalIdcs {
		// 根据物理机房拼接出真实的smart bns
		var smartBns string
		if strings.Contains(m.Bns, "from_product") {
			smartBns = fmt.Sprintf("%s,from_idc=%s@%s", splitStrings[0], idc, splitStrings[1])
		} else {
			smartBns = fmt.Sprintf("%sfrom_idc=%s@%s", splitStrings[0], idc, splitStrings[1])
		}
		smartBnsCache := &BnsCache{Idc: m.Idc, Bns: smartBns, IsFakeBns: false}
		ips := smartBnsCache.GetBnsIps(true)
		slices[idc] = ips
	}
	return slices
}

func (m *BnsCache) GetNormalBnsIdcMapIps(ips []string) map[string][]string {
	var slices map[string][]string
	slices = make(map[string][]string)

	for _, ip := range ips {
		hostname := utils.Ip2host(ip)
		if hostname == "" {
			continue
		}

		info := strings.Split(hostname, ":")
		if len(info) != 2 {
			continue
		}

		s := strings.Split(info[0], ".")

		idc := s[len(s)-1]
		slices[idc] = append(slices[idc], ip)
	}

	return slices
}
