package ufcadmin

import (
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/simplejson"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/models"
	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

// 获取新的小流量ip
func (p *UfcadminController) SmallFlowIpNew() error {
	var err error
	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	serviceName := p.FwController.Ctx.Input.Query("service_name")
	if serviceName == "" {
		err = &utils.ErrServiceParamMissing
		return err
	}

	ip := models.GetUpstreamIps(serviceName)

	data := simplejson.New()
	data.Set("ip", ip)

	body := p.CreateBody(data)
	p.Echo(200, body)
	return nil
}

// 获取之前的小流量ip
func (p *UfcadminController) SmallFlowIpGet() error {
	var err error
	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	serviceName := p.FwController.Ctx.Input.Query("service_name")
	if serviceName == "" {
		err = &utils.ErrServiceParamMissing
		return err
	}

	ips := models.GetSmallFlowIps(serviceName)

	data := simplejson.New()
	data.Set("ip", ips)

	body := p.CreateBody(data)
	p.Echo(200, body)
	return nil
}

// 删除小流量池
func (p *UfcadminController) SmallFlowDelete() error {
	var err error
	defer func() {
		if err != nil {
			p.EchoError(err)
		}
	}()

	serviceName := p.FwController.Ctx.Input.Query("service_name")
	if serviceName == "" {
		err = &utils.ErrServiceParamMissing
		return err
	}

	err = models.ForIdcDelete(serviceName)
	if err != nil {
		golog.Error("delete small flow ip fail, err:%+v", err)
		return err
	}
	p.Echo(200, "ok")
	return nil
}
