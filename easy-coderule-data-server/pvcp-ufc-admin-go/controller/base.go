package ufcadmin

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/framework"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/simplejson"

	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/models"
	"icode.baidu.com/baidu/netdisk/pvcp-ufc-admin-go/utils"
)

type response struct {
	ErrorCode int              `json:"error_code"`
	ErrorMsg  string           `json:"error_msg"`
	RequestId string           `json:"request_id"`
	Data      *simplejson.Json `json:"data"`
}

type UfcadminController struct {
	framework.FwController
	Logid     string
	Method    string
	RequestId string
}

func NotFound(context *framework.Context) {
	context.Output.SetStatus(404)
	context.Output.Body([]byte(""))
}

func (r *UfcadminController) EchoError(err error) {
	var e *utils.UfcadminError
	var ok bool
	e, ok = err.(*utils.UfcadminError)
	if !ok {
		e = &utils.ErrUnknown
	}

	re := response{ErrorCode: e.ErrCode, ErrorMsg: e.ErrMsg, RequestId: r.RequestId, Data: nil}
	js, err := json.Marshal(re)
	if err != nil {
		return
	}
	body := fmt.Sprintf("%s", string(js))

	// log
	golog.Error(fmt.Sprintf("[Logid = %s] [Method = %s] [Message = %s]", r.Logid, r.Method, body))

	r.Echo(200, body)
}

func (r *UfcadminController) Echo(status int, body string) {
	r.Ctx.Output.Header("Content-Type", "application/json")
	r.Ctx.Output.SetStatus(status)
	r.Ctx.Output.Body([]byte(body))
}

func (r *UfcadminController) CreateBody(data *simplejson.Json) string {
	re := response{ErrorCode: 0, ErrorMsg: "success", RequestId: r.RequestId, Data: data}
	js, ok := json.Marshal(re)
	if ok != nil {
		return ""
	}

	body := fmt.Sprintf("%s", string(js))
	return body
}

func (r *UfcadminController) CreateRequest(p *framework.FwController) *utils.UfcAdminRequest {
	bodyByte := p.Ctx.Input.RawBody()

	method := p.Ctx.Input.Query("method")

	req := &utils.UfcAdminRequest{
		Method:     method,
		BodyByte:   bodyByte,
		BodyString: string(bodyByte),
	}

	r.Logid = utils.GetLogid()
	r.Method = method
	r.RequestId = r.GetRequestId()

	golog.Info(fmt.Sprintf("[Logid = %s] [Method = %s] [Param = %s]", r.Logid, r.Method, string(bodyByte)))

	return req
}

func (r *UfcadminController) CheckParamValid(req *utils.UfcAdminRequest) error {
	args, err := simplejson.NewJson(req.BodyByte)
	if err != nil {
		return &utils.ErrBodyJSONFormatError
	}

	// 检查必备参数
	for k := range models.ReqBodyMustParamMap {
		if _, ok := args.CheckGet(k); !ok {
			return &utils.UfcadminError{ErrCode: 400, ErrMsg: "param miss : " + k}
		}
	}

	// 检查json参数格式
	for k := range models.ReqBodyJSONParamMap {
		js, ok := args.CheckGet(k)
		if !ok {
			continue
		}

		if js == nil {
			continue
		}

		if js.MustString() == "" {
			continue
		}

		_, err := simplejson.NewJson([]byte(js.MustString()))
		if err != nil {
			return &utils.UfcadminError{ErrCode: 400, ErrMsg: k + " new json error"}
		}
	}

	// 检查整型参数
	for k, v := range args.MustMap() {
		if _, ok := models.ReqBodyIntParamMap[k]; !ok {
			continue
		}

		if _, ok := v.(json.Number); !ok {
			return &utils.UfcadminError{ErrCode: 400, ErrMsg: k + " is not int"}
		}
	}

	// 检查 advanced 参数格式，除了上述检查为json逻辑，需要对json内部的header/query_string, app_name 和bns权重等做校验
	if err := r.checkAdvancedRouteConfigValid(args); err != nil {
		golog.Error("check advanced route config fail, err:%s", err.Error())
		return &utils.UfcadminError{ErrCode: 400, ErrMsg: "check advanced route config failed, msg is " + err.Error()}
	}
	return nil
}

// nolint:gocyclo
func (r *UfcadminController) checkAdvancedRouteConfigValid(args *simplejson.Json) error {
	routeRule, routeRuleExist := args.CheckGet("advanced_route_rule")
	routeApp, routeAppExist := args.CheckGet("advanced_route_app")
	if routeRuleExist != routeAppExist { // 这两者要同时存在
		if routeRuleExist {
			// advanced_route_rule存在，advanced_route_app不存在
			return errors.New("param advanced_route_rule is exist, but advanced_route_app is not exist")
		}
		// advanced_route_rule不存在，advanced_route_app存在
		return errors.New("param advanced_route_rule is not exist, but advanced_route_app is exist")
	}

	if !routeRuleExist {
		// 同时都不存在
		return nil
	}
	// 收集routeRule中的app name和 routeApp中的app name
	routeRuleAppNameMap := make(map[string]struct{})

	// route rule字段校验
	routeRuleString, err := routeRule.String()
	if err != nil {
		golog.Error("[err: %s]", err.Error())
		return err
	}
	routeAppString, err := routeApp.String()
	if err != nil {
		golog.Error("[err: %s]", err.Error())
		return err
	}

	if (routeRuleString == "" || routeRuleString == "{}") && (routeAppString == "" || routeAppString == "{}") {
		return nil
	}

	routeRuleMapJSON, err := simplejson.NewJson([]byte(routeRuleString))
	if err != nil {
		golog.Error("[msg: route rule new json failed] [err: %s]", err.Error())
		return err
	}
	routeRuleMap, err := routeRuleMapJSON.Map()
	if err != nil {
		golog.Error("[msg: route rule json map failed] [err: %s]", err.Error())
		return err
	}
	for _, appMapInterface := range routeRuleMap {
		appMap, ok := appMapInterface.(map[string]interface{})
		if !ok {
			return errors.New("the route rule`s app map type is not map[string]interface{}")
		}

		for appName, contentMapInterface := range appMap {
			contentMap, ok := contentMapInterface.(map[string]interface{})
			if !ok {
				return errors.New("the content map type is not match")
			}

			// header 字段存在时, 需要进行校验
			header, ok := contentMap["header"]
			if ok {
				_, ok := header.(map[string]interface{})
				if !ok {
					return fmt.Errorf("the app header is not map[string]interface{}, app name is %s", appName)
				}
			}

			// query_string字段时，需要满足该格式
			queryString, ok := contentMap["query_string"]
			if ok {
				_, ok := queryString.(map[string]interface{})
				if !ok {
					return fmt.Errorf("the app query string is not map[string]string, app name is %s", appName)
				}
			}

			// 收集app name 和 routeAppMap 中的 appName 做校验
			routeRuleAppNameMap[appName] = struct{}{}
		}
	}

	routeAppMapJSON, err := simplejson.NewJson([]byte(routeAppString))
	if err != nil {
		golog.Error("[msg: route app new json failed] [err: %s]", err.Error())
		return err
	}

	routeAppMap, err := routeAppMapJSON.Map()
	if err != nil {
		golog.Error("[msg: route app json map failed] [err: %s]", err.Error())
		return err
	}

	for appName, contentMapInterface := range routeAppMap {
		contentMap, ok := contentMapInterface.(map[string]interface{}) // app name-> bns name -> f
		if !ok {
			return errors.New("the route_app map type is not map[string]map[string]int")
		}
		// app name校验：advanced_route_rule的app和advanced_route_app的app name需要完全匹配
		_, ok = routeRuleAppNameMap[appName]
		if !ok {
			return errors.New("the app name is not match")
		}
		delete(routeRuleAppNameMap, appName)

		// 权重校验
		var total int64 = 0
		for _, weightInteface := range contentMap {
			weightNumber, ok := weightInteface.(json.Number)
			if !ok {
				return errors.New("the weight type is not match")
			}

			weight, err := weightNumber.Int64()
			if err != nil {
				return errors.New("the type is not float64")
			}
			if weight < 0 || weight > 100 {
				return fmt.Errorf("the advanced_route_app bns`s weight is less than 0 or greate than 100, app is %s", appName)
			}
			total += weight
		}
		if total != 100 {
			return fmt.Errorf("the sum of the advaced_route_app bns`s weight is not equal 100, app is %s", appName)
		}
	}

	// app name 需要一一对应
	if len(routeRuleAppNameMap) != 0 {
		return errors.New("the app name is not match")
	}
	return nil
}

func (r *UfcadminController) GetRequestId() string {
	port := models.BaseConf.Server.HttpPort
	timeNow := time.Now().Format(time.RFC3339)
	timeShort := strings.TrimSuffix(timeNow, "+08:00")

	hostname := r.GetHostnameAndIp()
	str := hostname + ",," + port + ",," + r.Logid + ",," + timeShort
	return str
}

func (r *UfcadminController) GetHostnameAndIp() string {
	var err error
	HostnameRaw, err := os.Hostname()
	if err != nil {
		return "unkown"
	}

	return strings.TrimSuffix(HostnameRaw, ".baidu.com")
}
