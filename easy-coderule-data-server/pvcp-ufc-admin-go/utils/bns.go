/*
 * author  : licen
 * file    : src/ufc/bns.go
 * ctime   : Tue 19 Apr 2016 04:42:15 PM CST
 */

package utils

import (
	"errors"
	"fmt"
	"math/rand"
	"net"
	"strconv"
	"strings"

	bns "icode.baidu.com/baidu/netdisk/pcs-go-lib/bns"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/protobuf/proto"
)

type BnsClient struct {
	Name   string
	Filter string
}

/*
 * API for this Bns structure
 */
func NewBnsClient(bnsName string) *BnsClient {
	strs := strings.Split(bnsName, "|")
	cnt := len(strs)

	name := strs[0]
	filter := "main"

	if cnt >= 2 {
		//这里决定了可以使用bns的其他端口别名作为配置
		filter = strs[1]
	}

	n := &BnsClient{name, filter}

	return n
}

func (n *BnsClient) GetServers(healthyOnly bool) ([]string, error) {
	dst := []string{}
	ret, err := n.list(healthyOnly)
	if err != nil {
		return dst, err
	}

	if ret == nil {
		return dst, nil
	}

	return ret, nil
}

func (n *BnsClient) GetServer() (string, error) {
	instances, err := n.list(true)
	if err != nil {
		return "", err
	}

	if instances == nil {
		return "", nil
	}

	index := rand.Intn(**********) % len(instances)
	return instances[index], nil
}

func _format(instance *bns.InstanceInfo, filter string, live bool) (string, error) {
	ipInt := instance.GetHostIp()
	if ipInt == 0 {
		return "", errors.New("get ip for hostname failed")
	}

	ip := fmt.Sprintf("%d.%d.%d.%d", byte(ipInt>>24), byte(ipInt>>16), byte(ipInt>>8), byte(ipInt))

	instanceStatus := instance.InstanceStatus
	if instanceStatus == nil {
		return "", errors.New("instance_status field not found")
	}

	/* exclude disabled instances */
	disabled := instanceStatus.InterventionalStatus
	if *disabled > 0 {
		return "", nil
	}

	port := instanceStatus.Port
	if *port < 0 {
		return "", errors.New("instance_status.port field not found")
	}

	status := instanceStatus.Status

	multiports := instanceStatus.MultiPort
	if *multiports == "" {
		return "", errors.New("instance_status.multi_port field not found")
	}

	if live && *status != 0 {
		// 只有状态0才行,999怎么办
		return "", nil
	}

	if filter == "main" {
		return string(ip) + ":" + strconv.Itoa(int(*port)), nil
	}

	portsInfo := strings.Split(*multiports, ",")
	for _, ps := range portsInfo {
		ports := strings.Split(ps, "=")
		if ports[0] == filter {
			return string(ip) + ":" + ports[1], nil
		}
	}

	return "", nil
}

func (n *BnsClient) list(live bool) ([]string, error) {
	var info *bns.LocalNamingResponse
	var err error
	for i := 0; i < 3; i++ {
		info, err = n.do()
		if err != nil {
			continue
		}

		resCode := info.GetRetcode()
		if resCode == 0 || resCode == -11 || resCode == -16 || resCode == -1 {
			break
		}

		message := fmt.Sprintf("[bns=%s] [res_code=%d] [message=bns get failed]", n.Name, resCode)
		err = errors.New(message)
	}

	if err != nil {
		return nil, err
	}

	servers := n.getFormatServers(info, live)
	if servers != nil && live {
		serversAll := n.getFormatServers(info, false)
		if float32(len(servers)) < 0.75*float32(len(serversAll)) {
			servers = serversAll
		}
	}

	return servers, nil
}

func (n *BnsClient) getFormatServers(info *bns.LocalNamingResponse, live bool) []string {
	//bitmap := make(map[string]bool)
	servers := []string{}
	instance := info.GetInstanceInfo()
	if instance == nil {
		return nil
	}

	for _, item := range instance {
		server, err := _format(item, n.Filter, live)
		if err != nil || server == "" {
			continue
		}

		servers = append(servers, server)
	}
	return servers
}

func (n *BnsClient) do() (*bns.LocalNamingResponse, error) {
	c := bns.New()

	var reply bns.LocalNamingResponse
	err := c.Call(&bns.LocalNamingRequest{
		ServiceName: proto.String(n.Name),
	}, &reply)

	if err != nil {
		return nil, errors.New("local naming request failed")
	}

	return &reply, nil
}

func Ip2host(ip string) string {
	slices := strings.Split(ip, ":")

	if len(slices) != 2 {
		return ""
	}

	addrs, err := net.LookupAddr(slices[0])
	if err != nil {
		return ""
	}

	if addrs == nil {
		return ""
	}

	hosts := strings.Split(addrs[0], ".baidu.com")
	return hosts[0] + ":" + slices[1]
}

func Host2ip(hostname string) (string, error) {
	ns, err := net.LookupHost(hostname)
	if err != nil {
		return "", err
	}

	for _, n := range ns {
		return n, nil
	}

	return "", errors.New("ns not found")
}
