package utils

import (
	"math/rand"

	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
)

func Loginit(filePath string, logLevel int32) {
	golog.SetLevel(logLevel)
	golog.SetFile(filePath)
	golog.ReOpen("")
}

func GetLogid() string {
	str := "123456789123456789"
	strLen := len(str)
	randLen := 18

	res := string(str[rand.Intn(strLen)])
	for i := 1; i < randLen; i = len(res) {
		tmp := string(str[rand.Intn(strLen)])
		if tmp != string(res[i-1]) {
			res = res + tmp
		}
	}

	return res
}
