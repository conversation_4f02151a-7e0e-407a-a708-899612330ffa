package utils

import (
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/redigo/redis"
)

func Lock(key string) bool {
	c := GetRedisConn()
	if c == nil {
		return false
	}

	expire := 300
	ret, err := redis.String(c.Do("SET", key, 1, "NX", "EX", expire))
	if err != nil {
		return false
	}

	if ret != "OK" {
		return false
	}

	return true
}

func UnLock(key string) bool {
	c := GetRedisConn()
	if c == nil {
		return false
	}

	_, err := redis.String(c.Do("DEL", key))
	if err != nil {
		return false
	}
	return true
}
