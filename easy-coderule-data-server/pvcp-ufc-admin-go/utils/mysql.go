package utils

import (
	//"math/rand"
	"database/sql"
	_ "database/sql/driver"

	//"fmt"
	"icode.baidu.com/baidu/netdisk/pcs-go-lib/golog"
	_ "icode.baidu.com/baidu/netdisk/pcs-go-lib/mysql"

	//"time"
	"fmt"
)

var (
	UfcMetaInfoTableName string
)

var (

	//global UfcadminDBHandler handler: initialization at server start
	UfcadminDB UfcadminDBHandler

	//db max open connections
	DATABASE_MAX_OPEN_CONNS int

	//db connections pool size
	DATABASE_MAX_IDLE_CONNS int

	//db conn pool wait timeout(ms)
	DATABASE_CONNS_POOL_TIMEOUT int

	//db read timeout(ms)
	DATABASE_READ_TIMEOUT int

	//db write timeout(ms)
	DATABASE_WRITE_TIMEOUT int
)

type UfcadminDBHandler struct {
	<PERSON><PERSON> *sql.DB
}

func RegisterUfcadminDB(user, pwd, dbname, server string) error {
	var err error
	//db max open connections
	DATABASE_MAX_OPEN_CONNS = 25
	//db connections pool size
	DATABASE_MAX_IDLE_CONNS = 10
	//db connections pool wait timeout(ms)
	DATABASE_CONNS_POOL_TIMEOUT = 500
	//db tcp read timeout(ms)
	DATABASE_READ_TIMEOUT = 5000
	//db tcp write timeout(ms)
	DATABASE_WRITE_TIMEOUT = 5000

	//init DB
	// DB is a database handle representing a pool of zero or more underlying connections
	// It's safe for concurrent use by multiple goroutines.
	UfcadminDB.Handler, err = UfcadminDB.getHandler(user, pwd, server, dbname)
	if err != nil {
		return err
	}
	return nil
}

func (db *UfcadminDBHandler) getHandler(user, pwd, host, dbname string) (*sql.DB, error) {
	str := fmt.Sprintf("%s:%s@tcp(%s)/%s?readTimeout=%dms&writeTimeout=%dms",
		user, pwd, host, dbname, DATABASE_READ_TIMEOUT, DATABASE_WRITE_TIMEOUT)
	golog.Debug("[GET NEW UFCADMIN SQL HANDLER:%s]", str)
	handler, err := sql.Open("mysql", str)
	if err != nil {
		golog.Warn("Open " + str + " failed, err: " + err.Error())
		return nil, err
	}
	//set max open/idle conns, avoid db connections increasing consistently
	golog.Info("[Set max open/idle conns] [MaxOpenConns:%d] [MaxIdleConns:%d] [PoolTimeOut:%d]",
		DATABASE_MAX_OPEN_CONNS, DATABASE_MAX_IDLE_CONNS, DATABASE_CONNS_POOL_TIMEOUT)
	handler.SetMaxOpenConns(DATABASE_MAX_OPEN_CONNS)
	handler.SetMaxIdleConns(DATABASE_MAX_IDLE_CONNS)

	return handler, err
}

func (db *UfcadminDBHandler) GetInstance() (*sql.DB, error) {
	if db.Handler != nil {
		return db.Handler, nil
	} else {
		return nil, &ErrDBConnectFailed
	}
}
