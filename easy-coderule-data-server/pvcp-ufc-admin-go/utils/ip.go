package utils

import (
	"strconv"
)

// 根据index 生成字符串ip
func IpGenerate(fromServiceIndex, toServiceIndex uint32) string {
	//基础ip 127.0.0.1
	var baseNum uint32 = 0x7f000000
	//fromservice 左移11位获取高11位
	upper11Bits := fromServiceIndex << 11
	ipNum := baseNum | upper11Bits | toServiceIndex

	//数字ip转换成字符串ip
	return numToString(ipNum)
}

// 数字IP转成字符串ip
func numToString(ip uint32) string {
	res := ""
	for i := 3; i >= 0; i-- {
		numTemp := ip & (255 << (i * 8))
		numTemp = numTemp >> (i * 8)
		//这里numTemp最大才255，这样转换是没有问题的
		res = res + strconv.Itoa(int(numTemp))
		if i != 0 {
			res = res + "."
		}
	}
	return res
}
