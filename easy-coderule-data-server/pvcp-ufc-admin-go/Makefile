# init project path
HOMEDIR := $(shell pwd)
OUTDIR := $(HOMEDIR)/output
 
# init command params
GO := go
GOMOD := $(GO) mod
GOBUILD := $(GO) build
GOTEST  := $(GO) test -gcflags="-N -l"
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")
 
# test cover files
COVPROF := $(HOMEDIR)/covprof.out  # coverage profile
COVFUNC := $(HOMEDIR)/covfunc.txt  # coverage profile information for each function
COVHTML := $(HOMEDIR)/covhtml.html # HTML representation of coverage profile
 
# make, make all
all: prepare compile package
sandbox: prepare compile-sandbox package-sandbox
 
# set proxy env
set-env:
	$(GO) env -w GO111MODULE=on
	$(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com
	$(GO) env -w GONOSUMDB=\*
 
#make prepare, download dependencies
prepare: bcloud gomod
 
bcloud:
	bcloud local -U
gomod: set-env
	$(GOMOD) download
 
#make compile
compile: build
compile-sandbox: build-sandbox
 
build:
	$(GOBUILD) -o $(HOMEDIR)/ufcadmin
build-sandbox: 
	$(GOBUILD) -o $(HOMEDIR)/ufc-admin-sandbox
 
# make test, test your code
test: prepare test-case
test-case:
	$(GOTEST) -v -cover -coverprofile=cover.out $(GOPKGS)
 
# make package
package: package-bin
package-sandbox: package-bin-sandbox

package-bin:
	mkdir -p $(OUTDIR)
	mkdir -p $(OUTDIR)/bin
	mkdir -p $(OUTDIR)/conf
	mkdir -p $(OUTDIR)/logs
	mkdir -p $(OUTDIR)/data
	cp -rf ufcadmin $(OUTDIR)/bin/
	cp -rf conf/base.toml $(OUTDIR)/conf/base.toml
	cp -rf scripts/control scripts/start.sh scripts/stop.sh scripts/restart.sh $(OUTDIR)/

package-bin-sandbox:
	mkdir -p $(OUTDIR)
	mkdir -p $(OUTDIR)/bin
	mkdir -p $(OUTDIR)/conf
	mkdir -p $(OUTDIR)/logs
	mkdir -p $(OUTDIR)/data
	cp -rf ufc-admin-sandbox $(OUTDIR)/bin/
	cp -rf conf/base_sandbox.toml $(OUTDIR)/conf/base.toml
	cp -rf scripts/control_sandbox scripts/start_sandbox.sh scripts/stop_sandbox.sh scripts/restart.sh $(OUTDIR)/
	mv $(OUTDIR)/control_sandbox $(OUTDIR)/control &&
	mv $(OUTDIR)/start_sandbox.sh $(OUTDIR)/start.sh &&
	mv $(OUTDIR)/stop_sandbox.sh $(OUTDIR)/stop.sh

# make clean
clean:
	$(GO) clean
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/pvcp-ufc-admin-go
	rm -rf $(GOPATH)/pkg/darwin_amd64
 
# avoid filename conflict and speed up build 
.PHONY: all prepare compile test package clean build
