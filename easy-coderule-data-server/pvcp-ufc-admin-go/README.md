ufc数据库相关：
mysql -h 10.58.162.53 -uufc -pufc -P3306

cq02-proxy-platform00.cq02（10.58.162.53），准备用这台机器
jumbo安装mysql；  ./home/<USER>/.jumbo/my.cnf   /home/<USER>/.jumbo/etc/mysql/my.cnf  这两个用哪个？
mysqladmin -u root -p password "123456"   # 为root创建密码
mysql -uroot -p123456  # root登录
select current_user();  select user();
show processlist;  show full processlist;
show status;

CREATE DATABASE ufc;
CREATE USER 'ufc'@'%' IDENTIFIED BY 'ufc';  # 创建用户
GRANT ALL privileges ON *.* TO 'ufc'@'%' identified by "ufc" with grant option;
GRANT ALL privileges ON *.* TO 'ufc'@'localhost' identified by "ufc" with grant option;  # %
flush privileges;
SELECT DISTINCT CONCAT('User: ''',user,'''@''',host,''';') AS aa FROM mysql.user;   # 查看所有user和权限
show grants for 'ufc'@'%';

# 不能为空的字段一定要带
# 可以为空的字段，没写入就是NULL，这儿要注意  唯一索引
# small这种二级数组，在mysql的colume中要保持前缀。有个地方要记录colume名字和真正配置key的映射
CREATE TABLE `service` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `service_name` varchar(40) NOT NULL COMMENT '服务名',
  `product` varchar(40) NOT NULL COMMENT '产品线',
  `bns_list` varchar(1024) NOT NULL COMMENT 'bns列表',
  `connect_timeout` int(10) NOT NULL COMMENT '自增id',
  `read_timeout` int(10) NOT NULL COMMENT '连接超时',
  `person` varchar(512) NOT NULL COMMENT '操作用户',
  `bp` int(10) NOT NULL DEFAULT '1' COMMENT '负载策略',
  `send_timeout` int(10) NOT NULL DEFAULT '0' COMMENT '发送超时',
  `forbid_timeout` int(10) NOT NULL DEFAULT '0' COMMENT '封禁超时',
  `max_forbid_percent` int(10) NOT NULL DEFAULT '0' COMMENT '自动封禁比例',
  `request_reset_interval` int(10) NOT NULL DEFAULT '0' COMMENT '请求重置时间',
  `max_fail` int(10) NOT NULL DEFAULT '1' COMMENT '自动封禁阈值',
  `limit_qps_per_backend` bigint(20) NOT NULL DEFAULT '0' COMMENT '单机限速',
  `limit_qps_all` bigint(20) NOT NULL DEFAULT '0' COMMENT '整体限速',
  `limit_qps_running` bigint(20) NOT NULL DEFAULT '0' COMMENT '执行限速',
  `qps_drop_rate` int(10) NOT NULL DEFAULT '0' COMMENT '丢弃请求',
  `max_global_running_cnt` int(10) NOT NULL DEFAULT '0' COMMENT '后端任务阀值',
  `hash_pool_size` int(10) NOT NULL DEFAULT '0' COMMENT '哈希池大小',
  `backup` varchar(1024) NOT NULL DEFAULT '' COMMENT '备用',
  `illegal_headers` varchar(1024) NOT NULL DEFAULT '' COMMENT '请求头',
  `black_list` text NOT NULL COMMENT '黑名单',
  `small_flow_bns_list` varchar(1024) NOT NULL DEFAULT '' COMMENT '小流量bns',
  `small_flow_hash_range` varchar(128) NOT NULL DEFAULT '' COMMENT '小流量比例',
  `small_flow_small_list` text NOT NULL COMMENT '自定义切流值',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` bigint(20) NOT NULL DEFAULT '0' COMMENT '版本号',
  `backends_max_tries` int(2) NOT NULL DEFAULT '1' COMMENT '重试次数',
  `idc_map` varchar(1024) NOT NULL DEFAULT '' COMMENT '智能idc map',
  `small_flow_idc_map` varchar(1024) NOT NULL DEFAULT '' COMMENT '智能idc map',
  `description` varchar(1024) NOT NULL DEFAULT '' COMMENT '备注描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_name` (`service_name`)
) ENGINE=InnoDB AUTO_INCREMENT=510 DEFAULT CHARSET=utf8 COMMENT='service相关数据'


#为type创建唯一索引
ALTER TABLE article ADD UNIQUE(type);
DESCRIBE mytable;
desc mysql.user;
SHOW INDEX FROM users;

INSERT INTO service (service_name,product,bns_list,connect_timeout,read_timeout,protocol,send_timeout,request_reset_interval,
qps_drop_rate,black_list,max_global_running_cnt,max_forbid_percent,max_fail,forbid_timeout,limit_qps_all,bp,
hash_pool_size,worker_timeout,illegal_headers,limit_qps_running,backup,options,limit_qps_per_backend,
small_flow_protocol,small_flow_bns_list,small_flow_hash_range,small_flow_small_list )
VALUES ( "ufc-test", "pcs",'{"pssui-all.cloud-storage.all":100}',1000,30000,"http",3000,25,
0,'{"***********":1}',3,25,1,120,2147483648,1,16,60,'[{"host":"illegal","xx":"xx"},{"y":"y"}]',2147483648,'{"pss-test":{"enable":1,"options":["read","write"]}}','["read","write"]',2147483648,'redis','{"pssui.cloud-storage.st":100}','{"max":10000,"min":0,"size":10000}','{"123123123":1,"62254624":1}');

UPDATE service SET product="222" where id=1;
DELETE FROM service WHERE id=1;  # 1和"1"都行



需要几个对外接口： 这些接口需要全部和原来的ufc-admin同步
url样式:
xxx:8041/rest/2.0/pvcp/ufc?method=queryservice
xxx:8041/rest/2.0/pvcp/ufc?method=queryall
1、查询配置接口（all和单独的）
2、更新配置接口
3、新建配置接口












