# SQL Diff 检测功能增强

## 概述

本次优化主要解决了原有SQL diff检测系统只针对已落库SQL做diff的问题，新增了对新增SQL的diff检测功能。

## 问题背景

原有系统存在以下问题：
- 只对已存在且发生变化的SQL进行diff检测
- 新增的SQL直接入库，没有进行变更检测
- 缺少对新增SQL的审查机制

## 解决方案

### 1. 采集侧改造 (go-static-analysis/passes/sqlscan)

#### 新增Git diff功能模块
- **文件**: `git_differ.go`
- **功能**: 
  - 获取两个commit之间的变更文件列表
  - 检查文件是否在变更列表中
  - 验证Git仓库有效性
  - 获取commit信息

#### 扩展数据结构
- **SQLScanner结构扩展**:
  ```go
  type SQLScanner struct {
      // 原有字段...
      GitDiffer    *GitDiffer
      ChangedFiles map[string]FileChangeInfo
      LastCommitID string
  }
  ```

- **ResultInfo结构扩展**:
  ```go
  type ResultInfo struct {
      // 原有字段...
      ChangeType      string // "NEW", "MODIFIED", "UNCHANGED"
      PreviousSQL     string // 修改前的SQL
      IsInChangedFile bool   // 是否在变更文件中
  }
  ```

#### 扫描逻辑优化
- 在SQL扫描完成后，自动标记每个SQL的变更类型
- 根据Git diff结果判断文件是否有变更
- 为每个ResultInfo设置相应的变更信息

### 2. 服务端改造 (easy-coderule-data-server/service/sqldata)

#### DTO结构扩展
- **UploadBodyResult扩展**:
  ```go
  type UploadBodyResult struct {
      // 原有字段...
      ChangeType      string `json:"change_type"`
      PreviousSQL     string `json:"previous_sql"`
      IsInChangedFile bool   `json:"is_in_changed_file"`
  }
  ```

#### 实体结构扩展
- **SQLDiffInfo扩展**:
  ```go
  type SQLDiffInfo struct {
      UnicKey    string
      Old        *SQLScanResults
      New        *SQLScanResults
      IsNewSQL   bool   // 标记是否为新增SQL
      ChangeType string // 变更类型
  }
  ```

#### 处理逻辑优化
- **UploadExec方法**:
  - 对新增SQL也进行diff检测
  - 区分新增SQL和修改SQL的处理逻辑
  
- **handleDiffSQL方法**:
  - 支持处理新增SQL的diff情况
  - 为新增SQL生成特殊的diff HTML
  - 在Extra字段中标记SQL类型

## 核心功能

### 1. Git变更检测
```go
// 获取变更文件列表
changes, err := gitDiffer.GetChangedFiles(lastCommitID, currentCommitID)

// 检查文件是否变更
isChanged, changeType := gitDiffer.IsFileChanged(filePath, changes)
```

### 2. SQL变更标记
```go
// 设置变更信息
result.SetChangeInfo("NEW", "", true)        // 新增SQL
result.SetChangeInfo("MODIFIED", oldSQL, true) // 修改SQL
result.SetChangeInfo("UNCHANGED", "", false)   // 未变更SQL
```

### 3. 新增SQL diff处理
```go
if item.ChangeType == "NEW" || item.IsInChangedFile {
    diffList = append(diffList, &entity.SQLDiffInfo{
        UnicKey:    realKeyHash,
        Old:        nil, // 新增SQL没有旧版本
        New:        newEntityResult,
        IsNewSQL:   true,
        ChangeType: "NEW",
    })
}
```

## 使用方式

### 1. 运行SQL扫描
```bash
cd go-static-analysis/cmd/sqlscan
go run main.go
```

### 2. 查看diff结果
- 访问服务端的diff查看页面
- 新增SQL会显示为全部新增内容
- 修改SQL会显示具体的变更差异

## 配置说明

### Git diff配置
- 系统会自动获取上次扫描的commit ID
- 如果是首次扫描，会与第一个commit进行对比
- 支持检测未提交的变更

### 错误处理
- Git操作失败时不会中断扫描流程
- 会记录详细的错误日志
- 提供降级处理机制

## 测试

### 单元测试
```bash
cd go-static-analysis/passes/sqlscan
go test -v
```

### 功能测试
1. 创建新的SQL文件
2. 修改现有SQL语句
3. 运行扫描工具
4. 检查diff结果是否正确标记

## 注意事项

1. **性能影响**: Git操作可能影响扫描速度，建议在CI/CD环境中使用
2. **Git依赖**: 需要确保运行环境有Git命令且仓库状态正常
3. **首次扫描**: 首次扫描时可能会产生大量diff记录
4. **权限要求**: 需要有读取Git仓库的权限

## 后续优化建议

1. **缓存机制**: 添加Git操作结果缓存，提升性能
2. **配置化**: 提供更多配置选项，如是否启用新增SQL检测
3. **通知优化**: 区分新增SQL和修改SQL的通知消息
4. **UI优化**: 在前端页面中更好地展示新增SQL的diff
5. **历史记录**: 支持查看SQL的完整变更历史

## 版本信息

- **版本**: v1.1.0
- **更新时间**: 2024-12-19
- **兼容性**: 向后兼容，不影响现有功能
