# SQL Diff 检测功能增强

## 概述

本次优化主要解决了原有SQL diff检测系统只针对已落库SQL做diff的问题，新增了对新增SQL的diff检测功能。

## 问题背景

原有系统存在以下问题：
- 只对已存在且发生变化的SQL进行diff检测
- 新增的SQL直接入库，没有进行变更检测
- 缺少对新增SQL的审查机制

## 解决方案

### 核心思路：基于Git diff内容的SQL变更检测

不再依赖数据库中的历史记录比较，而是直接分析Git diff的内容来识别SQL的具体变更。

### 1. 采集侧改造 (go-static-analysis/passes/sqlscan)

#### 新增Git diff功能模块
- **文件**: `git_differ.go`
- **核心功能**:
  - 获取两个commit之间的变更文件列表
  - 获取每个文件的详细Git diff内容
  - 解析diff内容，识别SQL相关的变更行
  - 通过SQL关键词匹配判断SQL是否有变更
  - 验证Git仓库有效性

#### 扩展数据结构
- **SQLScanner结构扩展**:
  ```go
  type SQLScanner struct {
      // 原有字段...
      GitDiffer    *GitDiffer
      ChangedFiles map[string]FileChangeInfo
      LastCommitID string
  }
  ```

- **ResultInfo结构扩展**:
  ```go
  type ResultInfo struct {
      // 原有字段...
      ChangeType      string // "NEW", "MODIFIED", "UNCHANGED"
      PreviousSQL     string // 修改前的SQL
      IsInChangedFile bool   // 是否在变更文件中
  }
  ```

#### 扫描逻辑优化
- 在SQL扫描完成后，自动标记每个SQL的变更类型
- 根据Git diff结果判断文件是否有变更
- 为每个ResultInfo设置相应的变更信息

### 2. 服务端改造 (easy-coderule-data-server/service/sqldata)

#### DTO结构扩展
- **UploadBodyResult扩展**:
  ```go
  type UploadBodyResult struct {
      // 原有字段...
      ChangeType      string `json:"change_type"`
      PreviousSQL     string `json:"previous_sql"`
      IsInChangedFile bool   `json:"is_in_changed_file"`
  }
  ```

#### 实体结构扩展
- **SQLDiffInfo扩展**:
  ```go
  type SQLDiffInfo struct {
      UnicKey    string
      Old        *SQLScanResults
      New        *SQLScanResults
      IsNewSQL   bool   // 标记是否为新增SQL
      ChangeType string // 变更类型
  }
  ```

#### 处理逻辑优化
- **UploadExec方法**:
  - 对新增SQL也进行diff检测
  - 区分新增SQL和修改SQL的处理逻辑
  
- **handleDiffSQL方法**:
  - 支持处理新增SQL的diff情况
  - 为新增SQL生成特殊的diff HTML
  - 在Extra字段中标记SQL类型

## 核心功能

### 1. 基于Git diff的SQL变更检测
```go
// 获取SQL级别的变更信息（不依赖历史数据库记录）
sqlChanges, err := gitDiffer.GetSQLChanges(lastCommitID, currentCommitID, currentResults)

// SQL变更信息结构
type SQLChangeInfo struct {
    FileName   string // 文件名
    FuncName   string // 函数名
    Key        string // SQL的唯一标识
    ChangeType string // "NEW", "MODIFIED", "DELETED", "UNCHANGED"
    OldSQL     string // 旧SQL内容（从diff中提取）
    NewSQL     string // 新SQL内容
}
```

### 2. Git diff内容解析
```go
// 获取文件的Git diff内容
func (g *GitDiffer) getFileDiffContent(fromCommit, toCommit, filePath string) (string, error) {
    cmd := fmt.Sprintf("git diff %s..%s -- %s", fromCommit, toCommit, filePath)
    output, err := easyutil.ExecScript(cmd)
    return output, err
}

// 解析diff内容为结构化数据
func (g *GitDiffer) parseDiffContent(diffContent string) []DiffLine {
    // 解析每一行的变更类型：+（新增）、-（删除）、 （上下文）
}
```

### 3. SQL关键词匹配检测
```go
// 通过SQL关键词匹配判断变更类型
func (g *GitDiffer) detectSQLChangeInDiff(result *base.ResultInfo, diffLines []DiffLine) string {
    // 提取SQL中的关键词
    sqlKeywords := g.extractSQLKeywords(result.SQL)

    // 检查关键词在diff中的出现情况
    hasAddition := false  // 出现在新增行(+)中
    hasDeletion := false  // 出现在删除行(-)中

    // 根据出现情况判断变更类型
    if hasAddition && hasDeletion {
        return "MODIFIED"  // 既有删除又有新增，说明是修改
    } else if hasAddition && !hasDeletion {
        return "NEW"       // 只有新增，说明是新增SQL
    } else if !hasAddition && hasDeletion {
        return "DELETED"   // 只有删除，说明是删除SQL
    } else {
        return "UNCHANGED" // 都没有，说明未变更
    }
}
```

### 4. SQL关键词提取
```go
// 从SQL中提取关键词用于匹配
func (g *GitDiffer) extractSQLKeywords(sql string) []string {
    var keywords []string

    // 提取SQL操作类型
    if strings.Contains(sql, "select") { keywords = append(keywords, "select") }
    if strings.Contains(sql, "insert") { keywords = append(keywords, "insert") }
    if strings.Contains(sql, "update") { keywords = append(keywords, "update") }
    if strings.Contains(sql, "delete") { keywords = append(keywords, "delete") }

    // 提取表名
    // 例如：从 "SELECT * FROM users" 中提取 "users"

    return keywords
}
```

### 4. 新增SQL diff处理
```go
if item.ChangeType == "NEW" || item.IsInChangedFile {
    diffList = append(diffList, &entity.SQLDiffInfo{
        UnicKey:    realKeyHash,
        Old:        nil, // 新增SQL没有旧版本
        New:        newEntityResult,
        IsNewSQL:   true,
        ChangeType: "NEW",
    })
}
```

## 使用方式

### 1. 运行SQL扫描
```bash
cd go-static-analysis/cmd/sqlscan
go run main.go
```

### 2. 查看diff结果
- 访问服务端的diff查看页面
- 新增SQL会显示为全部新增内容
- 修改SQL会显示具体的变更差异

### 3. 历史SQL查询API
```bash
# 查询指定commit的历史SQL记录
curl "http://10.11.97.54:80/sqldata/history?commit_id=abc123&repo=your-repo"

# 响应格式
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "sql": "SELECT * FROM users WHERE id = ?",
      "filename": "dao/user.go",
      "fn_name": "GetUser",
      "key": "user_query",
      "type": "easy",
      "database": "user_db",
      "instance": "mysql_instance"
    }
  ]
}
```

### 4. 新增SQL的diff展示
- 在diff页面中，新增SQL会以绿色高亮显示
- Extra字段中包含`{"is_new_sql": true, "change_type": "NEW"}`标记
- 支持与空内容的对比，清晰展示新增内容

## 配置说明

### Git diff配置
- 系统会自动获取上次扫描的commit ID
- 如果是首次扫描，会与第一个commit进行对比
- 支持检测未提交的变更

### 错误处理
- Git操作失败时不会中断扫描流程
- 会记录详细的错误日志
- 提供降级处理机制

## 测试

### 单元测试
```bash
cd go-static-analysis/passes/sqlscan
go test -v
```

### 功能测试
1. 创建新的SQL文件
2. 修改现有SQL语句
3. 运行扫描工具
4. 检查diff结果是否正确标记

## 注意事项

1. **性能影响**: Git操作可能影响扫描速度，建议在CI/CD环境中使用
2. **Git依赖**: 需要确保运行环境有Git命令且仓库状态正常
3. **首次扫描**: 首次扫描时可能会产生大量diff记录
4. **权限要求**: 需要有读取Git仓库的权限

## 技术实现细节

### 1. SQL唯一标识生成
```go
func (g *GitDiffer) generateSQLKey(result *base.ResultInfo) string {
    return fmt.Sprintf("%s_%s_%s", result.FileName, result.FuncName, result.Key)
}
```
- 通过文件名、函数名和Key组合生成唯一标识
- 确保同一个SQL在不同版本间能够准确匹配

### 2. 降级处理机制
```go
func (s *SQLScanner) setChangeInfoForResults() {
    sqlChanges, err := s.GitDiffer.GetSQLChanges(s.LastCommitID, s.CommitID, s.Results)
    if err != nil {
        // 如果SQL级别检测失败，降级到文件级别检测
        s.setChangeInfoByFileLevel()
        return
    }
    // 使用SQL级别的精确检测
}
```

### 3. 错误处理策略
- Git操作失败时不中断扫描流程
- API调用失败时使用降级方案
- 详细的错误日志记录

## 部署说明

### 1. 环境要求
- Go 1.16+
- Git 2.0+
- 网络访问权限（调用服务端API）

### 2. 配置文件
```yaml
# config.yaml
git:
  enabled: true
  cache_enabled: true
  cache_ttl: 3600
api:
  base_url: "http://10.11.97.54:80"
  timeout: 30
```

### 3. 部署步骤
1. 编译采集端程序
2. 配置服务端API地址
3. 确保Git仓库状态正常
4. 运行扫描程序

## 监控和日志

### 1. 关键指标
- SQL变更检测成功率
- API调用响应时间
- Git操作耗时
- 新增SQL检测数量

### 2. 日志级别
- INFO: 正常操作日志
- WARN: 降级处理和非关键错误
- ERROR: 严重错误，需要人工介入

## 后续优化建议

1. **性能优化**:
   - 添加Git操作结果缓存
   - 并行处理多个文件的变更检测
   - 优化API调用频率

2. **功能增强**:
   - 支持更多Git工作流（如merge request）
   - 添加SQL语义分析，识别功能等价的SQL变更
   - 支持自定义SQL变更规则

3. **用户体验**:
   - 提供Web界面配置Git diff参数
   - 支持SQL变更的可视化展示
   - 添加变更影响分析

4. **集成优化**:
   - 与CI/CD流水线深度集成
   - 支持多种代码托管平台
   - 提供Webhook接口

## 版本信息

- **版本**: v1.1.0
- **更新时间**: 2024-12-19
- **兼容性**: 向后兼容，不影响现有功能
- **主要贡献者**: SQL Diff Enhancement Team
